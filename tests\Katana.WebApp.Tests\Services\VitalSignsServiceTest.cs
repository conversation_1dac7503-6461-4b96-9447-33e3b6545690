﻿using Aoshield.Core.Entities.Models;
using Katana.Services.VitalSigns;
using Katana.Services.VitalSigns.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// VitalSign CRUD service
    /// </summary>
    public class VitalSignsServiceTest : IVitalSignsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddVitalSignDto> Add(AddVitalSignDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddVitalSignDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<VitalSignDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<VitalSignDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<VitalSignDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new VitalSignDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateVitalSignDto> Update(UpdateVitalSignDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateVitalSignDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
