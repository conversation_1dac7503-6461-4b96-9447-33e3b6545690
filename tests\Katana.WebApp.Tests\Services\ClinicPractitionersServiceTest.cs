﻿using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Services.ApiCall;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Services.ClinicPractitioners;
using Katana.Services.ClinicPractitioners.Models;
using Microsoft.AspNetCore.Http;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Clinic Practitioner Service Test
    /// </summary>
    public class ClinicPractitionersServiceTest : IClinicPractitionersService
    {
        ///<inheritdoc/>
        public IValidator<ClinicPractitioner> Validator => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<AddClinicPractitionerDto> Add(AddClinicPractitionerDto dto, CancellationToken cancellation = default)
            => await Task.Run(() => new AddClinicPractitionerDto());

        ///<inheritdoc/>
        public async Task<AddClinicPractitionerDto[]> AddPractitioners(AddClinicPractitionerDto[] dtos, CancellationToken cancellation = default)
            => await Task.Run(() => dtos);

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => 0);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<IPagedResults<ClinicPractitionerDto>> GetAsPagedResults(SieveModel query, CancellationToken cancellation = default)
            => await Task.Run(() => new PagedResults<ClinicPractitionerDto>([],
                default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<ClinicPractitionerDto> GetById(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id <= 0 ? null : new ClinicPractitionerDto() { Id = id });

        ///<inheritdoc/>
        public Task<int> MarkAsReadyForOnboarding(BaseUpdateDto[] dtos, CancellationToken cancellation = default)
            => Task.Run(() => dtos.Length);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => 0);

        ///<inheritdoc/>
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<UpdateClinicPractitionerDto> Update(UpdateClinicPractitionerDto dto, CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateClinicPractitionerDto());

        ///<inheritdoc/>
        public Task<BlobItemDto> UploadPatientsPanelFile(int id, IFormFile formFile, bool overrideExisting, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<DeferredRequestResponse> OnboardingFileBaseSynchronization(int[] ids, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ToggleResponseBackCp(ClinicPractitionerResponseBackDto dtos, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<CareplansToSendBackDto> GetCareplansToSendBack(int id, CancellationToken cancellation = default) => throw new NotImplementedException();
    }
}
