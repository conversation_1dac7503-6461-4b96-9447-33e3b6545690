using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Katana.Core.Entities;
using Katana.Services.Common.Models;
using Katana.Services.Faxes;
using Katana.Services.Faxes.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Faxes Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class FaxesController : ControllerBase
    {
        private readonly IFaxesService _faxesService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="crudService">CRUD service</param>
        public FaxesController(IFaxesService crudService) => _faxesService = crudService;

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        // GET: api/Faxes
        [HttpGet]
        //[EnableQuery]
        public async Task<IPagedResults<FaxListDto>> GetFaxes([FromQuery] SieveModel query) =>
            await _faxesService.GetAsPagedResults(query);

        /// <summary>
        /// Get entity by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<FaxDto>> GetFax(int id)
        {
            var dto = await _faxesService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateFaxDto>> PutFax(int id, UpdateFaxDto dto,
            CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _faxesService.Update(dto, cancellation: cancellation);


        /// <summary>
        /// Import Faxes by manual process
        /// </summary>
        /// <param name="name"></param>
        /// <param name="files"></param>
        /// <param name="clinicId"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<int>> PostFax(string name, int clinicId, [FromForm] IEnumerable<IFormFile> files, CancellationToken cancellation)
        {
            var importFaxDto = new ImportFaxDto
            {
                Name = name,
                Files = files,
                ClinicId = clinicId,
            };
            return Ok(await _faxesService.ImportFaxes(importFaxDto, cancellation: cancellation));
        }

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // DELETE: api/Faxes(3)
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeleteFax(int id,
            CancellationToken cancellation = default) =>
            await _faxesService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestoreFax(int id) =>
            await _faxesService.Restore(id, notify: false);

        /// <summary>
        /// Assign Fax to transcribersGroup
        /// </summary>
        /// <param name="dtoArr">Contains id of TrancriberGroup and id(s) of fax(es) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignGroup")]
        public async Task<int> AssignToGroup(AssignTranscribersGroupDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.AssignToTranscriberGroup(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Assign Fax to Transcriber Batch
        /// </summary>
        /// <param name="dtoArr">Contains id of Transcriber and id(s) of fax(es) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignTranscriber")]
        public async Task<int> AssignToTranscriberBatch(AssignTranscriberDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.AssignToTranscriber(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Send Back faxes
        /// </summary>
        /// <param name="dtoArr">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // PUT: api/Faxes1/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("SendBack")]
        public async Task<ActionResult<int>> SendBackFax(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.SendBackFax(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Mark fax as Processed
        /// </summary>
        /// <param name="dtoArr">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // PUT: api/Faxes1/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("ProcessFax")]
        public async Task<ActionResult<int>> ProcessFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.ProcessFax(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Automatic process Fax
        /// </summary>
        /// <param name="dtoArr">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // PUT: api/Faxes1/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AutomaticProcessFax")]
        public async Task<ActionResult<int>> AutomaticProcessFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.AutomaticProcessFax(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Add note to fax
        /// </summary>
        /// <param name="dto">Entity's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Notes")]
        public async Task<ActionResult<AddNoteDto>> AddNote(AddNoteDto dto)
        {
            var entity = await _faxesService.AddNote(dto);
            return StatusCode(StatusCodes.Status201Created, entity);
        }

        /// <summary>
        /// Get Note by Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("Notes/{id}")]
        public async Task<ActionResult<NoteDto>> GetNote(int id, CancellationToken cancellation)
        {
            var dto = await _faxesService.GetNoteById(id, cancellation);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">Fax's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        // GET: api/Faxes
        [HttpGet("{id}/Notes")]
        //[EnableQuery]
        public async Task<IPagedResults<NoteDto>> GetNotes(int id, [FromQuery] SieveModel query,
            CancellationToken cancellation) =>
            await _faxesService.GetNotesAsPagedResults(id, query, cancellation);

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // PUT: api/Faxes1/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Notes/{id}")]
        public async Task<ActionResult<UpdateNoteDto>> UpdateNote(int id, UpdateNoteDto dto,
            CancellationToken cancellation) => id != dto.Id
            ? BadRequest()
            : await _faxesService.UpdateNote(dto, cancellation);

        /// <summary>
        /// Delete Note
        /// </summary>
        /// <param name="id">Id of the note to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // DELETE: api/Faxes(3)
        [HttpDelete("Notes/{id}")]
        public async Task<ActionResult<int?>>
            DeleteNote(int id, CancellationToken cancellation) =>
            await _faxesService.DeleteNote(id, cancellation);

        /// <summary>
        /// Delete Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to delete</param>
        /// <returns></returns>
        [HttpDelete("DeleteNoteBatch")]
        public async Task<int[]> DeleteNoteBatch(int[] ids) =>
            await _faxesService.DeleteNoteBatch(ids);

        /// <summary>
        /// Restore Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to restore</param>
        /// <returns></returns>
        [HttpDelete("RestoreNoteBatch")]
        public async Task<ActionResult<int[]>> RestoreNoteBatch(int[] ids) =>
            await _faxesService.RestoreNoteBatch(ids);

        /// <summary>
        /// Get workflow statuses
        /// </summary>
        /// <param name="id">Fax's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Workflowstatus")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<FaxWorkflowStatus>>>
            GetWorkflowStatusLogs(int id, [FromQuery] SieveModel query,
                CancellationToken cancellation) =>
            await _faxesService.GetWorkflowStatusLogsAsPagedResults(id, query, cancellation);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">Faxex's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLogs(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _faxesService.GetStatusLogsAsPagedResults(id, query, cancellation);

        /// <summary>
        /// Get list of attachments
        /// </summary>
        /// <param name="id">Fax id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Attachments")]
        public async Task<IPagedResults<BlobItemDto>> GetListFiles(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _faxesService.ListFiles(id, query, cancellation);

        /// <summary>
        /// Upload attachment
        /// </summary>
        /// <param name="id">Fax id</param>
        /// <param name="files">List of files to upload</param>
        /// <param name="overrideExisting">Override file if existing</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>
        [HttpPost("{id}/Attachments")]
        public async Task<ActionResult> UploadFiles(int id, [FromForm] List<IFormFile> files, bool overrideExisting, CancellationToken cancellation)
        {

            await _faxesService.UploadFiles(id, files, overrideExisting, cancellation);

            return Ok((FileName: string.Join(", ", files.Select(f => f.FileName)), Result: "Ok"));
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("Attachments/{id}/uri")]
        public Task<ActionResult> GetFileSasUri(int id, [FromQuery] string fileName)
        {
            var result = _faxesService.GeneratePublicUri(id, fileName);
            return Task.FromResult<ActionResult>(Ok(new { Url = result.ToString() }));
        }

        /// <summary>
        /// Delete attachment
        /// </summary>
        /// <param name="dtos"> DeleteStorageFileDto []</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>BlobItemDto</returns>
        [HttpDelete("Attachments")]
        public async Task<ActionResult<BlobItemDto[]>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) =>
            Ok(await _faxesService.DeleteFiles(dtos, cancellation));

        /// <summary>
        /// Process incoming fax from sharepoint storage
        /// </summary>
        /// <returns></returns>
        [HttpPost("IncomingStructure")]
        public async Task<ActionResult<int>> CreateFileStructure(string folderName, [FromQuery] SieveModel query, CancellationToken cancellation)
        {
            try
            {
                var result = await _faxesService.CreateIncomingFileStructure(folderName, query, cancellation);
                return Ok("Clinics recipients created:" + result);
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// Reject fax
        /// </summary>
        /// <param name="dtoArr">Confirmation Note Dto</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Number of entities processed</returns>
        [HttpPut("Cancel")]
        public async Task<int> Cancel(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _faxesService.Cancel(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _faxesService.DisableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _faxesService.EnableBatch(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Get outgoing faxes
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of outgoing faxes</returns>
        [HttpGet("Outgoings")]
        public async Task<IPagedResults<FaxListDto>> GetOutgoings([FromQuery] SieveModel query,
            CancellationToken cancellation) =>
            await _faxesService.GetOutgoings(query, cancellation);

        /// <summary>
        /// Get incoming faxes
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of incoming faxes</returns>
        [HttpGet("Incomings")]
        public async Task<IPagedResults<FaxListDto>> GetIncomings([FromQuery] SieveModel query,
            CancellationToken cancellation) =>
            await _faxesService.GetIncomings(query, cancellation);

        /// <summary>
        /// Get incoming faxes
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of incoming faxes</returns>
        [HttpGet("MigrateFaxesRelease33")]
        public async Task<string> MigrateFaxesRelease33(CancellationToken cancellation) =>
            await _faxesService.MigrateFaxesRelease33(cancellation);
    }
}
