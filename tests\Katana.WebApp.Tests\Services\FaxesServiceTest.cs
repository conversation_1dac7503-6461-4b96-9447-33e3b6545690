﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Core.Validation;
using Katana.Core.Entities;
using Katana.Core.Enums;
using Katana.Services.Common.Models;
using Katana.Services.Faxes;
using Katana.Services.Faxes.Models;
using Microsoft.AspNetCore.Http;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Faxes CRUD service
    /// </summary>
    public class FaxesServiceTest : IFaxesService
    {
        ///<inheritdoc/>
        public FluentValidation.IValidator<Fax> Validator => throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public Task<int> ImportFaxes(ImportFaxDto importFaxDto, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<AddFaxDto> Add(AddFaxDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddFaxDto(), cancellation);

        ///<inheritdoc/>

        public Task CommonAdd(Fax fax, string rulesSet = null, BeforeSaveDelegate<Fax> beforeSave = null, AfterSaveDelegate<Fax> afterSave = null,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<FaxListDto>([], default, default,
                default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<FaxDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new FaxDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateFaxDto> Update(UpdateFaxDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateFaxDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddNoteDto> AddNote(AddNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddNoteDto());

        ///<inheritdoc/>
        public async Task<NoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new NoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<NoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<NoteDto>([], default, default, default, default,
                default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdateNoteDto> UpdateNote(UpdateNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Workflow Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<FaxWorkflowStatus>>>
            GetWorkflowStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<FaxWorkflowStatus>>(
                    [], default, default,
                    default, default, default, default, default, default));

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Fax> ConfigureSet(IQueryable<Fax> _, Type _1, ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Fax _, IList<Fax> _1, string _2, Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Operations

        ///<inheritdoc/>
        public async Task<int> AssignToTranscriberGroup(AssignTranscribersGroupDto[] dtoArr, CancellationToken cancellation = default) =>
            await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetFaxesByTranscribersGroup(int groupId,
            SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<FaxListDto>([], default, default,
                default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<int> AssignToTranscriber(AssignTranscriberDto[] dtoArr, CancellationToken cancellation = default) =>
            await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetFaxesByTranscriber(int transcriberId,
            SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<FaxListDto>([], default, default,
                default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<int> SendBackFax(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> ProcessFax(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<Fax> CreateOutgoingFax(int id, Clinic clinic, ProcessingType processingType, int? practitionerId, int? patientId, UploadRequestOrigin requestOrigin = UploadRequestOrigin.Automatic, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        #endregion

        #region Attachments

        ///<inheritdoc/>
        public async Task<IPagedResults<BlobItemDto>>
            ListFiles(int Id, SieveModel query, CancellationToken cancellation) => await Task.Run(
            () => new PagedResults<BlobItemDto>([], default, default, default,
                default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<BlobItemDto> UploadFile(int id, AddBlobItemDto blobItem,
            bool overrideExisting = true, CancellationToken cancellation = default) =>
            await Task.Run(() => new BlobItemDto() { Id = id });

        ///<inheritdoc/>
        public async Task<BlobItemDto> DownloadFile(int id, string fileName,
            CancellationToken cancellation) => await Task.Run(() => new BlobItemDto() { Id = id });

        ///<inheritdoc/>
        public async Task<BlobItemDto> DeleteFile(int id, string fileName,
            CancellationToken cancellation) => await Task.Run(() => new BlobItemDto() { Id = id });

        /// <inheritdoc />
        public Task<BlobItemDto> DeleteFile(DeleteStorageFileDto dto,
            CancellationToken cancellation = default) =>
            Task.Run(() => new BlobItemDto() { Id = dto.ParentId }, cancellation);

        /// <inheritdoc />
        public async Task<IList<BlobItemDto>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) => await Task.Run(() => dtos.Select(dto => new BlobItemDto() { Id = dto.ParentId }).ToList());

        // TODO: implement
        ///<inheritdoc/>
        public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> AutomaticProcessFax(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> SendFaxToClinic(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<ProcessIncomingFaxesResult> ProcessIncomingFaxes(string folderName, SieveModel query, CancellationToken cancellation) => Task.Run(() => new ProcessIncomingFaxesResult());

        ///<inheritdoc/>
        public Task<DeferredRequestResponse> ProcessCloudIncomingFaxes(CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<bool> ProcessCloudIncomingFax(string cloudId, int TotalPagesCount, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> ProcessIncomingFaxes(CancellationToken cancellation) => Task.Run(() => new DeferredRequestResponse());

        ///<inheritdoc/>
        public Task<int> CreateIncomingFileStructure(string folderName, SieveModel query, CancellationToken cancellation) => Task.Run(() => 1);

        ///<inheritdoc/>
        public Task<List<BlobItemDto>> UploadFiles(int id, List<IFormFile> files, bool overrideExisting, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int> Cancel(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation = default) => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task RollBackFaxToNewStatus(int faxId, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task RecognizeFax(int faxId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<string> GetUploadFaxPdfVersion(int faxId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<FaxListDto>> GetOutgoings(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<FaxListDto>> GetIncomings(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<string> MigrateFaxesRelease33(CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion
    }
}
