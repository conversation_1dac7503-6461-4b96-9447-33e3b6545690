import { BaseEntityCommonDto } from "../BaseEntityDto";

export interface TreatmentplanCommonDto extends BaseEntityCommonDto {
    // np: PractitionerCommonDto;
    // custodianGp: PractitionerCommonDto;
    // workflowStatus: TreatmentplanWorkflowStatus;
    // assignedUserId: number | null;
    // requestedByCustodianGP: boolean;
    // actions: EntityAction[];
    // condition: PatientHistoryItemCommonDto | null;
    conditionFreeText: string;
}
