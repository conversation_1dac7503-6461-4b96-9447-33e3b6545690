using Aoshield.Core.DataAccess.AzureFileStorage;
using AutoFixture;
using AutoMapper;
using FluentAssertions;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Services.Tests.Common;
using Katana.Services.UserInquiryForms;
using Katana.Tests.Common.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Sieve.Models;
using Xunit;

namespace Katana.Services.Tests.Services.UserInquiryForms;

/// <summary>
/// Init tests
/// </summary>
/// <remarks>
/// Ctor
/// </remarks>
/// <param name="configurationFixture"></param>
public class UsersFormServiceInitTests(ConfigurationFixture configurationFixture) : ServicesUnitTestConfiguration(
    configurationFixture)
{
    private readonly Fixture _fixture = new();

    /// <summary>
    /// Init tests
    /// </summary>
    [Fact]
    public async Task InitForms_Tests()
    {
        //Arrange
        var form1 = InitMasterForms();
        var questions = form1.Steps.First().Questions.ToArray();
        questions[1].DependencyQuestion = questions[0];
        questions[1].DependencyQuestionValues = questions[0].QuestionValues;
        questions[2].DependencyQuestion = questions[1];
        questions[2].DependencyQuestionValues = questions[1].QuestionValues;
        questions[3].DependencyQuestion = questions[0];
        questions[3].DependencyQuestionValues = questions[0].QuestionValues;
        await Krudder.AddBatch([form1], null);

        var formIds = new[] { form1.Id };

        var userId = Krudder.Set<User>().Select(x => x.Id).First();
        var uut = new UserFormService(Krudder,
            new Mock<ILogger<UserFormService>>().Object,
            new Mock<IMapper>().Object,
            new Mock<IAzureFileStorageService>().Object,
            new Mock<IValidator<UserInquiryForm>>().Object,
            new Mock<IValidator<UserInquiryFormQuestion>>().Object,
            new Mock<IServiceProvider>().Object,
            new Mock<IOptions<SieveOptions>>().Object
        );
        //Act
        await uut.InitForms(formIds,
            [userId],
            CancellationToken.None);
        //Assert
        var userForms = await Krudder.Set<UserInquiryForm>()
            .Where(x => formIds.Contains(x.MasterInquiryFormId))
            .Include(x => x.Steps)
            .ThenInclude(x => x.Questions)
            .ToArrayAsync();

        var forms = new[] { form1 };
        userForms.Length.Should().Be(forms.Length);


        foreach (var inquiryForm in forms)
        {
            var userFrom = userForms.First(x => x.MasterInquiryFormId == inquiryForm.Id);
            userFrom.Name.Should().Be(inquiryForm.Name);
            userFrom.Description.Should().Be(inquiryForm.Description);
            userFrom.Type.Should().Be(inquiryForm.Type);
            userFrom.Validator.Should().Be(inquiryForm.Validator);
            userFrom.UserType.Should().Be(inquiryForm.UserType);
            userFrom.Type.Should().Be(inquiryForm.Type);
            userFrom.UserId.Should().Be(userId);


            var from1Steps = inquiryForm.Steps.ToArray();

            foreach (var inquiryFormStep in from1Steps)
            {
                var userFromStep = userFrom.Steps.First(x =>
                    x.MasterInquiryFormStepId == inquiryFormStep.Id);

                userFromStep.Header.Should().Be(inquiryFormStep.Header);
                userFromStep.Description.Should().Be(inquiryFormStep.Description);
                userFromStep.ImageUrl.Should().Be(inquiryFormStep.ImageUrl);
                userFromStep.StepIndex.Should().Be(inquiryFormStep.StepIndex);

                var fromStepIQuestions = inquiryFormStep.Questions.ToArray();

                foreach (var inquiryFormQuestion in fromStepIQuestions)
                {
                    var userFromStepQuestion =
                        userFromStep.Questions.First(x =>
                            x.MasterQuestionId == inquiryFormQuestion.Id);

                    userFromStepQuestion.Question.Should().Be(inquiryFormQuestion.Question);
                    userFromStepQuestion.QuestionType.Should().Be(inquiryFormQuestion.QuestionType);
                    userFromStepQuestion.Required.Should().Be(inquiryFormQuestion.Required);
                    userFromStepQuestion.QuestionValues.Should()
                        .Be(inquiryFormQuestion.QuestionValues);
                    userFromStepQuestion.Entity.Should().Be(inquiryFormQuestion.Entity);
                    userFromStepQuestion.EntityId.Should().Be(inquiryFormQuestion.EntityId);
                    userFromStepQuestion.DependencyQuestionValues.Should().Be(inquiryFormQuestion.DependencyQuestionValues);

                    if (inquiryFormQuestion.DependencyQuestionId is not null)
                    {
                        var related = userFromStep.Questions.FirstOrDefault(x =>
                            x.MasterQuestionId == inquiryFormQuestion.DependencyQuestionId);
                        if (related is not null)
                        {
                            userFromStepQuestion.DependencyQuestionId.Should().Be(related.Id);
                        }
                    }
                }
            }
        }
    }

    private InquiryForm InitMasterForms()
    {
        var masterForm1 = new InquiryForm()
        {
            Name = _fixture.Create<string>(),
            Description = _fixture.Create<string>(),
            Type = InquiryFormType.IntakeForm,
            UserType = UserType.Patient,
            Steps =
            [
                new InquiryFormStep()
                {
                    Header = _fixture.Create<string>(),
                    Description = _fixture.Create<string>(),
                    ImageUrl = _fixture.Create<string>(),
                    Questions =
                    [
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Number,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.String,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Boolean,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Boolean,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Boolean,
                            QuestionValues = _fixture.Create<string>(),
                        }
                    ]
                }
            ]
        };

        return masterForm1;
    }
}
