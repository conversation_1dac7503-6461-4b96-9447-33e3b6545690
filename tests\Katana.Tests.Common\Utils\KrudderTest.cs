﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.Entities;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sieve.Models;
using System.Reflection;

namespace Katana.Tests.Common.Utils
{
    /// <summary>
    /// Krudder Mock
    /// </summary>
    /// <remarks>
    /// Main constructor
    /// </remarks>
    /// <param name="katanaDataContext">Katana data context</param>
    /// <param name="mapper">Mapper</param>
    /// <param name="sieveProcessor"></param>
    /// <param name="options"></param>
    /// <param name="claimsPrincipalContext"></param>
    /// <param name="serviceScopeFactory"></param>
    public class KrudderTest<TUser>(
        IDataContext katanaDataContext,
        IMapper mapper,
        ISieveProcessor sieveProcessor,
        IOptions<SieveOptions> options,
        IClaimsPrincipalContext<TUser> claimsPrincipalContext,
        IServiceScopeFactory serviceScopeFactory) : IKrudder<TUser> where TUser : User<TUser>, IUser
    {
        private readonly IDataContext _katanaDataContext = katanaDataContext;

        /// <inheritdoc/>
        public ILogger Logger => throw new NotImplementedException();

        /// <inheritdoc/>
        public ISieveProcessor SieveProcessor { get; } = sieveProcessor;

        private readonly IOptions<SieveOptions> _options = options;

        /// <inheritdoc/>
        public IMapper Mapper { get; } = mapper;

        private readonly IClaimsPrincipalContext<TUser> _claimsPrincipalContext = claimsPrincipalContext;
        private readonly IServiceScopeFactory _serviceScopeFactory = serviceScopeFactory;

        #region Create

        /// <inheritdoc/>
        public async Task<TAddDto> Add<TAddDto, T>(TAddDto dto, IValidator<T> validator, Func<TAddDto, T> dtoInToEntity = null, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, bool includeRulesNotInRuleSet = true, CancellationToken cancellation = default)
            where TAddDto : BaseAddDto
            where T : BaseEntity
        {
            dtoInToEntity ??= Mapper.Map<T>;
            var entity = await Add(Map(dtoInToEntity, dto, GlobalActions.Add), validator, validationContextData, beforeSave, afterSave, rulesSet, includeRulesNotInRuleSet, cancellation);
            dto.Id = entity?.Id;

            return dto;
        }

        /// <inheritdoc/>
        public async Task<T> Add<T>(T entity, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null, string rulesSet = null, bool includeRulesNotInRuleSet = true, CancellationToken cancellation = default) where T : BaseEntity
        {
            await (beforeSave is not null ? beforeSave.Invoke(entity, cancellation) : Task.CompletedTask);
            var currentUserId = await GetCurrentUserId(cancellation);
            await AddStatusLog(entity, currentUserId, cancellation);
            await _katanaDataContext.AddAsync(entity, cancellation);

            if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
            {
                if (afterSave is not null)
                {
                    await afterSave.Invoke(entity, cancellation);
                    await _katanaDataContext.SaveChangesAsync(cancellation);
                }
                return entity;
            }

            return null;
        }

        /// <inheritdoc/>
        public async Task<TAddDto[]> AddBatch<TAddDto, T>(TAddDto[] dtos, IValidator<T> validator, Func<TAddDto, T> dtoInToEntity = null, Func<T, TAddDto> entityToTDtoOut = null, IDictionary<string, object> validationContextData = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            string rulesSet = null, CancellationToken cancellation = default)
            where TAddDto : BaseAddDto
            where T : BaseEntity
        {
            dtoInToEntity ??= Mapper.Map<T>;
            var entities = dtos.Select(d => Map(dtoInToEntity, d, GlobalActions.Add)).ToList();
            entities = await AddBatch(entities, validator, validationContextData, beforeSave, afterSave, afterBatchSave, rulesSet, cancellation);

            for (var i = 0; i < entities.Count; i++)
            {
                dtos[i].Id = entities[i].Id;
            }
            return dtos;
        }

        /// <inheritdoc/>
        public async Task<List<T>> AddBatch<T>(List<T> entities, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);
                await _katanaDataContext.AddAsync(entity, cancellation);
                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);

                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<List<object>> AddBatch(List<object> entities, BeforeSaveObjectBatchDelegate beforeSave = null, AfterSaveObjectBatchDelegate afterSave = null, AfterSaveAllObjectBatchDelegate afterBatchSave = null, string rulesSet = null, CancellationToken cancellation = default)
        {
            var savedEntities = new List<object>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);
                await _katanaDataContext.AddAsync(entity, cancellation);
                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);

                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        #endregion

        #region Update

        ///<inheritdoc/>
        private async Task<int> Update<T>(int id, IValidator<T> validator, Action<T> mapFn, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            T entity;
            try
            {
                var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
                if (configureSet != null)
                {
                    set = configureSet(set) ?? set;
                }

                entity = await set.FirstAsync(o => o.Id == id, cancellation);
            }
            catch (Exception ex)
            {
                var msg = $"Error updating {typeof(T).Name}. Object not found. Id: '{id}'";
                throw new CrudServiceException(msg, ex);
            }

            mapFn?.Invoke(entity);
            await Update(entity, validator, validationContextData, beforeSave, afterSave, rulesSet, true, cancellation);

            return id;
        }

        /// <inheritdoc/>
        public async Task<int> Update<T>(int id, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, string rulesSet = null,
            bool includeRulesNotInRuleSet = true, CancellationToken cancellation = default) where T : BaseEntity
            => await Update(id, validator, null, validationContextData, beforeSave, afterSave, configureSet, rulesSet, cancellation);

        /// <inheritdoc/>
        public async Task<TDto> Update<TDto, T>(TDto dto, IValidator<T> validator, Func<TDto, T, T> mapper = null, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, string rulesSet = null, bool includeRulesNotInRuleSet = true, CancellationToken cancellation = default)
            where TDto : BaseUpdateDto
            where T : BaseEntity
        {
            mapper ??= (dto, entity) => Mapper.Map(dto, entity);
            await Update(dto.Id, validator, (entity) => Map(dto, mapper, entity, GlobalActions.Update), validationContextData, beforeSave, afterSave, configureSet, rulesSet, cancellation);
            return dto;
        }

        /// <inheritdoc/>
        public async Task<T> Update<T>(T entity, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, bool includeRulesNotInRuleSet = true, CancellationToken cancellation = default) where T : BaseEntity
        {
            await (beforeSave is not null ? beforeSave.Invoke(entity, cancellation) : Task.CompletedTask);
            if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
            {
                if (afterSave is not null)
                {
                    await afterSave.Invoke(entity, cancellation);
                    //we save again here any possible change that happened in the afterSave
                    await _katanaDataContext.SaveChangesAsync(cancellation);
                }
                return entity;
            }
            return null;
        }

        private async Task<List<T>> UpdateBatch<T>(IEnumerable<T> entities, IValidator<T> _, string _1, BeforeSaveWithRulesDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null,
            CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            var i = -1;
            foreach (var entity in entities)
            {
                i++;
                await (beforeSave != null ? beforeSave.Invoke(entity, i, validationContextData ??= new Dictionary<string, object>(), cancellation) : Task.CompletedTask);
                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<int> UpdateBatch<TDtoIn, T>(TDtoIn[] dtoInArr, IValidator<T> validator, string rulesSet = null, AfterQueryBatchDelegate<T> afterQuery = null, Func<TDtoIn, T, T> mapper = null, IDictionary<string, object> validationContextData = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveWithRulesDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, CancellationToken cancellation = default)
            where TDtoIn : BaseUpdateDto
            where T : BaseEntity
        {
            List<T> entities;
            var dtoIds = dtoInArr.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(dtoInArr.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }

            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            entities = await set.Where(o => dtoIds.Contains(o.Id)).OrderBy(o => o.Id).ToListAsync(cancellation);
            if (!entities.Count.Equals(dtoIds.Length))
            {
                throw new CrudServiceException("Failed to load all entities.");
            }

            if (afterQuery is not null)
            {
                await afterQuery.Invoke(entities, cancellation);
            }
            //entities and dtoInArr are ordered by id so we have parallel arrays
            dtoInArr = [.. dtoInArr.OrderBy(dto => dto.Id)];
            mapper ??= (dto, entity) => Mapper.Map(dto, entity);
            for (var i = 0; i < entities.Count; i++)
            {
                Map(dtoInArr[i], mapper, entities[i], GlobalActions.Update);
            }

            return (await UpdateBatch(entities, validator, rulesSet, beforeSave, afterSave, afterBatchSave, validationContextData, cancellation: cancellation)).Count;
        }

        /// <inheritdoc/>
        public async Task<int> UpdateBatch<T>(int[] ids, IValidator<T> validator, string rulesSet, AfterQueryBatchDelegate<T> afterQuery = null, IDictionary<string, object> validationContextData = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveWithRulesDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            ids = ids.Distinct().ToArray();
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(o => ids.Contains(o.Id)).ToListAsync(cancellation);
            if (!entities.Count.Equals(ids.Length))
            {
                throw new CrudServiceException("Failed to load all entities.");
            }
            if (afterQuery is not null)
            {
                await afterQuery.Invoke(entities, cancellation);
            }

            return (await UpdateBatch(entities, validator, rulesSet, beforeSave, afterSave, afterBatchSave, validationContextData, cancellation: cancellation)).Count;
        }

        /// <inheritdoc/>
        public async Task<List<T>> UpdateBatch<T>(IEnumerable<T> entities, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            var i = -1;
            foreach (var entity in entities)
            {
                i++;
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);
                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<int> UpdateBatch<T>(IValidator<T> validator, string rulesSet, Func<IQueryable<T>, IQueryable<T>> configureSet, AfterQueryBatchDelegate<T> afterQuery = null, IDictionary<string, object> validationContextData = null, BeforeSaveWithRulesDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            set = configureSet(set);
            if (set is null)
            {
                return 0;
            }

            var entities = await set.ToListAsync(cancellation);
            if (afterQuery is not null)
            {
                await afterQuery.Invoke(entities, cancellation);
            }

            return (await UpdateBatch(entities, validator, rulesSet, beforeSave, afterSave, afterBatchSave, validationContextData, cancellation: cancellation)).Count;
        }

        #endregion

        #region PickUp/Release

        /// <inheritdoc/>
        public Task<int?> PickUp<T>(int id, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        /// <inheritdoc/>
        public async Task<List<int>> PickUpBatch<T>(IEnumerable<BaseUpdateDto> baseUpdateDtos, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var ids = baseUpdateDtos.Select(dto => dto.Id).ToList();
            await PickUpBatch(ids, validator, beforeSave, configureSet, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);
            return ids;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<T>> PickUpBatch<T>(IEnumerable<int> ids, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            ids = ids.Distinct().ToArray();
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            var entities = await set.Where(x => ids.Contains(x.Id)).ToListAsync(cancellation);
            if (!entities.Count.Equals(ids.Count()))
            {
                var msg = $"Error locking {typeof(T).Name} batch object.The number of entities to pick up does not match.";
                throw new CrudServiceException(msg);
            }

            return await PickUpBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);
        }

        /// <inheritdoc/>
        public async Task<List<T>> PickUpBatch<T>(List<T> entities, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var currentUser = await GetCurrentUser(cancellation);
            var isAnyLocked = entities.Any(e => e.LockUserId != null);
            if (isAnyLocked)
            {
                var msg = $"Error locking {typeof(T).Name} batch object. One o more entities are locked.";
                throw new CrudServiceException(msg);
            }

            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];

                entity.LockUserId = currentUser.Id;
                entity.LockUserName = currentUser.DisplayName;
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);

                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public Task<int?> Release<T>(int id, IValidator<T> validator, IDictionary<string, object> validationContextData = null, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<List<int>> ReleaseBatch<T>(IEnumerable<BaseUpdateDto> baseUpdateDtos, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IEnumerable<T>> ReleaseBatch<T>(IEnumerable<int> ids, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<List<T>> ReleaseBatch<T>(List<T> entities, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        #endregion

        #region Delete/Restore

        /// <inheritdoc/>
        public async Task<int?> Delete<T, TDtoOut>(int id, IValidator<T> validator, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto
        {
            var deletedId = await Delete(id, validator, beforeSave, afterSave, validationContextData, rulesSet, cancellation);
            if (deletedId is null)
            {
                var msg = $"The entity Id: {id} does not exist";
                throw new CrudServiceException(msg);
            }

            return deletedId;
        }

        /// <inheritdoc/>
        public async Task<int?> Delete<T>(int id, IValidator<T> validator, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var set = Set<T>().AsQueryable();

            var entity = await set.FirstOrDefaultAsync(o => o.Id == id, cancellation);
            if (entity is null)
            {
                return null;
            }
            entity.SoftDeleteLevel = 1;
            if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
            {
                if (afterSave is not null)
                {
                    await afterSave.Invoke(entity, cancellation);
                    //we save again here any possible change that happened in the afterSave
                    await _katanaDataContext.SaveChangesAsync(cancellation);
                }
                return entity.Id;
            }
            return null;
        }

        ///<inheritdoc/>
        private async Task<List<T>> DeleteBatch<T>(List<T> entities, IValidator<T> _, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> _1 = null, string _2 = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];

                // Soft deleting entity
                entity.SoftDeleteLevel = 1;
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);
                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<int[]> DeleteBatch<T>(int[] ids, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            ids = ids.Distinct().ToArray();
            var set = Set<T>().AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(x => ids.Contains(x.Id)).ToListAsync(cancellation);
            if (!entities.Count.Equals(ids.Length))
            {
                var msg = $"Error deleting {typeof(T).Name} batch object.The number of entities to delete does not match.";
                throw new CrudServiceException(msg);
            }

            entities = await DeleteBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);

            return entities.Select(e => e.Id).ToArray();
        }

        /// <inheritdoc/>
        public async Task<int[]> DeleteBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = confirmationNotes.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(confirmationNotes.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>().AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            var entities = await set.Where(x => dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);
            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error deleting {typeof(T).Name} batch object.The number of entities to delete does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            confirmationNotes = [.. confirmationNotes.OrderBy(dto => dto.Id)];

            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].DeleteNotes = confirmationNotes[i]?.Note;
            }

            entities = await DeleteBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);

            return entities.Select(e => e.Id).ToArray();
        }

        /// <inheritdoc/>
        public async Task<int?> Restore<T, TDtoOut>(int id, IValidator<T> validator, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto
        {
            var restoredId = await Restore(id, validator, beforeSave, afterSave, rulesSet, cancellation);
            if (restoredId is null)
            {
                var msg = $"The entity Id: {id} does not exist";
                throw new CrudServiceException(msg);
            }

            return restoredId;
        }

        /// <inheritdoc/>
        public async Task<int?> Restore<T>(int id, IValidator<T> validator, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var set = Set<T>(EntityActiveStatus.Any);
            var entity = await set.Where(o => o.Id == id && o.SoftDeleteLevel.Equals(1)).FirstOrDefaultAsync(cancellation);
            if (entity is null)
            {
                return null;
            }
            entity.SoftDeleteLevel = 0;
            await (beforeSave is not null ? beforeSave.Invoke(entity, cancellation) : Task.CompletedTask);
            if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
            {
                if (afterSave is not null)
                {
                    await afterSave.Invoke(entity, cancellation);
                    //we save again here any possible change that happened in the afterSave
                    await _katanaDataContext.SaveChangesAsync(cancellation);
                }
                return entity.Id;
            }
            return null;
        }

        ///<inheritdoc/>
        private async Task<List<T>> RestoreBatch<T>(
            List<T> entities,
            IValidator<T> _,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> _1 = null,
            string _2 = null,
            CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];

                // Restore deleted entity
                entity.SoftDeleteLevel = 0;
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);

                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<int[]> RestoreBatch<T>(int[] ids, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            ids = ids.Distinct().ToArray();
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(x => x.SoftDeleteLevel.Equals(1) && ids.Contains(x.Id)).ToListAsync(cancellation);
            if (!entities.Count.Equals(ids.Length))
            {
                var msg =
                    $"Error restoring {typeof(T).Name} batch object. The number of entities to restore does not match.";
                throw new CrudServiceException(msg);
            }
            entities = await RestoreBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);
            return entities.Select(e => e.Id).ToArray();
        }

        /// <inheritdoc/>
        public async Task<int[]> RestoreBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator, BeforeSaveBatchDelegate<T> beforeSave = null, Func<IQueryable<T>, IQueryable<T>> configureSet = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = confirmationNotes.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(confirmationNotes.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(x => x.SoftDeleteLevel.Equals(1) && dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);
            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error restoring {typeof(T).Name} batch object. The number of entities to restore does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            confirmationNotes = [.. confirmationNotes.OrderBy(dto => dto.Id)];

            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].DeleteNotes = confirmationNotes[i]?.Note;
            }

            entities = await RestoreBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, cancellation);

            return entities.Select(e => e.Id).ToArray();
        }

        #endregion

        #region Enable/Disable

        ///<inheritdoc/>
        private async Task<List<T>> DisableBatch<T>(
            List<T> entities,
            IValidator<T> _,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> _1 = null,
            string _2 = null,
            int? authorId = null,
            CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];

                entity.Status = Status.Disabled;
                await AddStatusLog(entity, authorId, cancellation);
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);

                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public Task CheckCanAction<T>(string rulesSet, T entity, IDictionary<string, object> contextData,
            CancellationToken cancellation) where T : BaseEntity =>
            throw new NotImplementedException();

        /// <inheritdoc/>
        public async Task<int[]> DisableBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = confirmationNotes.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(confirmationNotes.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(x => dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);
            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error restoring {typeof(T).Name} batch object. The number of entities to disable does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            confirmationNotes = [.. confirmationNotes.OrderBy(dto => dto.Id)];
            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].StatusNotes = confirmationNotes[i]?.Note;
            }
            var currentUserId = await GetCurrentUserId(cancellation);
            entities = await DisableBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, currentUserId, cancellation);
            return entities.Select(e => e.Id).ToArray();
        }

        /// <inheritdoc/>
        public async Task<int[]> DisableBatch<T>(ChangeActiveStatusWithReasonDto[] dtos, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = dtos.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(dtos.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }
            var entities = await set.Where(x => dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);
            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error restoring {typeof(T).Name} batch object. The number of entities to disable does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            dtos = [.. dtos.OrderBy(dto => dto.Id)];
            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].StatusNotes = dtos[i]?.Note;
                var reasonIdProperty = typeof(T).GetProperty(nameof(IReasonableStatusLogs<BaseStatusLogWithReasonEntity<Status, TUser, BaseStatusLogReasonEntity>, BaseStatusLogReasonEntity, TUser>.ReasonId));
                reasonIdProperty?.SetValue(entities[i], dtos[i]?.ReasonId);
            }
            var currentUserId = await GetCurrentUserId(cancellation);
            entities = await DisableBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, currentUserId, cancellation);
            return entities.Select(e => e.Id).ToArray();
        }

        ///<inheritdoc/>
        private async Task<List<T>> EnableBatch<T>(
            List<T> entities,
            IValidator<T> _,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> _1 = null,
            string _2 = null,
            int? authorId = null,
            CancellationToken cancellation = default) where T : BaseEntity
        {
            var savedEntities = new List<T>();
            for (var i = 0; i < entities.Count; i++)
            {
                var entity = entities[i];

                entity.Status = Status.Enabled;
                await AddStatusLog(entity, authorId, cancellation);
                await (beforeSave != null ? beforeSave.Invoke(entity, i, cancellation) : Task.CompletedTask);

                if (await _katanaDataContext.SaveChangesAsync(cancellation) > 0)
                {
                    if (afterSave is not null)
                    {
                        await afterSave.Invoke(entity, i, cancellation);
                        //we save again here any possible change that happened in the afterSave
                        await _katanaDataContext.SaveChangesAsync(cancellation);
                    }
                    savedEntities.Add(entity);
                }
            }
            if (afterBatchSave is not null)
            {
                await afterBatchSave.Invoke(savedEntities, cancellation);
                //we save again here any possible change that happened in the afterBatchSave
                await _katanaDataContext.SaveChangesAsync(cancellation);
            }
            return savedEntities;
        }

        /// <inheritdoc/>
        public async Task<int[]> EnableBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = confirmationNotes.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(confirmationNotes.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            var entities = await set.Where(x => dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);

            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error enabling {typeof(T).Name} batch object. The number of entities to enable does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            confirmationNotes = [.. confirmationNotes.OrderBy(dto => dto.Id)];
            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].StatusNotes = confirmationNotes[i]?.Note;
            }
            var currentUserId = await GetCurrentUserId(cancellation);
            entities = await EnableBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, currentUserId, cancellation);
            return entities.Select(e => e.Id).ToArray();
        }

        /// <inheritdoc/>
        public async Task<int[]> EnableBatch<T>(ChangeActiveStatusWithReasonDto[] dtos, IValidator<T> validator, Func<IQueryable<T>, IQueryable<T>> configureSet = null, BeforeSaveBatchDelegate<T> beforeSave = null, AfterSaveBatchDelegate<T> afterSave = null, AfterSaveAllBatchDelegate<T> afterBatchSave = null, IDictionary<string, object> validationContextData = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity
        {
            var dtoIds = dtos.Select(o => o.Id).Distinct().ToArray();
            if (!dtoIds.Length.Equals(dtos.Length))
            {
                throw new CrudServiceException("Duplicated entities.");
            }
            var set = Set<T>(EntityActiveStatus.Any).AsQueryable();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            var entities = await set.Where(x => dtoIds.Contains(x.Id)).OrderBy(e => e.Id).ToListAsync(cancellation);

            if (!entities.Count.Equals(dtoIds.Length))
            {
                var msg = $"Error enabling {typeof(T).Name} batch object. The number of entities to enable does not match.";
                throw new CrudServiceException(msg);
            }

            //entities and dtoInArr are ordered by id so we have parallel arrays
            dtos = [.. dtos.OrderBy(dto => dto.Id)];
            for (var i = 0; i < entities.Count; i++)
            {
                entities[i].StatusNotes = dtos[i]?.Note;
                var reasonIdProperty = typeof(T).GetProperty(nameof(IReasonableStatusLogs<BaseStatusLogWithReasonEntity<Status, TUser, BaseStatusLogReasonEntity>, BaseStatusLogReasonEntity, TUser>.ReasonId));
                reasonIdProperty?.SetValue(entities[i], dtos[i]?.ReasonId);
            }
            var currentUserId = await GetCurrentUserId(cancellation);
            entities = await EnableBatch(entities, validator, beforeSave, afterSave, afterBatchSave, validationContextData, rulesSet, currentUserId, cancellation);
            return entities.Select(e => e.Id).ToArray();
        }

        #endregion

        #region Read

        /// <inheritdoc/>
        public async Task<IPagedResults<TDtoOut>> GetAsPagedResults<T, TDtoOut>(SieveModel query, Func<IQueryable<T>, IQueryable<T>> configureSet = null, Func<IQueryable<T>, IQueryable<TDtoOut>> projectTo = null, EntityActiveStatus entityActiveStatus = EntityActiveStatus.Any, CancellationToken cancellation = default)
            where T : BaseEntity where TDtoOut : BaseEntityDto
        {
            var set = Set<T>(entityActiveStatus).AsNoTracking();

            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            var setDto = projectTo != null
                ? projectTo(set)
                : set.ProjectTo<TDtoOut>(Mapper.ConfigurationProvider);

            return await SieveProcessor.GetPagedResultsAsync(query, setDto, _options, cancellation: cancellation);
        }

        /// <inheritdoc/>
        public async Task<IPagedResults<T>> GetAsPagedResults<T>(SieveModel query, Func<IQueryable<T>, IQueryable<T>> configureSet = null, EntityActiveStatus entityActiveStatus = EntityActiveStatus.Any, CancellationToken cancellation = default) where T : BaseEntity
        {
            var set = Set<T>(entityActiveStatus).AsNoTracking();
            if (configureSet != null)
            {
                set = configureSet(set) ?? set;
            }

            return await SieveProcessor.GetPagedResultsAsync(query, set, _options, cancellation: cancellation);
        }

        /// <inheritdoc/>
        public async Task<TDtoOut> GetById<T, TDtoOut>(int id, Func<IQueryable<T>, IQueryable<T>> configureSet = null, Func<IQueryable<T>, IQueryable<TDtoOut>> projectTo = null, bool track = false, CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto
        {
            try
            {
                var startTime = DateTime.UtcNow;
                var set = Set<T>(EntityActiveStatus.Any).Where(e => e.Id == id);

                if (!track)
                {
                    set = set.AsNoTracking();
                }

                if (configureSet != null)
                {
                    set = configureSet(set) ?? set;
                }

                var result = projectTo != null
                    ? await projectTo(set).FirstOrDefaultAsync(cancellation)
                    : await set.ProjectTo<TDtoOut>(Mapper.ConfigurationProvider).FirstOrDefaultAsync(cancellation);
                return result;
            }
            catch (Exception ex)
            {
                var msg = $"Error getting {typeof(T).Name} object. Id: '{id}'.";
                throw new CrudServiceException(msg, ex);
            }
        }

        /// <inheritdoc/>
        public async Task<T> GetById<T>(int id, Func<IQueryable<T>, IQueryable<T>> configureSet = null, bool track = false, CancellationToken cancellation = default) where T : BaseEntity
        {
            try
            {
                var set = Set<T>(EntityActiveStatus.Any).Where(e => e.Id == id);

                if (!track)
                {
                    set = set.AsNoTracking();
                }

                if (configureSet != null)
                {
                    set = configureSet(set) ?? set;
                }

                return await set.FirstOrDefaultAsync(cancellation);
            }
            catch (Exception ex)
            {
                var msg = $"Error getting {typeof(T).Name} object. Id: '{id}'.";
                throw new CrudServiceException(msg, ex);
            }
        }

        #endregion

        #region DataContext

        /// <inheritdoc/>
        public void ClearTracker() => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int> ExecuteInTransaction(Func<CancellationToken, Task> actionExecute, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int> SaveChangesAsync(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public IQueryable<TEntity> Set<TEntity>(EntityActiveStatus activeStatus = EntityActiveStatus.Active, string customActiveFilterMethod = null, bool includedDisabled = true) where TEntity : class
            => _katanaDataContext.Set<TEntity>(activeStatus, customActiveFilterMethod);

        #endregion

        #region User

        /// <inheritdoc/>
        public async Task<int> GetCurrentUserId(CancellationToken cancellation = default) => (await GetCurrentUser(cancellation)).Id;

        ///<inheritdoc/>
        public async Task<string> GetCurrentUserObjectId(CancellationToken cancellation = default) => (await GetCurrentUser(cancellation))?.ObjectId;

        /// <inheritdoc/>
        public void InitCurrentUser() => _claimsPrincipalContext.InitCurrentUser();

        /// <inheritdoc/>
        public async Task<TUser> GetCurrentUser(CancellationToken cancellation) => await _claimsPrincipalContext.GetCurrentUser(_katanaDataContext, cancellation);

        /// <inheritdoc/>
        public async Task<IServiceScope> GetUserScope(int userId, CancellationToken cancellation = default)
        {
            var scope = _serviceScopeFactory.CreateScope();
            var claimsPrincipalContext = scope.ServiceProvider.GetRequiredService<IClaimsPrincipalContext<TUser>>();
            var dataContext = scope.ServiceProvider.GetRequiredService<IDataContext>();
            await claimsPrincipalContext.GetCurrentUser(dataContext, userId, null, cancellation);

            return scope;
        }

        /// <inheritdoc/>
        public async Task<IServiceScope> GetUserScope(string objectId, CancellationToken cancellation = default)
        {
            var scope = _serviceScopeFactory.CreateScope();
            var claimsPrincipalContext = scope.ServiceProvider.GetRequiredService<IClaimsPrincipalContext<TUser>>();
            var dataContext = scope.ServiceProvider.GetRequiredService<IDataContext>();
            await claimsPrincipalContext.GetCurrentUser(dataContext, objectId, null, cancellation);

            return scope;
        }

        /// <inheritdoc/>
        public async Task<Microsoft.Graph.Models.User> GetCurrentUserGraphInfo(CancellationToken cancellation) => await _claimsPrincipalContext.GetCurrentUserGraphInfo(_katanaDataContext, cancellation);

        /// <inheritdoc/>
        public async Task<(TUser, string)> GetCurrentUserWithPhoto(CancellationToken cancellation) => await _claimsPrincipalContext.GetCurrentUserWithPhoto(_katanaDataContext, cancellation);

        ///<inheritdoc/>
        public async Task<Impersonation<TUser>> GetActiveImpersonation(CancellationToken cancellation = default) => await _claimsPrincipalContext.GetActiveImpersonation(_katanaDataContext, cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<IEnumerable<string>> GetCurrentUserGroups(CancellationToken cancellation = default) =>
            await _claimsPrincipalContext.GetCurrentUserGroups(_katanaDataContext, cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<IEnumerable<string>> GetCurrentUserGroupsNames(CancellationToken cancellation = default) =>
            await _claimsPrincipalContext.GetCurrentUserGroupsNames(_katanaDataContext, cancellation);

        ///<inheritdoc/>
        public async Task<IEnumerable<string>> GetCurrentUserRoles(CancellationToken cancellation = default) =>
            await _claimsPrincipalContext.GetCurrentUserRoles(_katanaDataContext, cancellation);

        /// <summary>
        /// Gets the message's owner user
        /// </summary>
        /// <returns></returns>
        private static async Task<TUser> GetTraceUser(IServiceScope scope, int traceUserId, CancellationToken cancellation)
        {
            var dataContext = scope.ServiceProvider.GetRequiredService<IDataContext>();
            return await dataContext.Set<TUser>(EntityActiveStatus.Any).FirstOrDefaultAsync(u => u.Id.Equals(traceUserId), cancellation);
        }

        ///<inheritdoc/>
        public string GetClaimsPrincipalObjectId() => _claimsPrincipalContext.GetClaimsPrincipalObjectId();

        ///<inheritdoc/>
        public async Task<bool> IsImpersonation(CancellationToken cancellation = default) => await _claimsPrincipalContext.IsImpersonation(_katanaDataContext, cancellation);

        #endregion

        #region Utils

        /// <inheritdoc/>
        public IEnumerable<Type> GetEntitiesTypes() => throw new NotImplementedException();

        /// <inheritdoc/>
        public IEnumerable<IForeignKey> GetForeignKeys(Type entityType) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task LoadCollection(object entity, PropertyInfo collectionMember, CancellationToken cancellation) => _katanaDataContext.LoadCollection(entity, collectionMember, cancellation);

        /// <inheritdoc/>
        public TOut Map<TIn, TOut>(Func<TIn, TOut> objInToObjOut, TIn objIn, GlobalActions failedMethod)
            where TIn : class
            where TOut : class
        {
            try
            {
                return objInToObjOut(objIn);
            }
            catch (Exception ex)
            {
                var msg =
                    $"Mapping to {typeof(TOut).Name} failed while executing method {failedMethod}. See the inner exception for details.";
                throw new CrudServiceException(msg, ex);
            }
        }

        /// <inheritdoc/>
        public TOut Map<TIn, TOut>(TIn objIn, Func<TIn, TOut, TOut> objInToObjOut, TOut objOut, GlobalActions failedMethod)
            where TIn : class
            where TOut : class
        {
            try
            {
                return objInToObjOut(objIn, objOut);
            }
            catch (Exception ex)
            {
                var msg =
                    $"Mapping to {typeof(TOut).Name} failed while executing method {failedMethod}. See the inner exception for details.";
                throw new CrudServiceException(msg, ex);
            }
        }

        private async Task AddStatusLog<T>(T entity, int? authorId,
            CancellationToken cancellation) where T : BaseEntity
        {
            MethodInfo addStatusMethod = null;
            object[] parameters = null;

            var statusLogInterface = entity.GetType().GetInterfaces().FirstOrDefault(x =>
                x.IsGenericType &&
                x.GetGenericTypeDefinition() == typeof(IReasonableStatusLogs<,,>));
            if (statusLogInterface is not null)
            {
                var reasonId = entity.GetType().GetProperty(nameof(IReasonableStatusLogs<BaseStatusLogWithReasonEntity<Status, TUser, BaseStatusLogReasonEntity>, BaseStatusLogReasonEntity, TUser>.ReasonId))?.GetValue(entity);
                parameters = [entity, this, entity.Status, entity.StatusNotes, authorId, reasonId, cancellation];
                addStatusMethod = typeof(StatusLogsServicesExtensions)
                    .GetMethod(nameof(StatusLogsServicesExtensions.AddStatusWithReasonFromKrudderBase), BindingFlags.Static | BindingFlags.Public)
                    .MakeGenericMethod(entity.GetType(), statusLogInterface.GenericTypeArguments[0], statusLogInterface.GenericTypeArguments[1], typeof(TUser));
            }
            if (addStatusMethod is null)
            {
                statusLogInterface = entity.GetType().GetInterfaces().FirstOrDefault(x =>
                    x.IsGenericType &&
                    x.GetGenericTypeDefinition() == typeof(IStatusLogs<,>));
                if (statusLogInterface is not null)
                {
                    parameters = [entity, this, entity.Status, entity.StatusNotes, authorId, cancellation];
                    addStatusMethod = typeof(StatusLogsServicesExtensions)
                        .GetMethod(nameof(StatusLogsServicesExtensions.AddStatusFromKrudderBase), BindingFlags.Static | BindingFlags.Public)
                        .MakeGenericMethod(entity.GetType(), statusLogInterface.GenericTypeArguments[0], typeof(TUser));
                }
            }

            if (addStatusMethod is not null)
            {
                var addTask = addStatusMethod.Invoke(null, parameters: parameters) as Task;
                await addTask;
            }
        }

        /// <inheritdoc/>
        public PropertyInfo GetPrimaryKeyPropertyInfo(Type entityType) => throw new NotImplementedException();

        /// <inheritdoc/>
        public PropertyInfo GetForeignKeyIdPropertyInfo(Type entityType, PropertyInfo navigationProperty) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<List<object>> UpdateBatch(IEnumerable<object> entities, BeforeSaveObjectBatchDelegate beforeSave = null, AfterSaveObjectBatchDelegate afterSave = null, AfterSaveAllObjectBatchDelegate afterBatchSave = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int?> Restore<T>(T entity, IValidator<T> validator, BeforeSaveDelegate<T> beforeSave = null, AfterSaveDelegate<T> afterSave = null, string rulesSet = null, CancellationToken cancellation = default) where T : BaseEntity => throw new NotImplementedException();

        #endregion
    }
}
