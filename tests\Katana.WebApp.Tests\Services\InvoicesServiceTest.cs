using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Validation;
using Katana.Core.Entities;
using Katana.Services.Invoices;
using Katana.Services.Invoices.Models;
using Microsoft.AspNetCore.Http;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Invoice CRUD service
    /// </summary>
    public class InvoicesServiceTest : IInvoicesService
    {
        ///<inheritdoc/>
        public FluentValidation.IValidator<Invoice> Validator =>
            throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddInvoiceDto> Add(AddInvoiceDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddInvoiceDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<InvoiceDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<InvoiceDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<InvoiceDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new InvoiceDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateInvoiceDto> Update(UpdateInvoiceDto dto,
            CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateInvoiceDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddNoteDto> AddNote(AddNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddNoteDto());

        ///<inheritdoc/>
        public async Task<NoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new NoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<NoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<NoteDto>([], default, default, default, default,
                default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdateNoteDto> UpdateNote(UpdateNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Workflow Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<InvoiceWorkflowStatus>>>
            GetWorkflowStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<InvoiceWorkflowStatus>>(
                    [], default, default,
                    default, default, default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Invoice> ConfigureSet(IQueryable<Invoice> _, Type _1, ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Invoice _, IList<Invoice> _1, string _2, Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<int> MarkAsInvoiced(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default) =>
            await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> MarkAsReadyToInvoice(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> SendBackToDraft(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => await Task.Run(() => dtoArr.Length);

        #endregion

        #region Attachments

        ///<inheritdoc/>
        public async Task<IPagedResults<BlobItemDto>> ListFiles(int id, SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<BlobItemDto>([], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<BlobItemDto> UploadFile(int id, AddBlobItemDto blobItem, bool overrideExisting, CancellationToken cancellation = default) =>
            await Task.Run(() => new BlobItemDto() { Id = id });

        ///<inheritdoc/>
        public Task<List<BlobItemDto>> UploadFiles(int id, List<IFormFile> files, bool overrideExisting, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<BlobItemDto> DownloadFile(int id, string fileName, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<BlobItemDto> DeleteFile(int id, string fileName, CancellationToken cancellation = default) =>
            await Task.Run(() => new BlobItemDto() { Id = id });

        /// <inheritdoc />
        public Task<BlobItemDto> DeleteFile(DeleteStorageFileDto dto,
            CancellationToken cancellation = default) =>
            Task.Run(() => new BlobItemDto() { Id = dto.ParentId }, cancellation);

        /// <inheritdoc />
        public async Task<IList<BlobItemDto>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) => await Task.Run(() => dtos.Select(dto => new BlobItemDto() { Id = dto.ParentId }).ToList());

        ///<inheritdoc/>
        public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) => throw new NotImplementedException();
        /// <inheritdoc/>
        public Task<IList<Careplan>> GetOCodeCareplans(DateTime date, CancellationToken cancellation) => throw new NotImplementedException();
        /// <inheritdoc/>
        public Task<IList<Careplan>> GetRCodeCareplans(DateTime date, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IEnumerable<InvoiceItem>> MarkItemsAsFreeService(Invoice invoice, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion
    }
}
