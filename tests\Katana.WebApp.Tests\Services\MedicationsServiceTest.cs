﻿using Aoshield.Core.Entities.Models;
using Katana.Services.Medications;
using Katana.Services.Medications.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Medication CRUD service
    /// </summary>
    public class MedicationsServiceTest : IMedicationsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddMedicationDto> Add(AddMedicationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddMedicationDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<MedicationDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<MedicationDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<MedicationDto>
            GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new MedicationDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateMedicationDto> Update(UpdateMedicationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateMedicationDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
