using Aoshield.Core.Entities.Models;
using Katana.Services.InquiryForms.Models;
using Katana.Services.InquiryFormSteps.Models;

namespace Katana.WebApp.Tests.Services;

/// <inheritdoc />
public class InquiryFormServiceTest : IInquiryFormService
{
    /// <inheritdoc />
    public Task<AddInquiryFormDto>
        Add(AddInquiryFormDto dto, CancellationToken cancellation = default) =>
        Task.FromResult(new AddInquiryFormDto());

    /// <inheritdoc />
    public Task<InquiryFromDto> GetById(int id, CancellationToken cancellation = default) =>
        Task.FromResult(id <= 0 ? null : new InquiryFromDto() { Id = id });

    /// <inheritdoc />
    public async Task<IPagedResults<InquiryFromDto>> GetAsPagedResults(SieveModel query,
        CancellationToken cancellation = default) => await Task.Run(() =>
        new PagedResults<InquiryFromDto>([], default,
            default, default, default, default, default, default, default), cancellation);

    /// <inheritdoc />
    public Task<UpdateInquiryFromDto> Update(UpdateInquiryFromDto dto,
        CancellationToken cancellation = default) =>
        Task.FromResult(new UpdateInquiryFromDto() { Id = 1 });

    /// <inheritdoc />
    public async Task<int?> Delete(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    /// <inheritdoc />
    public async Task<int?> Restore(int id, bool notify = true,
        CancellationToken cancellation = default) => await Task.Run(() => id);

    ///<inheritdoc/>
    public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    private static readonly int[] entitiesIds = [1];

    /// <inheritdoc />
    public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(entitiesIds);

    /// <inheritdoc />
    public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(entitiesIds);


    /// <inheritdoc />
    public async Task<IPagedResults<InquiryFormStepDto>> GetFormSteps(int inquiryFormId,
        SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() =>
            new PagedResults<InquiryFormStepDto>([], default,
                default, default, default, default, default, default, default), cancellation);

    ///<inheritdoc/>
    public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());
}
