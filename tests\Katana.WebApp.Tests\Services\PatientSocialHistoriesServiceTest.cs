﻿using Aoshield.Core.Entities.Models;
using Katana.Services.PatientSocialHistories;
using Katana.Services.PatientSocialHistories.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// PatientSocialHistory CRUD service
    /// </summary>
    public class PatientSocialHistoriesServiceTest : IPatientSocialHistoriesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientSocialHistoryDto> Add(AddPatientSocialHistoryDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientSocialHistoryDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientSocialHistoryDto>> GetAsPagedResults(
            SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<PatientSocialHistoryDto>([],
                    default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientSocialHistoryDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new PatientSocialHistoryDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdatePatientSocialHistoryDto> Update(UpdatePatientSocialHistoryDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientSocialHistoryDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id)
                .ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id)
                .ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Custom

        /// <summary>
        /// For retrieve all SocialHistories that belongs to an Patient
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<PatientSocialHistoryDto>> GetSocialHistoriesByPatientId(
            int patientId, SieveModel query) =>
            await Task.Run(() =>
                new PagedResults<PatientSocialHistoryDto>([],
                    default, default, default, default, default, default, default, default));

        #endregion
    }
}
