/**
 * Katana Entities
 */
export enum EntityName {
    UserInquiryFormStep = "UserInquiryFormStep",
    Treatmentplan = "Treatmentplan",
    Careplan = "Careplan",
    Fax = "Fax",
    Referral = "Referral",
    ReferralSpecialty = "ReferralSpecialty",
    ReferralSpecialist = "ReferralSpecialist",
    Patient = "Patient",
    PatientPoolItem = "PatientPoolItem",
    Clinic = "Clinic",
    Practitioner = "Practitioner",
    PractitionerSpecialty = "PractitionerSpecialty",
    ClinicPractitioner = "ClinicPractitioner",
    Invoice = "Invoice",
    InvoiceItem = "InvoiceItem",
    UserInquiryForm = "UserInquiryForm",
    UserInquiryFormQuestion = "UserInquiryFormQuestion",
    InquiryForm = "InquiryForm",
    InquiryFormStep = "InquiryFormStep",
    InquiryFormQuestion = "InquiryFormQuestion",
    Country = "Country",
    Province = "Province",
    User = "User",
    Transcriber = "Transcriber",
    TranscribersGroup = "TranscribersGroup",
    TranscribersManager = "TranscribersManager",
    TranscribersGroupConfig = "TranscribersGroupConfig",
    Diagnostic = "Diagnostic",
    EducationalCareplan = "EducationalCareplan",
    Medication = "Medication",
    VitalSign = "VitalSign",
    SocialHistory = "SocialHistory",
    PatientMedication = "PatientMedication",
    PatientSocialHistory = "PatientSocialHistory",
    PatientDiagnostic = "PatientDiagnostic",
    PatientVitalSign = "PatientVitalSign",
    Allergy = "Allergy",
    PatientAllergy = "PatientAllergy",
    UnitOfMeasurement = "UnitOfMeasurement",
    NotificationTemplate = "NotificationTemplate",
    ExportProfile = "ExportProfile",
    ImportItem = "ImportItem",
    DeferredRequest = "DeferredRequest",
    DeferredMerge = "DeferredMerge",
    ImportEvent = "ImportEvent",
    GroupItem = "GroupItem",
    PatientHistoryType = "PatientHistoryType",
    GroupLabTest = "GroupLabTest",
    GroupFolder = "GroupFolder",
    PatientStatus = "PatientStatus",
    Flag = "Flag",
    PatientGeneratedLetter = "PatientGeneratedLetter",
    ExportEvent = "ExportEvent",
    PatientDocumentFolder = "PatientDocumentFolder",
    Reminder = "Reminder",
    Group = "Group",
    CareplanResponseTemplate = "CareplanResponseTemplate",
    BillingSettings = "BillingSettings",
    PractitionerDiagnostic = "PractitionerDiagnostic",
    AiPromptDefinition = "AiPromptDefinition",
    PatientAttachment = "PatientAttachment",
}
