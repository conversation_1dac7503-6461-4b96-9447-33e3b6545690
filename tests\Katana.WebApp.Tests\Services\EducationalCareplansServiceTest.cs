using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.Entities.Models;
using Katana.Services.EducationalCareplans;
using Katana.Services.EducationalCareplans.Models;
using Microsoft.AspNetCore.Http;

namespace Katana.WebApp.Tests.Services;

/// <summary>
/// EducationalCareplan CRUD service
/// </summary>
public class EducationalCareplansServiceTest : IEducationalCareplanService
{
    /// <inheritdoc />
    public Task<AddEducationalCareplanDto> Add(AddEducationalCareplanDto dto, CancellationToken cancellation = default) =>
        Task.Run(() => new AddEducationalCareplanDto(), cancellation);

    /// <inheritdoc />
    public Task<EducationalCareplanDto> GetById(int id, CancellationToken cancellation = default) =>
        Task.Run(() => id <= 0 ? null : new EducationalCareplanDto() { Id = id }, cancellation);

    /// <inheritdoc />
    public async Task<IPagedResults<EducationalCareplanDto>> GetAsPagedResults(SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new PagedResults<EducationalCareplanDto>(
            [],
            default, default, default,
            default, default, default,
            default, default), cancellation);

    /// <inheritdoc />
    public Task<UpdateEducationalCareplanDto> Update(UpdateEducationalCareplanDto dto,
        CancellationToken cancellation = default) =>
        Task.Run(() => new UpdateEducationalCareplanDto() { Id = dto.Id }, cancellation);

    /// <inheritdoc />
    public async Task<int?> Delete(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    /// <inheritdoc />
    public async Task<int?> Restore(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id, cancellation);

    /// <inheritdoc />
    public async Task<IPagedResults<BlobItemDto>> ListFiles(int id, SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new PagedResults<BlobItemDto>([],
            default, default, default, default,
            default, default, default, default), cancellation);

    /// <inheritdoc />
    public Task<BlobItemDto> UploadFile(int id, AddBlobItemDto blobItem, bool overrideExisting,
        CancellationToken cancellation = default) =>
        Task.Run(() => new BlobItemDto() { Id = id }, cancellation);

    /// <inheritdoc />
    public Task<BlobItemDto> DownloadFile(int id, string fileName,
        CancellationToken cancellation = default) =>
        Task.Run(
            () => new BlobItemDto()
            {
                Id = id,
                Content = new MemoryStream(),
                ContentType = "application/octet-stream"
            }, cancellation);

    /// <inheritdoc />
    public Task<BlobItemDto> DeleteFile(int id, string fileName,
        CancellationToken cancellation = default) =>
        Task.Run(() => new BlobItemDto() { Id = id }, cancellation);

    /// <inheritdoc />
    public Task<BlobItemDto> DeleteFile(DeleteStorageFileDto dto,
        CancellationToken cancellation = default) =>
        Task.Run(() => new BlobItemDto() { Id = dto.ParentId }, cancellation);

    /// <inheritdoc />
    public async Task<IList<BlobItemDto>> DeleteFiles(DeleteStorageFileDto[] dtos,
        CancellationToken cancellation = default) => await Task.Run(() => dtos.Select(dto => new BlobItemDto() { Id = dto.ParentId }).ToList());


    /// <inheritdoc />
    public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) =>
        new("https://test.com");

    /// <inheritdoc />
    public Task<List<BlobItemDto>> UploadFiles(int id, List<IFormFile> files, bool overrideExisting, CancellationToken cancellation = default) =>
        throw new NotImplementedException();

    ///<inheritdoc/>
    public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default) => Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray(),
        cancellation);

    /// <inheritdoc />
    public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default) =>
        Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray(),
            cancellation);

    /// <inheritdoc />
    public Task<(MemoryStream, string)> GenerateEducationalCpPdf(int id, CancellationToken cancellation = default) => Task.Run(
            () => (new MemoryStream() { }, ""), cancellation);

    ///<inheritdoc/>
    public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());
}
