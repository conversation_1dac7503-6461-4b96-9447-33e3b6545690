{
    "CosmosDb": {
        "DatabaseName": "katana-audit-demo",
        "ContainerName": "ChangesLog",
        "PartitionKeyPath": "/partitionKey",
        "MaxPageSize": 2147483647,
        "DefaultPageSize": 50
    },
    //Based on version 3.3.1 of azure-identity : https://github.com/AzureAD/microsoft-identity-web
    //Added JSON schema support for Microsoft.Identity.Web configuration.
    //This allows for schema validation in the appsettings.json, improving configuration accuracy and developer experience.
    //To use it, add the following at the top of your appsettings.json:
    //"$schema": "https://github.com/AzureAD/microsoft-identity-web/blob/master/JsonSchemas/microsoft-identity-web.json",
    "AllowedHosts": "*",
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft": "Warning",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "SieveOptions": {
        "CaseSensitive": false,
        "DefaultPageSize": 50,
        "MaxPageSize": 2147483647,
        "ThrowExceptions": true,
        "EnabledByDefault": true,
        "IgnoreNullsOnNotEqual": true,
        "DisableNullableTypeExpressionForSorting": true
    },
    "KrudderDbConfiguration": {
        "BatchSize": 100,
        "DbMaxBatchSize": 42,
        "DbCommandTimeout": 120,
        "DbConcurrencyRetryLimit": 5,
        "DbConcurrencyMillisecondsDelay": 10,
        "DbConcurrencyMaximumRetryDelay": 500,
        "DbConcurrencyRetryDelayMultiplier": 2,
        "ProductionAllowedOrigins": "https://*.satorigroup.ca",
        "DevelopmentAllowedOrigins": "http://localhost:3000,https://localhost:3000"
    },
    "AzureAd": {
        "Instance": "https://login.microsoftonline.com/",
        "Domain": "satorigroup.ca",
        "TenantId": "266d411f-4c9f-4b35-a35e-db25f5e5616e",
        "ClientId": "b73061cf-54b5-4a64-9f1d-219f84fd8ee8",
        "ObjectId": "a081552d-fd29-4a09-97fc-c4c0147a0006",
        "PatientGroupObjectId": "9dfeca1b-3e07-42a8-93cf-79bfb515860d",
        "RegistrationDefaultGroupObjectId": "9dfeca1b-3e07-42a8-93cf-79bfb515860d",
        "RegistrationUserId": "338"
        //"CallbackPath": "/SigninOidc",
        /*"Audience": "https://satorigroup.ca/AuthTest"*/
    },
    "MicrosoftGraph": {
        // Specify BaseUrl if you want to use Microsoft graph in a national cloud.
        // See https://learn.microsoft.com/graph/deployments#microsoft-graph-and-graph-explorer-service-root-endpoints
        "BaseUrl": "https://graph.microsoft.com/v1.0",
        // Set RequestAppToken this to "true" if you want to request an application token (to call graph on
        // behalf of the application). The scopes will then automatically
        // be ['https://graph.microsoft.com/.default'].
        "RequestAppToken": true
        //// Set Scopes to request (unless you request an app token).
        //"Scopes": [
        //    "User.Read",
        //    "ChannelMessage.Send"
        //]
        // See https://aka.ms/ms-id-web/downstreamApiOptions for all the properties you can set.
    },
    "Auth": {
        "UserTypeGroupPrefix": "Katana"
    },
    "ValidationStatusManager": {
        "BatchSize": 500,
        "ValidationUserId": 334,
        "DisabledEntities": []
    },
    "SearchManager": {
        "BatchSize": 500,
        "SearchIndexUserId": 335
    },
    "AuditManager": {
        "BatchSize": 100,
        "AuditUserId": 334
    },
    "AzureFormRecognizer": {
        "Endpoint": "https://katana-form-recognizer-prod-01.cognitiveservices.azure.com/",
        "ModelId": []
    },
    "Export": {
        "ExpirationInMinutes": 1440,
        "GenericPageSize": 2500
    },
    "ServiceBus": {
        // SessionId
        "SessionId": "prod_dispatcher",
        "ProdSessionId": "prod_dispatcher",
        "BetaSessionId": "beta",
        "CleanupSessionIds": [],
        "DeadLetterEnabled": true,
        //Validation
        "Validation": {
            "QueueName": "sess_concurrent_validation",
            "BulkQueueName": "sess_concurrent_bulkvalidation",
            "MaxConcurrentSessions": 5,
            "MaxBulkConcurrentSessions": 5,
            "MaxAutoLockRenewalDuration": 5,
            "MaxBulkAutoLockRenewalDuration": 15
        },
        "Audit": {
            "QueueName": "sess_concurrent_audit",
            "MaxConcurrentSessions": 5,
            "MaxAutoLockRenewalDuration": 5
        },
        //Search
        "Search": {
            "QueueName": "sess_concurrent_buildsearchindex",
            "BulkQueueName": "sess_concurrent_bulkbuildsearchindex",
            "MaxConcurrentSessions": 5,
            "MaxBulkConcurrentSessions": 5,
            "MaxAutoLockRenewalDuration": 5,
            "MaxBulkAutoLockRenewalDuration": 15
        },
        "DeferredRequest": {
            "QueueName": "sess_concurrent_deferredrequest",
            "MaxConcurrentSessions": 5,
            "MaxAutoLockRenewalDuration": 15
        },
        //Merges
        "MergeQueueName": "sess_entitymerge",
        // Telemetry Collection
        "TelemetryCollectionQueueName": "sess_telemetrycollection",
        //User Notifications
        "UserNotificationQueueName": "sess_usernotification",
        //Expire
        "ExpireQueueName": "sess_expire",
        "Queues": [
            {
                "Name": "sess_incomingfax",
                "MessageType": "SBIncomingFaxMessage",
                "ProcessorType": "SBIncomingFaxMessageProcessor"
            },
            {
                "Name": "sess_sharepointincomingfax",
                "MessageType": "SBSharepointIncomingFaxMessage",
                "ProcessorType": "SBSharepointIncomingFaxMessageProcessor"
            },
            {
                "Name": "sess_pendingprocessingdoc",
                "MessageType": "SBPendingProcessingFaxMessage",
                "ProcessorType": "SBPendingProcessingFaxMessageProcessor",
                "DeadletterProcessorType": "SBDeadLetterPendingProcessingFaxMessageProcessor"
            },
            {
                "Name": "sess_aiprocessing",
                "MessageType": "SBOpenAIMessage",
                "ProcessorType": "SBOpenAIMessageProcessor",
                "DeadletterProcessorType": "SBDeadLetterOpenAIMessageProcessor"
            }
        ]
    },
    "AzureStorage": {
        "ContainerName": "katana-attachments",
        "DefaultSasExpirationInMinutes": 5,
        "MoveFileDelayInMilliseconds": 1000,
        "MoveFileTimeoutInMilliseconds": 30000
    },
    "SharepointStorage": {
        "DriveId": "b!bexlfnhlSU-5K53SA6sKLLKPf5XCnUZFnpKGZ1uHmelepjxp5Wv8QqKWbBbPs29I"
    },
    "AzureDevOps": {
        "Uri": "https://dev.azure.com/Satori-Group",
        "Project": "Katana",
        "AreaPath": "KATANA\\KATANA Stakeholders Team"
    },
    "RingCentral": {
        "ClientId": "BIVfRoc8Tsy-PBWJIQHfew",
        "ClientIdJwt": "VfugBDtt9CYdUihlzTny9u", //Jwt token based auth app
        "ServerUrl": "https://platform.ringcentral.com/",
        "UserName": "+15874414315",
        "Extension": "101",
        "Threshold": 600,
        "Interval": 15,
        "UseJwt": false,
        "ErrorStatusCodes": [ "TooManyRequests", "ServiceUnavailable", "Unauthorized", "BadRequest" ]
    },
    "CPSSettings": {
        "AutoassignRetryAttemps": 3,
        "DefaultCPSTreatmentPlanTypeId": 338,
        "DefaultCPSPostTreatmentPlanTypeId": 402,
        "DefaultCPSSecondLoopCareplanTypeId": 318,
        "DefaultWebclinicAsCPSCareplanTypeId": 478,
        "DefaultPdfPreviewFolder": "preview",
        "NearlyExpiringDays": 7,
        "ExpiringSoonDays": 14,
        "SecondLoopSettings": {
            "GpPendingDays": 1,
            "PendingReviewDays": 5,
            "ITPPendingDays": 5,
            "SPPendingDays": 5
        },
        "GPSettings": {
            "GPDefaultPeriod": 0, //hours (1 days)
            "GPMaxPeriod": 168, //hours (7 days)
            "CronBatchSize": 50
        },
        "PediatricSpecialtyId": 200,
        "ConditionsGroupId": 4,
        "DefaultDispatchApprovingSpQuota": -1,
        "DefaultDispatchPatientQuota": 4,
        "DefaultDispatchPatientDxCodeQuota": 1,
        "DefaultDispatchRequestingGpQuota": 100
    },
    "ECCSettings": {
        "DaysSettings": {
            "ITPPendingDays": 5,
            "SPPendingDays": 10
        }
    },
    "ReportsSettings": {
        "PrCareplanEcc": "pr_careplan_ecc",
        "HPU": "hpu"
    },
    "Powerbi": {
        "AzureTenantId": "266d411f-4c9f-4b35-a35e-db25f5e5616e",
        "ApplicationId": "94c1fc6d-873c-4636-be05-824e86590208",
        "WorkspaceName": "Reports",
        "UrlPowerBiServiceApiRoot": "https://api.powerbi.com/",
        "TenantSpecificAuthority": "https://login.microsoftonline.com/",
        "PbiReportExportTimeOut": 60,
        "PowerBiServiceScopes": [
            "https://analysis.windows.net/powerbi/api/.default"
        ]
    },
    "SendGrid": {
        "FromEmail": "<EMAIL>",
        "FromName": "Katana Solutions"
    },
    "Impersonation": {
        "GrantedExpirationPeriod": 10
    },
    "ClientApp": {
        "IdleSessionTimeout": 30
    },
    "FeatureManagement": {
        "DisableAISummaryUI": false,
        "DisableAISummaryFeedback": true,
        "DisableIdBasedValidationTableLogging": true,
        "DisableAliasSearch": true,
        "DisableSearchIndexUpdateTableLogging": true,
        "DisableCrons": true,
        "DisableCloudFaxesProcessing": true,
        "DisableSharepointFaxesProcessing": true,
        "DisableDeadLetterMessagesRetry": true,
        "DisableDailyMaintenance": true,
        "DisableGPAutomaticProcessing": true,
        "DisableEntityActionsQualityCheck": false,
        "DisableAccuroIntegration": false,
        "DisableAutomaticAccuroRefreshToken": true,
        "DisableOpenAIIntegration": true,
        "DisableCareplansAudit": true,
        "DisableUploadFaxProcess": true,
        "DisableInvalidateQueryWithCache": true,
        "DisableInvalidateQueryWithAdd": true,
        "DisableSyncEMRNomenclators": true,
        "DisableOnboardingAccuroClinics": true,
        "DisableOnboardingHealthQuestClinics": true,
        "DisableReSynchronizeClinicPatient": true,
        "DisableOnboardingAccuroPractitioners": true,
        "DisableOnboardingExternalExpressPractitioners": true,
        "DisableOnboardingHealthQuestPractitioners": true,
        "DisableOnboardingMedAccessPractitioners": true,
        "DisableOnboardingAccuroExternalPractitioners": true,
        "DisableOnboardingAvaPractitioners": true,
        "DisableGeneratePanelingPool": true,
        "DisableScheduleClinicalQuestions": true,
        "DisableDispatchClinicalQuestions": true,
        "DisablePatientDemographicsSynchronization": false,
        "DisablePatientMedicationsSynchronization": false,
        "DisablePatientAllergiesSynchronization": false,
        "DisablePatientGeneratedLettersSynchronization": false,
        "DisablePatientEncountersNotesSynchronization": false,
        "DisablePatientDocumentFoldersSynchronization": false,
        "DisablePatientHistoryItemsSynchronization": false,
        "DisableCronSynchronizePatientEncountersNotes": true,
        "DisablePatientLabTestsSynchronization": false,
        "DisablePatientFlagsSynchronization": false,
        "DisableUserRegistration": true,
        "DisableDatabaseBasedRoles": false,
        "DisableMigrateRolesFromAd": true,
        "DisableGenerateAIClinicalQuestion": false,
        "DisablePrePanelingPatientStep": true,
        "DisableNewDependenciesApproach": true,
        "DisableProcessReminders": true,
        "DisableNewAIFlow": false,
        "DisablePopulateAvailableCareplansForSPs": true,
        "DisableMaintenanceAlertCard": true,
        "DisabledPatientDashboardAlert": true,
        "DisableAIMicroservice": true,
        "DisabledUploadDocument": false,
        "DisablePractitionerResponseBackUI": false
    },
    "UrlSingConfiguration": {
        "SigningKey": "&*Whasas%Gsg*8a7axzkljrgtngsxbhwwaw&&^%^SAGzxzxzBshdhsd",
        "DefaultExpirationInMinutes": 2
    },
    "RegistrationSettings": {
        "RedirectUrl": "https://katana.satorigroup.ca",
        "ADGroupPrefix": "Katana"
    },
    "EmailTemplateSettings": {
        "ImageUrl": ""
    },
    "HangfireSettings": {
        "EnableDashboard": true,
        "DashboardUrlPath": "/hangfire",
        "Queue": "default",
        "DashboardUser": "admin",
        "CronExpressions": {
            "IPatientsService_GeneratePanelingPool": "*/5 * * * *", //every 5 minutes
            "IDeadLetterMessageService_RequeueMessages": "*/5 * * * *", //every 5 minutes
            "IFaxesService_ProcessCloudIncomingFaxes": "*/5 * * * *", //every 5 minutes
            "IFaxesService_ProcessIncomingFaxes": "*/5 * * * *", //every 5 minutes
            "IBulkValidationService_EnqueueAllEntitiesDailyValidation": "0 0 * * *", //every day,
            "ICareplansService_AutomaticGPApproval": "*/10 * * * *", //every 10 minutes
            "IAccuroHttpHandler_RefreshTokenJob": "0 */1 * * *", // every 6 hours
            "ICareplansService_ExportAuditCareplanPdfs": "0 */6 * * *", //every 6 hours"
            "ICareplansService_CronProcessUploadFax": "0 0 * * *", //every day
            "IPatientsService_SynchronizeEMRNomenclators": "0 0 * * *",
            "IPatientsService_ProcessOCRPendingDocuments": "0 6,18 * * *", //Twice a day at 6 AM and 6 PM
            "IPatientsService_OnboardingPractitioners": "0 0 1 * *", // One time a month
            "IPatientsService_OnboardingExternalPractitioners": "0 0 1 * *", // One time a month
            "IPatientsService_OnboardingExternalExpressPractitioners": "0 0 1 * *", // One time a month
            "IPatientsEMRSyncRecordService_OnboardingClinicPatient": "0 6,18 * * *", // twice a day at 6 AM and 6 PM
            "IPatientsEMRSyncRecordService_OnboardingHealthQuestPatient": "0 6,18 * * *", // Obsolete twice a day at 6 AM and 6 PM
            "IPatientsEMRSyncRecordService_OnboardingExternalClinicPatient": "0 6,18 * * *", // twice a day at 6 AM and 6 PM
            "IPatientsEMRSyncRecordService_ReSynchronizeClinicPatient": "0 0 * * 0", // Once a week at midnight on Sunday
            "ITreatmentplansService_ScheduleClinicalQuestions": "*/5 * * * *", //every 5 minute
            "ICareplansService_ScheduleClinicalQuestions": "*/5 * * * *", //every 5 minute
            "ICareplansService_DispatchClinicalQuestions": "0 * * * * ", //every day,
            "IPatientsService_UploadEncounterNotes": "0 0 * * * ", //every day
            "ITreatmentplansService_ProcessPatientsPendingByAIQuestions": "*/5 * * * *", //every 5 minute
            "IReminderService_ProcessReminders": "0 0 * * * ", //every day
            "IPatientsService_OnboardingHealthQuestPractitioners": "0 * * * *", //every hour
            "IPatientsService_OnboardingMedAccessPractitioners": "0 * * * *", //every hour
            "IPatientsService_OnboardingAccuroExternalPractitioners": "0 * * * *", //every hour
            "IPatientsService_OnboardingAvaPractitioners": "0 * * * *", //every hour
            "ICareplansService_MoveCareplansFromUpcomingToAvailableForSPs": "0 2 * * *" // every day at 2 AM
        },
        "AuthorizedDashboardIps": "",
        "ForceShowDashboard": false,
        "UserHttpContextAccessor": true
    },
    "NotificationsSettings": {
        "SPsTeamsGroupId": "test",
        "SMSTestRecipient": "",
        "EmailTestRecipient": ""
    },
    "InvoiceSettings": {
        "BillingPeriodDays": 7,
        "RCodePaymentAmount": 34.89
    },
    "LockUserSettings": {
        "BillingLockUserId": 242,
        "DeliveryUserId": 277,
        "SummarizationLockedUserId": 290,
        "SynchronizeUserId": 336,
        "DefaultCronUserId": 336,
        "GenerateAIQuestionUserId": 365
    },
    "FileUploadSettings": {
        "MaxFileSize": 52428800,
        "MaxFiles": 30
    },
    "WorkflowStatusOrder": {
        "Careplan": [
            "BeingCreated",
            "Proposed",
            "GpApproved",
            "GpsiItpPending",
            "GpPending",
            "PendingReview",
            "ITPPending",
            "ITPApproved",
            "SPPending",
            "SPApproved",
            "Unanswered",
            "Billed",
            "Cancelled",
            "ReloopToGpsiItp",
            "ReloopToItp",
            "ReloopToSp",
            "AdjustedRequestDate",
            "AdjustedResponseDate"
        ]
    },
    "VideoPlayerSettings": {
        "PatientPortalPlayList": [
            "704322316"
        ]
    },
    "NotificationLinkSettings": {
        "UrlBase": "https://satori-katana.azurewebsites.net"
    },
    "AccuroSettings": {
        "BaseUrl": "https://accapi.accuroemr.com/rest/v1/provider-portal/",
        "BaseUrl_V2": "https://accapi.accuroemr.com/rest/v2/provider-portal/",
        "BaseUrlBase": "https://accapi.accuroemr.com/rest/v1/",
        "AuthorizationUrl": "https://accapi.accuroemr.com/oauth/authorize",
        "CallbackUrl": "https://katana.satorigroup.ca/api/accuro/callback",
        "RedirectUri": "https://katana.satorigroup.ca/",
        "TokenUrl": "https://accapi.accuroemr.com/oauth/token",
        "ClientId": "e8mkfhavl1qim6p763cue4hs5j",
        "CpsUploadFolder": "CPS e-Consult",
        "CpsUploadFolderSubType": "CPS",
        "EccUploadFolder": "ECC e-Consult",
        "EccUploadFolderSubType": "ECC",
        "SubscriptionKeyHeader": "X-QHR-Subscription-Key",
        "SubscriptionKey": "",
        "Uuid": "bf83376b-c465-4fff-ac7f-7c37bd75b46a",
        "MaxBatchSize": 50,
        "SyncMaxBatchSize": 100,
        "OnboardingMaxPatientsByPractitioner": 10,
        "OnboardingMaxPatientsByExternalPractitioner": 10,
        "AuthorizedPractitionerIdForPool": [], // useful for truobleshooting; empty means all practitioners
        "CountRecordsToProcess": 100,
        "Gender": {
            "Male": 1,
            "Female": 2,
            "Unknown": 3
        },
        "DateFormats": {
            "DashDateFormat": "yyyy-MM-dd",
            "DashDateFormat2": "yyyy-MMM-dd",
            "DashDateFormat3": "dd-MM-yyyy",
            "DashDateFormat4": "dd-MMM-yyyy",
            "SlashDateFormat": "yyyy/MM/dd",
            "SlashDateFormat2": "yyyy/MMM/dd",
            "SlashDateFormat3": "dd/MM/yyyy",
            "SlashDateFormat4": "dd/MMM/yyyy",
            "SlashDateFormat5": "MM/dd/yyyy",
            "FullDateTimeFormat": "yyyy-MM-ddTHH:mm:ss.fff",
            "FullDateTimeFormatWithTimeZone": "yyyy-MM-ddTHH:mm:ss.fffzzz"
        },
        "EncountersNotesFolder": "",
        "EncountersNotesSourceFileName": "Chart.pdf",
        "DeleteEncountersNotesAfterUpload": false,
        "CustomHistoryTypes": {
            "CustomDiagnosesId": 1,
            "CustomOthersId": 47
        },
        "IgnoredSubFoldersToCopy": [ "preview" ],
        "InvalidConditionsExternalClinicsPatients": [
            "No Profile",
            "Comorbidities",
            "History of Problems",
            "None Recorded",
            "History of Problems None Recorded",
            "Comorbidities None Recorded"
        ],
        "InvalidPhns": [
            "000000000",
            "111111111",
            "999999999"
        ]
    },
    "DefaultEMRSyncIds": {
        "FolderId": 1,
        "GroupFolderId": 1,
        "GroupLabTestId": 2,
        "ConditionHistoryItemId": 4
    },
    "PageSizeLimit": 50,
    "AzureOpenAI": {
        "Endpoint": "https://inhou-m1jiotwk-westus.openai.azure.com/",
        "Model": "KatanaAIGpt4O-03"
    },
    "AzureAISearch": {
        "SearchEndpoint": "https://katanapatientsearch-02.search.windows.net/",
        "SearchIndex": "patientaidocument-prod-index",
        "IndexingWaitTime": 2,
        "SearchIndexer": "patientaidocument-prod-indexer",
        "ReIndexTimeout": 5
    },
    "CareplansAuditSetting": {
        "ContainerName": "KatanaDoctorsAudit",
        "MaxBatchSize": 200
    },
    "PatientPaneling": {
        "DefaultPoolSize": 20,
        "MaxPatientsByPractitionerToOCRProcess": 5,
        "MaxPatientsToOCRProcess": 10,
        "AlertPanelingDays": 20,
        "GpPanelingAuthorizeIds": [
            226,
            227,
            6001,
            322,
            6018,
            267
        ],
        "GpAutoApprovalIds": [
            6128,
            6123,
            6073,
            6027,
            6095,
            6115,
            267,
            6068,
            6112,
            6071,
            6113,
            6029,
            6070,
            6099,
            6104,
            334,
            6102,
            6057,
            6030,
            346,
            6033,
            6117,
            6094,
            6103,
            324,
            6001,
            6059,
            6107,
            218,
            6018,
            226,
            317,
            322,
            6118,
            6101,
            227,
            6109
        ],
        "AISummaryFeedbackUrl": "https://katana.satorigroup.ca/feedback",
        "MaxOcrAttempts": 3,
        "DocumentTypesToIncludeInQuestions": [ 1, 2, 3 ]
    },
    "UploadFaxSettings": {
        "MaxBatchSize": 100,
        "UploadAttempts": 3,
        "CountRecordsToProcess": 20
    },
    "AzureCommunication": {
        "PhoneNumber": "+18665504145",
        "FromEmail": "<EMAIL>"
    },
    "ActionableEntitiesSettings": {
        "DisabledActionableEntities": []
    },
    "FileBaseSynchronizationSettings": {
        "FileName": "PatientPaneling.xlsx"
    },
    "DispatchClinicalQuestionsSettings": {
        "LookBackDaysToAttempt": 3,
        "LookBackDispatchDate": "",
        "WellHealthClinics": [],
        "WellHealthItps": []
    },
    "NotesSettings": {
        "NumberOfNotesToPreview": 1
    },
    "MaintenanceSettings": {
        "MaintenanceAlertText": "Katana will be down for system maintenance on Monday, March 24th from 12am to 4am (MDT). We apologize for any inconvenience."
    },
    "DateTimeSettings": {
        "MaxInputFutureDateYearInterval": 10
    },
    "AutoSaveFormSettings": {
        "DelayAfterLastChange": 3000,
        "MaxWaitTimeWithoutSave": 10000,
        "MaxFormsSavedForKey": 3
    }
}
