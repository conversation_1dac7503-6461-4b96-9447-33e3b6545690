﻿using Aoshield.Core.Entities.Models;
using Aoshield.Services.Core.Validation;
using Aoshield.Services.Core.Validation.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Country CRUD service
    /// </summary>
    public class BulkValidationCrudServiceTest : IBulkValidationCrudService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddBulkValidationDto> Add(AddBulkValidationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddBulkValidationDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<BulkValidationListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<BulkValidationListDto>(
                [], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<BulkValidationDetailsDto> GetById(int id,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new BulkValidationDetailsDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateBulkValidationDto> Update(UpdateBulkValidationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateBulkValidationDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() => id);

        ///<inheritdoc/>
        public Task<int[]> DeleteBatch(ConfirmationNoteDto[] countryIds, bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() => countryIds.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> RestoreBatch(ConfirmationNoteDto[] ids, bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => ids.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
