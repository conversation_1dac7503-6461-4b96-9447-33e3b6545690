using Microsoft.AspNetCore.Mvc;

namespace Aoshield.Services.Core.ApiProblemDetails
{
    /// <summary>
    /// Container (DTO) used to send a collection of errors the Katana ProblemDetails.Errors (extension) field.
    ///
    /// In most cases, we may map the Title/Detail fields to Exception Type/Message, but
    /// this DTO could be used to map other error-related info.
    ///
    /// Certain reponses may include a more specific "error" type, such as Validation errors, where
    /// the ProblemDetails.Errors field is a ValidationFailure collection.
    ///
    /// </summary>
    public class ProblemDetailsError
    {
        /// <inheritdoc cref="ProblemDetails.Title"/>
        public string Title { get; set; }

        /// <inheritdoc cref="ProblemDetails.Detail"/>
        public string Detail { get; set; }
    }
}
