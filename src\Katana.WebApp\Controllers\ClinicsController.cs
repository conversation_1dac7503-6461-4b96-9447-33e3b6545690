﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Katana.Services.ClinicPractitioners;
using Katana.Services.ClinicPractitioners.Models;
using Katana.Services.Clinics;
using Katana.Services.Clinics.Models;
using Katana.Services.PatientsEMRSyncRecords;
using Katana.Services.PatientsEMRSyncRecords.Models;
using Katana.Services.Practitioners;
using Katana.Services.Practitioners.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Clinics Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ClinicsController : ControllerBase
    {
        private readonly IClinicsService _clinicsService;
        private readonly IPractitionersService _practitionersService;
        private readonly IClinicPractitionersService _clinicPractitionersService;
        private readonly IPatientsEMRSyncRecordService _patientEMRSyncRecordService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="clinicsService"></param>
        /// <param name="practitionersService"></param>
        /// <param name="clinicPractitionersService"></param>
        /// <param name="patientEMRSyncRecordService"></param>
        public ClinicsController(
            IClinicsService clinicsService,
            IPractitionersService practitionersService,
            IClinicPractitionersService clinicPractitionersService,
            IPatientsEMRSyncRecordService patientEMRSyncRecordService
            )
        {
            _clinicsService = clinicsService;
            _practitionersService = practitionersService;
            _clinicPractitionersService = clinicPractitionersService;
            _patientEMRSyncRecordService = patientEMRSyncRecordService;

        }

        /// <summary>
        /// Get all dtos
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<ClinicListDto>> GetClinics([FromQuery] SieveModel query) =>
            await _clinicsService.GetAsPagedResults(query);

        /// <summary>
        /// Get dto by id
        /// </summary>
        /// <param name="id">Id of the entity</param>
        /// <returns>Dto with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ClinicDto>> GetClinic(int id)
        {
            var dto = await _clinicsService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateClinicDto>> PutClinic(int id, UpdateClinicDto dto,
            CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _clinicsService.Update(dto, cancellation: cancellation);

        /// <summary>
        /// Add entity
        /// </summary>
        /// <param name="addDto"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost]
        public async Task<ActionResult<AddClinicDto>> PostClinic(AddClinicDto addDto,
            CancellationToken cancellation)
        {
            var dto = await _clinicsService.Add(addDto, cancellation: cancellation);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        #region Clinic practitioner

        /// <summary>
        /// Associated Practitioner to a Clinic
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Practitioners")]
        public async Task<ActionResult<AddClinicPractitionerDto>> PostClinicPractitioner(
            AddClinicPractitionerDto addDto)
        {
            var dto = await _clinicPractitionersService.Add(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Update Clinic Practitioner relationship
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Practitioners/{id}")]
        public async Task<ActionResult<UpdateClinicPractitionerDto>> PutClinicPractitioner(int id,
            UpdateClinicPractitionerDto dto) =>
            id != dto.Id ? BadRequest() : await _clinicPractitionersService.Update(dto);

        /// <summary>
        /// Get Clinic Practitioner relationship by Id
        /// </summary>
        /// <param name="id">id</param>
        /// <returns></returns>
        [HttpGet("Practitioners/{id}")]
        public async Task<ActionResult<ClinicPractitionerDto>> GetClinicPractitioner(int id)
        {
            var dto = await _clinicPractitionersService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// For retrieving all practitioners that belongs to a Clinic
        /// </summary>
        /// <param name="id"></param>
        /// <param name="query"></param>
        /// <returns>List of ClinicPractitioner</returns>
        [HttpGet("{id}/Practitioners")]
        public async Task<IPagedResults<ClinicPractitionerDto>> GetPractitioners(int id,
            [FromQuery] SieveModel query) =>
            await _practitionersService.GetClinicPractitionersByClinicId(id, query);

        /// <summary>
        /// Remove practitioners from a Clinic
        /// </summary>
        /// <param name="dtoArr">list of ClinicPractitioner ID List</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpDelete("Practitioners")]
        public async Task<int[]> DeletePractitioners(ConfirmationNoteDto[] dtoArr) =>
            await _clinicPractitionersService.DeleteBatch(dtoArr);

        /// <summary>
        /// List of practitioners availables to assing a Clinic
        /// </summary>
        /// <param name="id"></param>
        /// <param name="query"></param>
        /// <returns>List of diagnostics</returns>
        [HttpGet("{id}/AvailablePractitioners")]
        public async Task<IPagedResults<PractitionerDto>>
            GetAvailablePractitioners(int id, [FromQuery] SieveModel query) =>
            await _practitionersService.GetAvailablePractitionersByClinicId(id, query);

        /// <summary>
        /// Disable batch Clinic Practitioners with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisablePractitionerBatch")]
        public async Task<int[]> DisablePractitionerBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _clinicPractitionersService.DisableBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable batch Clinic Practitioners
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnablePractitionerBatch")]
        public async Task<int[]> EnablePractitionerBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _clinicPractitionersService.EnableBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Marks clinics as ready for onboarding
        /// </summary>
        /// <param name="dtos"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>//
        [HttpPut("MarkPractitionerAsReadyToOnboarding")]
        public async Task<int> MarkPractitionerAsReadyToOnboarding(BaseUpdateDto[] dtos,
            CancellationToken cancellation) =>
            await _clinicPractitionersService.MarkAsReadyForOnboarding(dtos, cancellation: cancellation);

        /// <summary>
        /// Upload Patient Paneling File
        /// </summary>
        /// <param name="id">Fax id</param>
        /// <param name="files">File to upload</param>
        /// <param name="overrideExisting">Override file if existing</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>        
        [HttpPost("PatientPanelingFile/{id}")]
        public async Task<ActionResult> UploadPatientsPanelFile(int id, [FromForm] List<IFormFile> files, bool overrideExisting, CancellationToken cancellation)
        {
            var response = await _clinicPractitionersService.UploadPatientsPanelFile(id, files.Count > 0 ? files[0] : null, overrideExisting, cancellation);

            return Ok((response.FullPathName, Result: "Ok"));
        }

        /// <summary>
        /// CP Response Back
        /// </summary>
        /// <param name="dtos">ResponseBackDto array</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Count of records processed</returns>
        [HttpPut("toggleResponseBackCp")]
        public async Task<int> ToggleResponseBackCp(
            [FromBody] ClinicPractitionerResponseBackDto[] dtos,
            CancellationToken cancellation) =>
            await _clinicPractitionersService.ToggleResponseBackCp(dtos[0], cancellation: cancellation);

        /// <summary>
        /// Get the count of care plans to send back for a given clinic practitioner.
        /// </summary>
        /// <param name="id">The ID of the clinic practitioner.</param>
        /// <param name="cancellation">Cancellation token.</param>
        /// <returns>The count of care plans to send back.</returns>
        [HttpGet("{id}/CareplansToSendBack")]
        public async Task<CareplansToSendBackDto> GetCareplansToSendBack(int id, CancellationToken cancellation) =>
            await _clinicPractitionersService.GetCareplansToSendBack(id, cancellation);

        #endregion

        /// <summary>
        /// Updates Cps value for clinics
        /// </summary>
        /// <param name="dtoArr">Contains id of TrancriberGroup and id(s) of fax(es) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("UpdateCps")]
        public async Task<int> UpdateCps(ClinicCpsDto[] dtoArr, CancellationToken cancellation) =>
            await _clinicsService.UpdateCps(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeleteClinic(int id,
            CancellationToken cancellation = default) =>
            await _clinicsService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestoreClinic(int id) =>
            await _clinicsService.Restore(id, notify: false);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">Clinic's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLogs(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _clinicsService.GetStatusLogsAsPagedResults(id, query, cancellation);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities with note to deleted</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _clinicsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _clinicsService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Add note to Clinic
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Notes")]
        public async Task<ActionResult<AddNoteDto>> PostNote(AddNoteDto addDto)
        {
            var dto = await _clinicsService.AddNote(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Note by Id
        /// </summary>
        /// <param name="id">Note id</param>
        /// <returns></returns>
        [HttpGet("Notes/{id}")]
        public async Task<ActionResult<NoteDto>> GetNote(int id)
        {
            var dto = await _clinicsService.GetNoteById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">Clinic's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Notes")]
        public async Task<IPagedResults<NoteDto>> GetNotes(int id, [FromQuery] SieveModel query) =>
            await _clinicsService.GetNotesAsPagedResults(id, query);

        /// <summary>
        /// Update note
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Notes/{id}")]
        public async Task<ActionResult<UpdateNoteDto>> PutNote(int id, UpdateNoteDto dto) =>
            id != dto.Id ? BadRequest() : await _clinicsService.UpdateNote(dto);

        /// <summary>
        /// Delete Note
        /// </summary>
        /// <param name="id">Id of the note to delete</param>
        /// <returns></returns>
        [HttpDelete("Notes/{id}")]
        public async Task<ActionResult<int?>> DeleteNote(int id) =>
            await _clinicsService.DeleteNote(id);

        /// <summary>
        /// Delete Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to delete</param>
        /// <returns></returns>
        [HttpDelete("DeleteNoteBatch")]
        public async Task<int[]> DeleteNoteBatch(int[] ids) =>
            await _clinicsService.DeleteNoteBatch(ids);

        /// <summary>
        /// Restore Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to restore</param>
        /// <returns></returns>
        [HttpDelete("RestoreNoteBatch")]
        public async Task<ActionResult<int[]>> RestoreNoteBatch(int[] ids) =>
            await _clinicsService.RestoreNoteBatch(ids);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _clinicsService.DisableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _clinicsService.EnableBatch(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Marks clinics as ready for onboarding
        /// </summary>
        /// <param name="dtos"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>//
        [HttpPut("MarkAsReadyToOnboarding")]
        public async Task<int> MarkAsReadyToOnboarding(BaseUpdateDto[] dtos,
            CancellationToken cancellation) =>
            await _clinicsService.MarkAsReadyForOnboarding(dtos, cancellation: cancellation);

        /// <summary>
        /// Assign one IntegratorType to Clinic
        /// </summary>
        /// <param name="dtos"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>//
        [HttpPut("assignIntegratorType")]
        public async Task<int> AssignIntegratorType(AssignClinicIntegratorTypeDto[] dtos,
            CancellationToken cancellation) =>
            await _clinicsService.AssignIntegratorType(dtos, cancellation: cancellation);

        /// <summary>
        /// Get all the patients EMR sync records of some practitioner
        /// </summary>
        /// <param name="id">Practitioner ID</param>
        /// <param name="query">Sieve query</param>
        /// <param name="cancellation">Cancellation Token</param>
        /// <returns></returns>
        [HttpGet("PatientEMRSyncRecords/{id}")]
        public async Task<IPagedResults<PatientsEMRSyncRecordDto>> GetPatientsEMRSyncRecord(int id, [FromQuery] SieveModel query, CancellationToken cancellation)
            => await _patientEMRSyncRecordService.GetEMRPatientsByPractitionerId(id, query, cancellation);

    }
}
