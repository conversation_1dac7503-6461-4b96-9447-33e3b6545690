﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage;
using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Services.Reminder.Extensions;
using Aoshield.Services.ApiCall.Core.Extensions;
using Aoshield.Services.Core.Extensions;
using Aoshield.Services.Core.Identity;
using Aoshield.Services.Core.Identity.Extensions;
using Aoshield.Services.Core.Identity.Models;
using Aoshield.Services.Core.PowerBi;
using Aoshield.Services.Core.Search.Extensions;
using Aoshield.Services.Core.SharepointFileStorage;
using Aoshield.Services.EntityMerge.Core.Extensions;
using Aoshield.Services.EntityMerge.Core.Interfaces;
using Aoshield.Services.Export.Core.Extensions;
using Aoshield.Services.Import.Core.Extensions;
using Aoshield.Services.Messaging.ChatHub;
using Aoshield.Services.Messaging.Mediatr;
using Aoshield.Services.Messaging.RingCentral;
using Aoshield.Services.Notification.Core.Interfaces;
using Aoshield.Services.Notification.Core.Mappers;
using Aoshield.Services.Notification.Core.Models;
using Aoshield.Services.Notification.Core.Services;
using Aoshield.Services.Notification.Core.Validators;
using Aoshield.Services.ServicesBus.Extensions;
using Aoshield.Services.ServicesBus.PubSub;
using AutoMapper;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Database;
using Katana.Services.AzureAIIntegration;
using Katana.Services.Careplans;
using Katana.Services.Common.FaxesService.Messaging;
using Katana.Services.Common.Identity;
using Katana.Services.Common.Sieve;
using Katana.Services.EntityMerge.Profiles;
using Katana.Services.Export.Profiles.InvoiceItems;
using Katana.Services.OpenAI;
using Katana.Services.OpenAI.Messaging;
using Katana.Services.Tests.Extensions;
using Katana.Services.Tests.Services;
using Katana.Services.Tests.Services.Senders;
using Katana.Services.Users.Models;
using Katana.Services.Validators;
using Katana.Tests.Common.AppInsights;
using Katana.Tests.Common.NoKatana;
using Katana.Tests.Common.Utils;
using Katana.Tests.Common.Utils.Seeders;
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Microsoft.Graph;
using Microsoft.Kiota.Abstractions.Authentication;
using Moq;
using Xunit;
using DIConfigurator = Katana.Tests.Common.Utils.DIConfigurator;
using User = Katana.Core.Entities.User;

namespace Katana.Services.Tests.Common;

/// <summary>
/// Infraestructure for services test
/// </summary>
public class ServicesUnitTestConfiguration : IClassFixture<ConfigurationFixture>
{
    /// <summary>
    /// Whether to skip ClaimsPrincipalContext registration
    /// </summary>
    public virtual bool SkipClaimsPrincipalContext { get; set; }

    /// <summary>
    /// Member to implement singleton pattern for using Krudder
    /// </summary>
    private IKrudder<User> _krudder;

    /// <summary>
    /// ServiceProvider
    /// </summary>
    public IServiceProvider ServiceProvider { get; internal set; }

    /// <summary>
    /// Mapper
    /// </summary>
    public IMapper Mapper => ServiceProvider.GetService<IMapper>();

    /// <summary>
    /// SieveProcessor
    /// </summary>
    public ISieveProcessor SieveProcessor { get; }

    /// <summary>
    /// Fake Authenticated user
    /// </summary>
    public User User { get; private set; }

    /// <summary>
    /// KatanaTestDbContext
    /// </summary>
    public IKrudder<User> Krudder
    {
        get
        {
            _krudder ??= ServiceProvider.GetService<IKrudder<User>>();
            return _krudder;
        }
    }


    /// <summary>
    /// Public constructor
    /// </summary>
    public ServicesUnitTestConfiguration(ConfigurationFixture configurationFixture)
    {

        ServiceProvider = DIConfigurator.ConfigureServices(null, false, services =>
        {
            services.AddScoped<IOpenAIService, OpenAIServiceTest>();
            services.AddScoped<IAuthService, AuthServiceTests>();
            //Crud services
            services.AddCrudServicesFromAssemblyContaining<CareplansService>();
            services.AddCrudServicesFromAssemblyContaining<NotificationTemplateService>();

            //validators
            services.AddValidatorsFromAssemblyContaining<CareplanValidator>();
            services.AddKatanaCoreServices(configurationFixture.Configuration);
            services.AddScoped<IChatHub, ChatHubTests>();
            services.RegisterServiceBusQueues<User, SBIncomingFaxMessageProcessor>(configurationFixture.Configuration, null);
            services.AddScoped(provide => DIConfigurator.CreateSendeMoq<SBPendingProcessingFaxMessage>());
            services.AddScoped(provide => DIConfigurator.CreateSendeMoq<SBIncomingFaxMessage>());
            services.AddScoped(provide => DIConfigurator.CreateSendeMoq<SBNotificationMessage>());
            services.AddScoped<ISBSender<SBOpenAIMessage>, SBOpenAIMessageSenderTest>();
            services.AddScoped<IAzureFileStorageService, AzureFileStorageServiceTest>();
            services.AddScoped<SharepointStorage, SharepointStorageService>();
            services.AddSingleton(provider => new GraphServiceClient(new AnonymousAuthenticationProvider()));
            services.AddScoped<IMediatorPublisher, MediatorPublisherTests>();
            services.AddScoped<IPowerbiApiService, PowerbiApiServiceTest>();
            services.AddScoped<INotificationProfileService<CareplansActions>, NotificationCareplanProfileServiceTest>();
            services.AddFakesClassFromAssemblyContaining<ServicesUnitTestConfiguration>();
            // Sieve
            services.AddScoped<ISieveCustomSortMethods, SieveCustomSort>();
            services.AddScoped<ISieveCustomFilterMethods, SieveCustomFilter>();
            services.AddAosSieve(configurationFixture.Configuration);
            services.AddScoped(provider => new Mock<SBSender<SBExpireMessage>>());
            services.AddScoped(provider => new Mock<SBSender<SBNotificationMessage>>() as ISBSender<SBNotificationMessage>);
            services.AddScoped<IRingCentralClient, RingCentralClientTest>();
            services.AddSingleton(provider => new Mock<IConfiguration>().Object);
            services.AddFeatureManagement(configurationFixture.Configuration.GetSection("FeatureManagement"));
            services.AddTestDatabaseAndKrudder<KatanaDbContext, User>(options =>
            {
                var featureManager = ServiceProvider.GetService<IFeatureManager>();
                return new KatanaDbContext(options);
            });
            services.AddAosIdentityServices<User, UserDto, BaseAddUserDto, BaseUpdateUserDto, AuthServiceTests, AppRoles, AppResources, AppAuthType>(
                configurationFixture.Configuration);
            // Principals Service
            // TODO: remove from here after createing test registration for identity services
            services.AddScoped<IClaimsPrincipalContext<User>, ClaimsPrincipalContextTest<User>>(DIConfigurator.CreateClaimsPrincipalContextForTests);
            services.AddScoped<IClaimsPrincipalContext>(provider =>
            {
                return provider.GetRequiredService<IClaimsPrincipalContext<User>>();
            });
            services.AddAosSearchServices<User, AppResources, AppAuthType>(configurationFixture.Configuration, null);
            services.AddSingleton(provider => new Mock<IImpersonationService<User>>().Object);
            services.AddSingleton(provider => new Mock<INotificationProfileService<UsersActions>>().Object);
            services.AddValidatorsFromAssemblyContaining<NotificationTemplateValidator>();
            services.AddAosAutoMapper(typeof(NotificationAutomapper));
            services.AddAosAutoMapper(typeof(NotificationAutomapper));
            // Automapper
            services.AddScoped<INotificationsService, NotificationsService>();

            //Pdf Generator
            services.AddPdfGenerator();

            //Import profiles
            services.AddAosImportServices<CareplansService, User, AppResources, AppAuthType>(configurationFixture.Configuration);

            //Export profiles
            services.AddAosExportServices<InvoiceOCodeExportProfile, User, AppResources, AppAuthType>(configurationFixture.Configuration);

            //merge profiles
            services.AddAosMergeServices<PatientMergeProfile, Careplan, User, AppResources, AppAuthType>(configurationFixture.Configuration, null);
            //Overriding merge service
            services.AddScoped<IEntityMergeService, EntityMergeServiceTests>();

            services.AddScoped<ITemplateService, TemplateService>();

            services.AddAosApiCallServices<User, AppResources, AppAuthType>(configurationFixture.Configuration, null);

            services.AddScoped<IAzureAIIntegrationService, AzureAIIntegrationService>();

            services.AddAosReminders<User, AppResources, AppAuthType>(configurationFixture.Configuration);

            // AppInsights
            //
            // The TelemetryClient doesn't implement any interface. It's also a sealed class, so there's no
            // easy way to mock it out. The suggested solution is to implement a stub Telemetry Channel, which
            // is the component is charge of dispatching all telemetry data to the ingestion point.
            //
            var configuration = Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.CreateDefault();
            configuration.TelemetryChannel = new StubTelemetryChannel();

            services.AddSingleton(new TelemetryClient(configuration));
            services.AddScoped<TestDataSeeder>();
        });

        var testDataSeeder = ServiceProvider.GetService<TestDataSeeder>();
        testDataSeeder.Seed();
    }
}
