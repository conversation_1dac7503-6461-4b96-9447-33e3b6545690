import { DonutSmall } from "@mui/icons-material";
import AccountTreeIcon from "@mui/icons-material/AccountTree";
import AssessmentIcon from "@mui/icons-material/Assessment";
import AssignmentReturnedIcon from "@mui/icons-material/AssignmentReturned";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import CreateNewFolderIcon from "@mui/icons-material/CreateNewFolder";
import DescriptionIcon from "@mui/icons-material/Description";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import SettingsIcon from "@mui/icons-material/Settings";
import { AuthPermission, AuthResource } from "lib/auth/types";
import NotificationsActiveIcon from "@mui/icons-material/NotificationsActive";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import { MenuItem, menuKeys, pagesId, SubMenuKeys } from "../types";
import { urlsMap } from "./sitemap";
import { KATANA_EMPTY_FIELD } from "lib/utensils";

/**
 * Describe the app menu structure
 */
export const menuMap: MenuItem[] = [
    // CARE_PLANS | eCONSULTS
    {
        key: menuKeys.ECONSULTS,
        text: "_common:menu.econsults",
        icon: <CreateNewFolderIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.Careplans,
                role: [AuthPermission.Manage, AuthPermission.Process],
            },
            {
                resource: AuthResource.CPS_Careplans,
                role: [AuthPermission.Manage, AuthPermission.Process],
            },
            {
                resource: AuthResource.ClinicalQuestion,
                role: [AuthPermission.ReAsk],
            },
            {
                role: [AuthPermission.Read],
                resource: AuthResource.CareplanAttention,
            },
            { resource: AuthResource.ClinicalQuestion, role: [AuthPermission.ReadAll] },
            {
                resource: AuthResource.EConsultQualityReviewHPCDashboard,
                role: [AuthPermission.Read],
            },
            {
                resource: AuthResource.EConsultQualityReviewMVCADashboard,
                role: [AuthPermission.Read],
            },
        ],
        subItems: [
            {
                key: SubMenuKeys.CARE_PLANS,
                href: urlsMap[pagesId.CARE_PLANS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.carePlans",
                roleBasedProtected: [
                    {
                        role: [AuthPermission.Read],
                        resource: AuthResource.EConsultsAssignedTab,
                    },
                    {
                        role: [AuthPermission.Read],
                        resource: AuthResource.EConsultsUnansweredTab,
                    },
                ],
            },
            {
                key: SubMenuKeys.ECONSULTS_OPERATION_SUBMENU,
                href: urlsMap[pagesId.ECONSULTS_OPERATION].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.operation",
                roleBasedProtected: [
                    { resource: AuthResource.EConsultsOperationDashboard, role: [AuthPermission.Read] },
                ],
            },
            {
                key: SubMenuKeys.ECONSULTS_GP_SUBMENU,
                href: urlsMap[pagesId.ECONSULTS_GP].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.gp",
                roleBasedProtected: [
                    { resource: AuthResource.EConsultsGPDashboard, role: [AuthPermission.Read] },
                ],
            },
            {
                key: SubMenuKeys.ECONSULTS_ITP_SUBMENU,
                href: urlsMap[pagesId.ECONSULTS_ITP].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.itp",
                roleBasedProtected: [
                    { resource: AuthResource.EConsultsITPDashboard, role: [AuthPermission.Read] },
                ],
            },
            {
                key: SubMenuKeys.ECONSULTS_SP_SUBMENU,
                href: urlsMap[pagesId.ECONSULTS_SP].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.sp",
                roleBasedProtected: [
                    { resource: AuthResource.EConsultsSPDashboard, role: [AuthPermission.Read] },
                ],
            },
            {
                key: SubMenuKeys.CLINICAL_QUESTIONS_SUBMENU,
                href: urlsMap[pagesId.CLINICAL_QUESTIONS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.clinicalQuestions",
                roleBasedProtected: [
                    { resource: AuthResource.ClinicalQuestion, role: [AuthPermission.ReadAll] },
                ],
            },
            {
                key: SubMenuKeys.QUALITY_REVIEW_HPC_SUBMENU,
                href: urlsMap[pagesId.QUALITY_REVIEW_HPC].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.qualityReview.hpc",
                roleBasedProtected: [
                    {
                        resource: AuthResource.EConsultQualityReviewHPCDashboard,
                        role: [AuthPermission.Read],
                    },
                ],
            },
            {
                key: SubMenuKeys.QUALITY_REVIEW_MVCA_SUBMENU,
                href: urlsMap[pagesId.QUALITY_REVIEW_MVCA].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eConsults.qualityReview.mvca",
                roleBasedProtected: [
                    {
                        resource: AuthResource.EConsultQualityReviewMVCADashboard,
                        role: [AuthPermission.Read],
                    },
                ],
            },
        ],
    },
    // PATIENTS WITH MANAGE AND PANELING
    {
        key: menuKeys.PATIENTS,
        text: "_common:menu.patients",
        icon: <ContactMailIcon />,
        operator: "AND",
        roleBasedProtected: [
            {
                resource: AuthResource.Patients,
                role: [AuthPermission.Menu],
            },
            {
                resource: AuthResource.Paneling,
                role: [AuthPermission.Menu],
            },
        ],
        subItems: [
            {
                key: SubMenuKeys.PATIENTS_SUBMENU,
                href: urlsMap[pagesId.PATIENTS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.patients",
            },
            {
                key: SubMenuKeys.PATIENT_PANELING,
                href: urlsMap[pagesId.PATIENT_PANELING].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.patientPaneling",
            },
        ],
    },
    // PATIENTS WITH MANAGE WITHOUT PANELING
    {
        key: menuKeys.PATIENTS_MANAGE,
        text: "_common:menu.patients",
        icon: <ContactMailIcon />,
        href: urlsMap[pagesId.PATIENTS].href ?? KATANA_EMPTY_FIELD,
        roleBasedProtected: [
            {
                resource: AuthResource.Patients,
                role: [AuthPermission.Menu],
            },
        ],
        excludedRoles: [
            {
                resource: AuthResource.Paneling,
                role: [AuthPermission.Menu],
            },
        ],
    },
    // PATIENTS WITH PANELING WITHOUT MANAGER
    {
        key: menuKeys.PATIENTS_PANELING,
        text: "_common:menu.patients",
        icon: <ContactMailIcon />,
        href: urlsMap[pagesId.PATIENT_PANELING].href ?? KATANA_EMPTY_FIELD,
        roleBasedProtected: [
            {
                resource: AuthResource.Paneling,
                role: [AuthPermission.Menu],
            },
        ],
        excludedRoles: [
            {
                resource: AuthResource.Patients,
                role: [AuthPermission.Menu],
            },
        ],
    },
    //REMINDERS
    {
        key: menuKeys.REMINDERS,
        text: "_common:menu.reminders",
        icon: <NotificationsActiveIcon />,
        href: urlsMap[pagesId.REMINDERS].href ?? KATANA_EMPTY_FIELD,
        roleBasedProtected: [
            {
                resource: AuthResource.Reminders,
                role: [AuthPermission.Read],
            },
        ],
    },
    // REFERRALS
    {
        key: menuKeys.REFERRALS,
        text: "_common:menu.referrals",
        icon: <AssignmentReturnedIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.Referrals,
                role: [AuthPermission.Manage],
            },
        ],
        subItems: [
            {
                key: SubMenuKeys.REFERRALS_SUBMENU,
                href: urlsMap[pagesId.REFERRALS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.referrals",
            },
            {
                key: SubMenuKeys.REFERRALS_SPECIALISTS_SUBMENU,
                href: urlsMap[pagesId.REFERRALS_SPECIALISTS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.referralSpecialists",
            },
        ],
    },
    // BILLING
    {
        key: menuKeys.BILLING,
        text: "_common:menu.billing",
        icon: <MonetizationOnIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.Invoice,
                role: [AuthPermission.Process],
            },
            {
                resource: AuthResource.InvoiceItem,
                role: [AuthPermission.Process],
            },
        ],
        subItems: [
            {
                key: SubMenuKeys.INVOICES_SUBMENU,
                href: urlsMap[pagesId.INVOICES].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.invoices",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Invoice,
                        role: [AuthPermission.Process],
                    },
                ],
            },
            {
                key: SubMenuKeys.INVOICE_ITEMS_SUBMENU,
                href: urlsMap[pagesId.INVOICE_ALL_ITEMS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.invoiceItems",
                roleBasedProtected: [
                    {
                        resource: AuthResource.InvoiceItem,
                        role: [AuthPermission.Process],
                    },
                ],
            },
            {
                key: SubMenuKeys.BILLING_SETTINGS,
                href: urlsMap[pagesId.BILLING_SETTINGS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.billingSettings",
                roleBasedProtected: [
                    { resource: AuthResource.BillingSettings, role: [AuthPermission.Manage] },
                ],
            },
        ],
    },
    // FAXES
    {
        key: menuKeys.FAXES,
        text: "_common:menu.faxes.text",
        tooltip: "_common:menu.faxes.tooltip",
        href: urlsMap[pagesId.FAXES].href ?? KATANA_EMPTY_FIELD,
        icon: <DescriptionIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.Faxes,
                role: [
                    AuthPermission.Manage,
                    AuthPermission.AssignTranscribersGroup,
                    AuthPermission.AssignTranscriber,
                    AuthPermission.Process,
                ],
            },
        ],
    },
    // IMPORTS_EXPORTS
    {
        key: menuKeys.IMPORTS_EXPORTS,
        text: "_common:menu.importsExports",
        icon: <AccountTreeIcon />,
        subItems: [
            {
                key: SubMenuKeys.IMPORT_EVENTS_SUBMENU,
                href: urlsMap[pagesId.IMPORT_EVENTS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.importEvents",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Faxes,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            {
                key: SubMenuKeys.EXPORTS_SUBMENU,
                href: urlsMap[pagesId.EXPORTS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.exports",
            },
            {
                key: SubMenuKeys.BULK_VALIDATIONS_SUBMENU,
                href: urlsMap[pagesId.BULK_VALIDATIONS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.bulkValidations",
                roleBasedProtected: [
                    {
                        resource: AuthResource.BulkValidations,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            {
                key: SubMenuKeys.BULK_SEARCH_INDEX_BUILD_SUBMENU,
                href: urlsMap[pagesId.BULK_SEARCH_INDEX_BUILDS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.bulkSearchIndexBuilds",
                roleBasedProtected: [
                    {
                        resource: AuthResource.BulkSearchIndexBuilds,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            {
                key: SubMenuKeys.DEFERRED_REQUEST_SUBMENU,
                href: urlsMap[pagesId.DEFERRED_REQUEST].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.deferredRequests",
                roleBasedProtected: [
                    {
                        resource: AuthResource.DeferredRequests,
                        role: [AuthPermission.Read, AuthPermission.ReadAll],
                    },
                ],
            },
        ],
    },
    // REPORTING
    {
        key: menuKeys.REPORTING,
        text: "_common:menu.reporting",
        icon: <AssessmentIcon />,
        href: urlsMap[pagesId.REPORTS].href ?? KATANA_EMPTY_FIELD,
        roleBasedProtected: [
            {
                resource: AuthResource.Reports,
                role: [AuthPermission.ReadAll],
            },
            {
                resource: AuthResource.ReportsNpmetrics,
                role: [AuthPermission.Read],
            },
        ],
    },
    // SETTINGS
    {
        key: menuKeys.SETTINGS,
        text: "_common:menu.settings",
        icon: <SettingsIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.ResponseTemplate,
                role: [AuthPermission.Write],
            },
            {
                resource: AuthResource.Workflow,
                role: [AuthPermission.Manage],
            },
        ],
        subItems: [
            // TODO: Define if it will continue within the menuMap (INQUIRY_FORMS_SUBMENU)
            {
                key: SubMenuKeys.INQUIRY_FORMS_SUBMENU,
                href: urlsMap[pagesId.INQUIRY_FORMS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.inquiryForms",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Workflow,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            {
                key: SubMenuKeys.CARE_PLAN_RESPONSE_TEMPLATE_SUBMENU,
                href: urlsMap[pagesId.CARE_PLAN_RESPONSE_TEMPLATE].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.careplanResponseTemplates",
                roleBasedProtected: [
                    {
                        resource: AuthResource.ResponseTemplate,
                        role: [AuthPermission.Write],
                    },
                ],
            },
        ],
    },
    //NP_METRICS
    {
        key: menuKeys.NP_METRICS,
        text: "_common:menu.npMetrics",
        icon: <DonutSmall />,
        roleBasedProtected: [
            {
                resource: AuthResource.Nps,
                role: [AuthPermission.Manage],
            },
        ],
        subItems: [
            {
                key: SubMenuKeys.ECC_DAILY_METRICS_SUBMENU,
                href: urlsMap[pagesId.ECC_DAILY_METRICS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.eccDailyMetrics",
            },
        ],
    },
    // ADMIN
    {
        key: menuKeys.ADMIN,
        text: "_common:menu.admin",
        icon: <AdminPanelSettingsIcon />,
        roleBasedProtected: [
            {
                resource: AuthResource.Roles,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Users,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Careplans,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Impersonation,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Groups,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Practitioners,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Specialties,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.PatientPool,
                role: [AuthPermission.ReadAll, AuthPermission.Read],
            },
            {
                resource: AuthResource.Transcribers,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.TranscriberManagers,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Clinics,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.CPS_Careplans,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.GoalItems,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Referrals,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.EMRIntegration,
                role: [AuthPermission.Read],
            },
            {
                resource: AuthResource.Countries,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Provinces,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Notifications_Templates,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Accuro,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Merges,
                role: [AuthPermission.ReadAll],
            },
            {
                resource: AuthResource.Merges,
                role: [AuthPermission.Read],
            },
            {
                resource: AuthResource.DeadLetterMessages,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Treatmentplans,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Faxes,
                role: [AuthPermission.Process],
            },
            {
                resource: AuthResource.Treatmentplans,
                role: [AuthPermission.Process],
            },
            {
                resource: AuthResource.AiPrompts,
                role: [AuthPermission.Manage],
            },
            {
                resource: AuthResource.Reports,
                role: [AuthPermission.Manage],
            },
        ],
        subItems: [
            //MANAGE_USERS_SUBMENU
            {
                key: SubMenuKeys.MANAGE_USERS_SUBMENU,
                href: "",
                text: "_common:submenu.manageUsers",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Roles,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Users,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Careplans,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Impersonation,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Groups,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Practitioners,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.PractitionersMine,
                        role: [AuthPermission.Read],
                    },
                    {
                        resource: AuthResource.PatientPool,
                        role: [AuthPermission.ReadAll, AuthPermission.Read],
                    },
                    {
                        resource: AuthResource.Transcribers,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.TranscriberManagers,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.ROLES_SUBMENU,
                        href: urlsMap[pagesId.ROLES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.roles",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Roles,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.USERS_SUBMENU,
                        href: urlsMap[pagesId.USERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.users",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Users,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.DEFERRED_USERINVITES_SUBMENU,
                        href: urlsMap[pagesId.USER_INVITES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.userInvites",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.AUTHORIZED_USERS,
                        href: urlsMap[pagesId.AUTHORIZED_USERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.authorizedUsers",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Impersonation,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.GROUPS_SUBMENU,
                        href: urlsMap[pagesId.GROUPS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.userGroups",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Groups,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.PRACTITIONERS_SUBMENU,
                        href: urlsMap[pagesId.PRACTITIONERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.practitioners",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Practitioners,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.HPCs_SUBMENU,
                        href: urlsMap[pagesId.HPCS].href ?? "",
                        text: "_common:submenu.hpcPractitioners",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.PractitionersMine,
                                role: [AuthPermission.Read],
                            },
                        ],
                        excludedRoles: [
                            { resource: AuthResource.Practitioners, role: [AuthPermission.Manage] },
                        ],
                    },
                    {
                        key: SubMenuKeys.PATIENT_POOLS_SUBMENU,
                        href: urlsMap[pagesId.PATIENT_POOLS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.patientPools",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.PatientPool,
                                role: [AuthPermission.ReadAll, AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.TRANSCRIBER_SUBMENU,
                        href: urlsMap[pagesId.TRANSCRIBER].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.transcribers",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Transcribers,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.TRANSCRIBER_MANAGERS_SUBMENU,
                        href: urlsMap[pagesId.TRANSCRIBER_MANAGERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.managers",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.TranscriberManagers,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.TRANSCRIBER_GROUPS_SUBMENU,
                        href: urlsMap[pagesId.TRANSCRIBER_GROUPS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.transcribersGroups",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.TranscriberManagers,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //CLINICS_SUBMENU
            {
                key: SubMenuKeys.CLINICS_SUBMENU,
                href: urlsMap[pagesId.CLINICS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.clinics",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Clinics,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            //DIAGNOSES_SETTINGS
            {
                key: SubMenuKeys.DIAGNOSES_SETTINGS,
                href: "",
                text: "_common:submenu.diagnosesSettings",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Careplans,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.DIAGNOSES_SUBMENU,
                        href: urlsMap[pagesId.DIAGNOSES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.diagnoses",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.DIAGNOSES_ALIASES_SUBMENU,
                        href: urlsMap[pagesId.DIAGNOSES_ALIASES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.diagnosesAliases",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //ECONSULT_SETTINGS
            {
                key: SubMenuKeys.ECONSULT_SETTINGS,
                href: "",
                text: "_common:submenu.eConsultSettings",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Careplans,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.CPS_Careplans,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Specialties,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.DeclineReasons,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.EDUCATIONAL_CARE_PLANS_SUBMENU,
                        href: urlsMap[pagesId.EDUCATIONAL_CARE_PLANS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.educationalCarePlans",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                            {
                                resource: AuthResource.CPS_Careplans,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.SPECIALTIES_SUBMENU,
                        href: urlsMap[pagesId.SPECIALTIES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.specialties",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Specialties,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.DECLINE_REASON_SUBMENU,
                        href: urlsMap[pagesId.DECLINE_REASON].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.declineReasons",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.DeclineReasons,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //PATIENT_CHART_SETTINGS
            {
                key: SubMenuKeys.PATIENT_CHART_SETTINGS,
                href: "",
                text: "_common:submenu.patientChartSettings",
                roleBasedProtected: [
                    {
                        resource: AuthResource.GoalItems,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Careplans,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Referrals,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.EMRIntegration,
                        role: [AuthPermission.Read],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.GOAL_ITEMS_SUBMENU,
                        href: urlsMap[pagesId.GOAL_ITEMS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.goalItems",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.GoalItems,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.UNITS_OF_MEASUREMENT_SUBMENU,
                        href: urlsMap[pagesId.UNITS_OF_MEASUREMENT].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.unitsOfMeasurement",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                            {
                                resource: AuthResource.Referrals,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.VITAL_SIGNS_SUBMENU,
                        href: urlsMap[pagesId.VITAL_SIGNS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.vitalSigns",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                            {
                                resource: AuthResource.Referrals,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.ALLERGIES_SUBMENU,
                        href: urlsMap[pagesId.ALLERGIES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.allergies",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Allergies,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.LAB_TESTS_SUBMENU,
                        href: urlsMap[pagesId.LAB_TESTS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.labTests",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.EMRIntegration,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.SOCIAL_HISTORIES,
                        href: urlsMap[pagesId.SOCIAL_HISTORIES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.socialHistories",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Careplans,
                                role: [AuthPermission.Manage],
                            },
                            {
                                resource: AuthResource.Referrals,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.PATIENT_HISTORY_TYPES_SUBMENU,
                        href: urlsMap[pagesId.PATIENTS_HISTORY_TYPE].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.historyTypes",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.EMRIntegration,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.PATIENT_STATUSES_SUBMENU,
                        href: urlsMap[pagesId.PATIENT_STATUSES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.patientStatuses",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.EMRIntegration,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.FLAGS_SUBMENU,
                        href: urlsMap[pagesId.FLAGS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.flags",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.EMRIntegration,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.FOLDERS_SUBMENU,
                        href: urlsMap[pagesId.FOLDERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.folders",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.EMRIntegration,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.GROUP_ITEMS,
                        href: urlsMap[pagesId.GROUP_ITEMS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.groupItems",
                        roleBasedProtected: [
                            { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
                        ],
                    },
                    {
                        key: SubMenuKeys.GROUP_LAB_TESTS,
                        href: urlsMap[pagesId.GROUP_LAB_TESTS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.groupLabTests",
                        roleBasedProtected: [
                            { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
                        ],
                    },
                    {
                        key: SubMenuKeys.GROUP_FOLDERS,
                        href: urlsMap[pagesId.GROUP_FOLDERS].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.groupFolders",
                        roleBasedProtected: [
                            { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
                        ],
                    },
                ],
            },
            //LOCATIONS
            {
                key: SubMenuKeys.LOCATIONS,
                href: "",
                text: "_common:submenu.locations",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Countries,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Provinces,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.COUNTRIES_SUBMENU,
                        href: urlsMap[pagesId.COUNTRIES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.countries",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Countries,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.PROVINCES_SUBMENU,
                        href: urlsMap[pagesId.PROVINCES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.provinces",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Provinces,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //TEMPLATES
            {
                key: SubMenuKeys.TEMPLATES,
                href: "",
                text: "_common:submenu.templates",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Notifications_Templates,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.NOTIFICATIONS_TEMPLATES_SUBMENU,
                        href: urlsMap[pagesId.NOTIFICATIONS_TEMPLATES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.notificationTemplates",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Notifications_Templates,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //INTEGRATIONS
            {
                key: SubMenuKeys.INTEGRATIONS,
                href: "",
                text: "_common:submenu.integrations",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Accuro,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.ACCURO_SUBMENU,
                        href: urlsMap[pagesId.ACCURO].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.accuro",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Accuro,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //MERGE_DATA
            {
                key: SubMenuKeys.MERGE_DATA,
                href: "",
                text: "_common:submenu.mergeData",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Merges,
                        role: [AuthPermission.ReadAll],
                    },
                    {
                        resource: AuthResource.Merges,
                        role: [AuthPermission.Read],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.DEFERRED_All_MERGES_SUBMENU,
                        href: urlsMap[pagesId.DEFERRED_All_MERGES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.allDeferredMerges",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Merges,
                                role: [AuthPermission.ReadAll],
                            },
                        ],
                    },
                    {
                        key: SubMenuKeys.DEFERRED_MERGES_SUBMENU,
                        href: urlsMap[pagesId.DEFERRED_MERGES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.mineDeferredMerges",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.Merges,
                                role: [AuthPermission.Read],
                            },
                        ],
                    },
                ],
            },
            //DEAD_LETTER_MESSAGES
            {
                key: SubMenuKeys.DEAD_LETTER_MESSAGES,
                href: "",
                text: "_common:submenu.deadLetterMessages",
                roleBasedProtected: [
                    {
                        resource: AuthResource.DeadLetterMessages,
                        role: [AuthPermission.Manage],
                    },
                ],
                subItems: [
                    {
                        key: SubMenuKeys.DEAD_LETTER_MESSAGES_SUBMENU,
                        href: urlsMap[pagesId.DEAD_LETTER_MESSAGES].href ?? KATANA_EMPTY_FIELD,
                        text: "_common:submenu.deadLetterMessages",
                        roleBasedProtected: [
                            {
                                resource: AuthResource.DeadLetterMessages,
                                role: [AuthPermission.Manage],
                            },
                        ],
                    },
                ],
            },
            //REPORT_MANAGEMENT
            {
                key: SubMenuKeys.REPORTS_SUBMENU,
                text: "_common:submenu.reports",
                href: urlsMap[pagesId.REPORTS_SETTINGS].href ?? KATANA_EMPTY_FIELD,
                roleBasedProtected: [
                    {
                        resource: AuthResource.Reports,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            //AI Prompts
            {
                key: SubMenuKeys.AI_PROMPT_DEFINITIONS_SUBMENU,
                href: urlsMap[pagesId.AI_PROMPT_DEFINITIONS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.aiPromptDefinition",
                roleBasedProtected: [
                    {
                        resource: AuthResource.AiPrompts,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            //CHANGELOGS_SUBMENU
            {
                key: SubMenuKeys.CHANGELOGS_SUBMENU,
                href: urlsMap[pagesId.ENTITY_CHANGE_LOGS].href ?? KATANA_EMPTY_FIELD,
                text: "_common:submenu.changesLogs",
                roleBasedProtected: [
                    {
                        resource: AuthResource.Audit,
                        role: [AuthPermission.Manage],
                    },
                ],
            },
            //HPU
            {
                key: SubMenuKeys.HPUs_SUBMENU,
                text: "_common:submenu.hpus",
                href: urlsMap[pagesId.HPUS].href ?? KATANA_EMPTY_FIELD,
                roleBasedProtected: [
                    {
                        resource: AuthResource.Treatmentplans,
                        role: [AuthPermission.Manage],
                    },
                    {
                        resource: AuthResource.Faxes,
                        role: [AuthPermission.Process],
                    },
                    {
                        resource: AuthResource.Treatmentplans,
                        role: [AuthPermission.Process],
                    },
                ],
            },
        ],
    },
];
