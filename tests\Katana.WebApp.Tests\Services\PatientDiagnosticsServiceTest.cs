﻿using Aoshield.Core.Entities.Models;
using Katana.Services.PatientDiagnostics;
using Katana.Services.PatientDiagnostics.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Mock PatientDiagnostic CRUD service
    /// </summary>
    public class PatientDiagnosticsServiceTest : IPatientDiagnosticsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientDiagnosticDto> Add(AddPatientDiagnosticDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientDiagnosticDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientDiagnosticDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<PatientDiagnosticDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientDiagnosticDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new PatientDiagnosticDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdatePatientDiagnosticDto> Update(UpdatePatientDiagnosticDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientDiagnosticDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Custom

        /// <summary>
        /// For retrieving all Diagnostics that belongs to an Patient
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<PatientDiagnosticDto>> GetDiagnosticsByPatientId(
            int patientId, SieveModel query) =>
            await Task.Run(() =>
                new PagedResults<PatientDiagnosticDto>([], default,
                    default, default, default, default, default, default, default));

        #endregion
    }
}
