using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage;
using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Services.ApiCall.Core.Extensions;
using Aoshield.Services.Core.Extensions;
using Aoshield.Services.Core.Identity;
using Aoshield.Services.Core.Identity.Extensions;
using Aoshield.Services.Core.Identity.Models;
using Aoshield.Services.Core.Search.Extensions;
using Aoshield.Services.Core.SharepointFileStorage;
using Aoshield.Services.Core.Validation.Extensions;
using Aoshield.Services.EntityMerge.Core.Interfaces;
using Aoshield.Services.Import.Core.Extensions;
using Aoshield.Services.Import.Core.Interfaces;
using Aoshield.Services.Messaging.Mediatr;
using Aoshield.Services.Messaging.RingCentral;
using Aoshield.Services.Notification.Core.Extensions;
using Aoshield.Services.Notification.Core.Interfaces;
using Aoshield.Services.ServicesBus.PubSub;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Database;
using Katana.Services.Careplans;
using Katana.Services.Common.FaxesService.Messaging;
using Katana.Services.Common.Identity;
using Katana.Services.OpenAI.Messaging;
using Katana.Services.Templates.Profiles;
using Katana.Services.Tests.Services;
using Katana.Services.Tests.Services.Senders;
using Katana.Services.Users.Models;
using Katana.Services.Validators;
using Katana.Tests.Common.AppInsights;
using Katana.Tests.Common.NoKatana;
using Katana.Tests.Common.Utils;
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Microsoft.Graph;
using Moq;
using Xunit;

namespace Katana.Services.Tests.Import
{
    /// <summary>
    /// Tests for Careplan entity
    /// </summary>
    /// <remarks>
    /// Public constructor receiving output
    /// </remarks>
    /// <param name="configurationFixture"></param>
    public class ImportsServiceUnitTest(ConfigurationFixture configurationFixture) : IClassFixture<ConfigurationFixture>
    {
        private readonly IServiceProvider _serviceProvider = DIConfigurator.ConfigureServices(null, extraActions: services =>
            {
                //Crud services
                services.AddCrudServicesFromAssemblyContaining<CareplansService>();

                //Validation services
                services.AddCheckStatusValidationServices<CareplansService, User>(configurationFixture.Configuration.GetSection("ValidationStatusManager"), null);

                //validators
                services.AddValidatorsFromAssemblyContaining<CareplanValidator>();
                services.AddKatanaCoreServices(configurationFixture.Configuration);
                services.AddAosApiCallServices<User, AppResources, AppAuthType>(configurationFixture.Configuration, null);
                services.AddAosIdentityServices<User, UserDto, BaseAddUserDto, BaseUpdateUserDto, AuthServiceTests, AppRoles, AppResources, AppAuthType>(
                    configurationFixture.Configuration);
                services.AddSingleton(provider => new Mock<IImpersonationService<User>>().Object);
                services.AddAosNotifications<User, CareplanTemplateProfile, AppResources, AppAuthType>(configurationFixture.Configuration, null);
                services.AddSingleton(provider => new Mock<INotificationProfileService<UsersActions>>().Object);
                services.AddSingleton(provider => new Mock<INotificationProfileService<CareplansActions>>().Object);
                services.AddScoped<IAzureFileStorageService, AzureFileStorageServiceTest>();
                services.AddScoped<IMediatorPublisher, MediatorPublisherTests>();
                services.AddFeatureManagement(configurationFixture.Configuration.GetSection("FeatureManagement"));
                services.AddScoped<IEntityMergeService, EntityMergeServiceTests>();
                services.AddTestDatabaseAndKrudder<KatanaDbContext, User>(options =>
                {
                    var featureManager = services.BuildServiceProvider().GetService<IFeatureManager>();
                    return new KatanaDbContext(options);
                });
                services.AddAosSearchServices<User, AppResources, AppAuthType>(configurationFixture.Configuration, null);
                services.AddScoped(provide => DIConfigurator.CreateSendeMoq<SBPendingProcessingFaxMessage>());
                services.AddScoped(provide => DIConfigurator.CreateSendeMoq<SBIncomingFaxMessage>());
                services.AddScoped<SharepointStorage, SharepointStorageService>();
                services.AddSingleton(provider => new Mock<GraphServiceClient>().Object);
                services.AddScoped<IRingCentralClient, RingCentralClientTest>();
                services.AddScoped<IAuthService, AuthServiceTests>();
                services.AddScoped<ISBSender<SBOpenAIMessage>, SBOpenAIMessageSenderTest>();
                services.AddPdfGenerator();
                // Sieve
                services.AddAosSieve(configurationFixture.Configuration);
                //Import profiles
                services.AddAosImportServices<CareplansService, User, AppResources, AppAuthType>(configurationFixture.Configuration);
                // Principals Service
                services.AddScoped<IClaimsPrincipalContext>(provider =>
                {
                    return provider.GetRequiredService<IClaimsPrincipalContext<User>>();
                });
                var configuration = Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.CreateDefault();
                configuration.TelemetryChannel = new StubTelemetryChannel();
                services.AddSingleton(new TelemetryClient(configuration));
            });
        private IKrudder _krudder;

        /// <summary>
        /// KatanaTestDbContext
        /// </summary>
        public IKrudder Krudder
        {
            get
            {
                _krudder ??= _serviceProvider.GetService<IKrudder>();
                return _krudder;
            }
        }

        //private async Task CreateCountriesAndProvinces()
        //{
        //    var country = new Country()
        //    {
        //        Name = "Canada"
        //    };
        //    await Krudder.Add(country, null);
        //    var province = new Province()
        //    {
        //        Name = "Alberta",
        //        Abbrev = "AB",
        //        Country = country
        //    };
        //    await Krudder.Add(province, null);
        //    province = new Province()
        //    {
        //        Name = "British Columbia",
        //        Abbrev = "BC",
        //        Country = country
        //    };
        //    await Krudder.Add(province, null);
        //}

        private static int ImportItemsCount(IImportProfile importProfile)
        {
            var counter = 1;
            foreach (var relation in importProfile.Relations)
            {
                counter += ImportItemsCount(relation);
            }
            return counter;
        }

        /// <summary>
        /// Test for profiles functionality
        /// </summary>
        /// <returns></returns>
        [Fact]
        public void Test_Profiles()
        {
            var importsHandlerService = _serviceProvider.GetService<IImportService>();

            var allProfiles = importsHandlerService.GetProfilesAsPagedResults(new SieveModel());
            Assert.NotEmpty(allProfiles.Items);
        }

        //private async Task Test_Import_Master_Base(string fileName, Type profileType)
        //{
        //    if (Krudder == null) //we make sure db is created (ensurecreated)
        //    {
        //        return;
        //    }

        //    var importsHandlerService = _serviceProvider.GetService<IImportService>();

        //    var filePath = $"../../../Import/Files/{fileName}";
        //    var fileStream = File.OpenRead(filePath); // or
        //    var ms = new MemoryStream();
        //    fileStream.CopyTo(ms);

        //    var importEvent = await importsHandlerService.Enqueue(ms, profileType.Name, "TestSource", true, null, default);
        //    var importProfile = (_serviceProvider.GetService(profileType) as IImportProfileBuilder).Build();

        //    //import event
        //    Assert.True(Krudder.Set<ImportEvent>().Count().Equals(1));

        //    //import items
        //    var importItemsCount = ImportItemsCount(importProfile);
        //    var importItems = Krudder.Set<ImportItem>().Where(i => i.ImportEventId.Equals(importEvent.Id));
        //    Assert.True(importItems.Count().Equals(importEvent.FailedItems * importItemsCount)); //1 for parent import item and one per relation
        //}
    }
}
