﻿using Aoshield.Core.Entities.Models;
using Katana.Services.PatientVitalSigns;
using Katana.Services.PatientVitalSigns.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// PatientVitalSign CRUD service
    /// </summary>
    public class PatientVitalSignsServiceTest : IPatientVitalSignsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientVitalSignDto> Add(AddPatientVitalSignDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientVitalSignDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientVitalSignDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<PatientVitalSignDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientVitalSignDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new PatientVitalSignDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdatePatientVitalSignDto> Update(UpdatePatientVitalSignDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientVitalSignDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        #endregion

        #region Custom

        /// <summary>
        /// For retrieve all VitalSigns that belongs to an Patient
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<PatientVitalSignDto>> GetVitalSignsByPatientId(
            int patientId, SieveModel query) =>
            await Task.Run(() =>
                new PagedResults<PatientVitalSignDto>([], default,
                    default, default, default, default, default, default, default));

        #endregion
    }
}
