export enum ExportEntityKeys {
    AllergyDto = "AllergyDto",
    CareplanResponseTemplateDto = "CareplanResponseTemplateDto",
    CareplanListDto = "CareplanListDto",
    ClinicDto = "ClinicDto",
    ClinicPractitionerDto = "ClinicPractitionerDto",
    DiagnosticDto = "DiagnosticDto",
    PractitionerDiagnosticDto = "PractitionerDiagnosticDto",
    EccDailyProcessedMetricDto = "EccDailyProcessedMetricDto",
    FaxDto = "FaxDto",
    FaxListDto = "FaxListDto",
    PatientDto = "PatientDto",
    ImportEventDto = "ImportEventDto",
    InvoiceDto = "InvoiceDto",
    MedicationDto = "MedicationDto",
    PractitionerDto = "PractitionerDto",
    PractitionerSpecialtyDto = "PractitionerSpecialtyDto",
    SpecialtyPractitionerDto = "SpecialtyPractitionerDto",
    SpecialtyDiagnosticDto = "SpecialtyDiagnosticDto",
    HpcAssignedPractitionerDto = "HpcAssignedPractitionerDto",
    ReferralDto = "ReferralDto",
    ReferralSpecialistDto = "ReferralSpecialistDto",
    ReferralSpecialtyDto = "ReferralSpecialtyDto",
    SocialHistoryDto = "SocialHistoryDto",
    TranscribersGroupDto = "TranscribersGroupDto",
    TranscribersManagerDto = "TranscribersManagerDto",
    TranscriberDto = "TranscriberDto",
    UnitOfMeasurementDto = "UnitOfMeasurementDto",
    UserDto = "UserDto",
    VitalSignDto = "VitalSignDto",
    TreatmentplanListDto = "TreatmentplanListDto",
    EducationalCareplanDto = "EducationalCareplanDto",
    ImpersonationDto = "ImpersonationDto",
    GroupDto = "GroupDto",
    GroupUserDto = "GroupUserDto",
    GroupMemberDto = "GroupMemberDto",
    InquiryFormDto = "InquiryFormDto",
    InquiryFormStepDto = "InquiryFormStepDto",
    InquiryFormQuestionDto = "InquiryFormQuestionDto",
    BillingSettingsDto = "BillingSettingsDto",
    CountryDto = "CountryDto",
    ProvinceDto = "ProvinceDto",
    InvoiceItemDto = "InvoiceItemDto",
    DiagnosticAliasDto = "DiagnosticAliasDto",
    BulkValidationListDto = "BulkValidationListDto",
    BulkSearchIndexBuildListDto = "BulkSearchIndexBuildListDto",
    PatientLabTestDto = "PatientLabTestDto",
    PatientHistoryItemDto = "PatientHistoryItemDto",
    LabTestDto = "LabTestDto",
    PatientMedicationDto = "PatientMedicationDto",
    PatientAllergyDto = "PatientAllergyDto",
    PatientGeneratedLetterListDto = "PatientGeneratedLetterListDto",
    FolderDto = "FolderDto",
    ClinicalQuestionDto = "ClinicalQuestionDto",
    PatientDocumentFolderDto = "PatientDocumentFolderDto",
    GoalItemDto = "GoalItemDto",
    PatientGoalDto = "PatientGoalDto",
    PatientPoolDto = "PatientPoolDto",
    ReminderDto = "ReminderDto",
    PatientFlagDto = "PatientFlagDto",
    RoleDto = "RoleDto",
    ReportDto = "ReportDto",
    DeclineReasonDto = "DeclineReasonDto",
    AiPromptDefinitionDto = "AiPromptDefinitionDto",
    ListEntityChangeLogDto = "ListEntityChangeLogDto",
}
