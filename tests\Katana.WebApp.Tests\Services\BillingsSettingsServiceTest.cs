﻿using Aoshield.Core.Entities.Models;
using Katana.Services.BillingSettings;
using Katana.Services.BillingSettings.Models;

namespace Katana.WebApp.Tests.Services;

/// <summary>
///     BillingsSettings Mock CRUD service
/// </summary>
public class BillingsSettingsServiceTest : IBillingSettingsService
{
    /// <inheritdoc />
    public async Task<AddBillingSettingsDto> Add(AddBillingSettingsDto dto,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new AddBillingSettingsDto(), cancellation);

    /// <inheritdoc />
    public async Task<BillingSettingsDto> GetById(int id,
        CancellationToken cancellation = default) => await Task.Run(() =>
        id <= 0 ? null : new BillingSettingsDto { Id = id });

    /// <inheritdoc />
    public async Task<IPagedResults<BillingSettingsListDto>> GetAsPagedResults(SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new PagedResults<BillingSettingsListDto>(
            [], default,
            default, default, default, default, default, default, default));

    /// <inheritdoc />
    public async Task<UpdateBillingSettingsDto> Update(UpdateBillingSettingsDto dto,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new UpdateBillingSettingsDto { Id = dto.Id });

    /// <inheritdoc />
    public async Task<int?> Delete(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    /// <inheritdoc />
    public async Task<int?> Restore(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    ///<inheritdoc/>
    public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());
}
