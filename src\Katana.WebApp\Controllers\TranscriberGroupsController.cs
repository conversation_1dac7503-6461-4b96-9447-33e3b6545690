﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Katana.Services.Faxes;
using Katana.Services.Faxes.Models;
using Katana.Services.TranscriberGroupConfigs.Models;
using Katana.Services.TranscriberGroups;
using Katana.Services.TranscriberGroups.Models;
using Katana.Services.Transcribers;
using Katana.Services.Transcribers.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// TranscribersGroup Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class TranscriberGroupsController : ControllerBase
    {
        private readonly ITranscriberGroupsService _transcriberGroupsService;
        private readonly ITranscribersService _transcribersService;
        private readonly IFaxesService _faxesService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="crudService">CRUD service</param>
        /// <param name="transcribersService">Transcribers Service</param>
        /// <param name="faxesService">Faxes service</param>
        public TranscriberGroupsController(
            ITranscriberGroupsService crudService,
            ITranscribersService transcribersService,
            IFaxesService faxesService)
        {
            _transcriberGroupsService = crudService;
            _transcribersService = transcribersService;
            _faxesService = faxesService;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<TranscribersGroupDto>> GetTranscriberGroups(
            [FromQuery] SieveModel query) =>
            await _transcriberGroupsService.GetAsPagedResults(query);

        /// <summary>
        /// Get entity by id
        /// </summary>
        /// <param name="id">Id of the entity</param>
        /// <returns>Entity with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<TranscribersGroupDto>> GetTranscriberGroup(int id)
        {
            var dto = await _transcriberGroupsService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="transcriberGroup">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateTranscribersGroupDto>> PutTranscriberGroup(int id,
            UpdateTranscribersGroupDto transcriberGroup, CancellationToken cancellation) =>
            id != transcriberGroup.Id
                ? BadRequest()
                : await _transcriberGroupsService.Update(transcriberGroup,
                    cancellation: cancellation);

        /// <summary>
        /// Add entity
        /// </summary>
        /// <param name="transcriberGroup">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost]
        public async Task<ActionResult<AddTranscribersGroupDto>> PostTranscriberGroup(
            AddTranscribersGroupDto transcriberGroup, CancellationToken cancellation)
        {
            var dto = await _transcriberGroupsService.Add(transcriberGroup,
                cancellation: cancellation);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeleteTranscriberGroup(int id,
            CancellationToken cancellation = default) =>
            await _transcriberGroupsService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcriberGroupsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcriberGroupsService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcriberGroupsService.DisableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _transcriberGroupsService.EnableBatch(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestoreTranscriberGroup(int id) =>
            await _transcriberGroupsService.Restore(id, notify: false);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">TranscriberGroup's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLog(int id,
            [FromQuery] SieveModel query) =>
            await _transcriberGroupsService.GetStatusLogsAsPagedResults(id, query);

        #region Transcribers Group Configuration

        /// <summary>
        /// For retrieving Group Fax Types assigned to a TranscriberGroup
        /// </summary>
        /// <returns>List of TranscribersGroupConfig (Configurations) </returns>
        [HttpGet("{id}/Configurations")]
        public async Task<IPagedResults<TranscribersGroupConfigDto>> GetGroupConfiguration(int id,
            [FromQuery] SieveModel query) =>
            await _transcriberGroupsService.GetConfigByGroupId(id, query);

        #endregion

        #region Transcribers Group - Transcribers

        /// <summary>
        /// For retrieving Group Fax Types assigned to a TranscriberGroup
        /// </summary>
        /// <returns>List of TranscribersGroupConfig (Configurations) </returns>
        [HttpGet("{id}/Transcribers")]
        public async Task<IPagedResults<TranscriberDto>> GetTranscribersByGroupId(int id,
            [FromQuery] SieveModel query) =>
            await _transcribersService.GetTranscribersByGroupId(id, query);

        #endregion

        #region Transcribers Group - Faxes

        /// <summary>
        /// Get assigned faxes
        /// </summary>
        /// <param name="id">Transcriber Group Id</param>
        /// <param name="query">Sieve model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Faxes list</returns>
        // GET: api/Faxes
        [HttpGet("{id}/Faxes")]
        //[EnableQuery]
        public async Task<IPagedResults<FaxListDto>> GetAssignedFaxes(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _faxesService.GetFaxesByTranscribersGroup(id, query, cancellation);

        #endregion
    }
}
