export enum ReactQueryKeys {
    Treatmentplan = "Treatmentplan",
    Careplan = "Careplan",
    Fax = "Fax",
    Referral = "Referral",
    ReferralSpecialty = "ReferralSpecialty",
    SpecialtyDiagnostic = "SpecialtyDiagnostic",
    ReferralSpecialist = "ReferralSpecialist",
    Patient = "Patient",
    Clinic = "Clinic",
    Practitioner = "Practitioner",
    PractitionerSpecialty = "PractitionerSpecialty",
    PractitionerDiagnostic = "PractitionerDiagnostic",
    PractitionerHPC = "PractitionerHPC",
    ClinicPractitioner = "ClinicPractitioner",
    Invoice = "Invoice",
    InvoiceItem = "InvoiceItem",
    UserInquiryForm = "UserInquiryForm",
    UserInquiryFormQuestion = "UserInquiryFormQuestion",
    InquiryForm = "InquiryForm",
    InquiryFormStep = "InquiryFormStep",
    InquiryFormQuestion = "InquiryFormQuestion",
    Country = "Country",
    Province = "Province",
    User = "User",
    Transcriber = "Transcriber",
    TranscribersGroup = "TranscribersGroup",
    TranscribersManager = "TranscribersManager",
    TranscribersGroupConfig = "TranscribersGroupConfig",
    Diagnostic = "Diagnostic",
    EducationalCareplan = "EducationalCareplan",
    Medication = "Medication",
    VitalSign = "VitalSign",
    SocialHistory = "SocialHistory",
    PatientMedication = "PatientMedication",
    PatientSocialHistory = "PatientSocialHistory",
    PatientDiagnostic = "PatientDiagnostic",
    PatientVitalSign = "PatientVitalSign",
    PatientGeneratedLetter = "PatientGeneratedLetter",
    Allergy = "Allergy",
    PatientAllergy = "PatientAllergy",
    PatientFlag = "PatientFlag",
    UnitOfMeasurement = "UnitOfMeasurement",
    NotificationTemplate = "NotificationTemplate",
    ExportProfile = "ExportProfile",
    ImportItem = "ImportItem",
    DeferredRequest = "DeferredRequest",
    DeferredMerge = "DeferredMerge",
    // Proper keys
    Accuro = "Accuro",
    AppSettings = "AppSettings",
    CurrentUser = "CurrentUser",
    FaxAutoAssignPlans = "FaxAutoAssignPlans",
    FaxNotes = "FaxNotes",
    ImportEvents = "ImportEvents",
    ImportItemDetail = "ImportItemDetail",
    ImportItemDetails = "ImportItemDetails",
    ImportProfiles = "ImportProfiles",
    CareplanResponseTemplate = "CareplanResponseTemplate",
    ClinicAvailablePractitioners = "ClinicAvailablePractitioners",
    ClinicNotes = "ClinicNotes",
    CareplanWorkflowStatus = "CareplanWorkflowStatus",
    PractitionerNotes = "PractitionerNotes",
    PatientNotes = "PatientNotes",
    DiagnosticAliases = "DiagnosticAliases",
    CareplanNotes = "CareplanNotes",
    ReferralSpecialistsNotes = "ReferralSpecialistsNotes",
    ReferralNotes = "ReferralNotes",
    ReferralWorkflowStatus = "ReferralWorkflowStatus",
    InvoiceAttachments = "InvoiceAttachments",
    InvoiceNotes = "InvoiceNotes",
    InvoiceWorkflowStatus = "InvoiceWorkflowStatus",
    InvoiceItemsNotes = "InvoiceItemsNotes",
    Exports = "Exports",
    TreatmentplanWorkflowStatus = "TreatmentplanWorkflowStatus",
    TreatmentplanNotes = "TreatmentplanNotes",
    EccDailyProcessedMetrics = "EccDailyProcessedMetrics",
    CareplanAttachments = "CareplanAttachments",
    EducationalCareplansAttachments = "EducationalCareplansAttachments",
    PatientAttachments = "PatientAttachments",
    PatientAskQuestionAttachments = "PatientAskQuestionAttachments",
    FaxAttachments = "FaxAttachments",
    ReferralAttachments = "ReferralAttachments",
    TreatmentplanAttachments = "TreatmentplanAttachments",
    NotificationProfiles = "NotificationProfiles",
    NotificationProfileActions = "NotificationProfileActions",
    NotificationProfileRecipients = "NotificationProfileRecipients",
    TemplateProfileTags = "TemplateProfileTags",
    TemplateProfileAttachmentsTags = "TemplateProfileAttachmentsTags",
    Feedback = "Feedback",
    AllowedImpersonationUsers = "AllowedImpersonationUsers",
    ApprovedSummary = "ApprovedSummary",
    RequestedSummary = "RequestedSummary",
    RealtimeHubConnection = "RealtimeHubConnection",
    Impersonations = "Impersonations",
    Group = "Group",
    GroupUser = "GroupUser",
    CareplanPdfVersion = "CareplanPdfVersion",
    PatientTreatmentPlans = "PatientTreatmentPlans",
    DeferredUserInvites = "DeferredUserInvites",
    NotificationTypes = "NotificationTypes",
    InquiryEntities = "InquiryEntities",
    RecordsByEntity = "RecordsByEntity",
    InquiryQuestionsValues = "InquiryQuestionsValues",
    UploadFaxPdfVersion = "UploadFaxPdfVersion",
    DeadLetterMessages = "DeadLetterMessages",
    UserKatanaNotifications = "UserKatanaNotifications",
    BillingSettings = "BillingSettings",
    HPUDashboard = "HPUDashboard",
    NpsDashboard = "NpsDashboard",
    CareplanDashboard = "CareplanDashboard",
    CareplansByStatusDashboard = "CareplansByStatusDashboard",
    BulkValidations = "BulkValidations",
    PractitionerAliases = "PractitionerAliases",
    UserSettings = "UserSettings",
    ManageFaxDashboard = "ManageFaxDashboard",
    ReferralCountDashboard = "ReferralCountDashboard",
    ManageGroupDashboard = "ManageGroupDashboard",
    TranscriberManagerDashboard = "TranscriberManagerDashboard",
    QuotasSummaryRequesting = "QuotasSummaryRequesting",
    QuotasSummaryApproving = "QuotasSummaryApproving",
    BillingSummaryDashboard = "BillingSummaryDashboard",
    CompletedPatientsByHpcDashboard = "CompletedPatientsByHpcDashboard",
    PatientEducationalCareplans = "PatientEducationalCareplans",
    BulkSearchIndexBuilds = "BulkSearchIndexBuilds",
    CareplanCaseNotes = "CareplanCaseNotes",
    CareplanRecommendations = "CareplanRecommendations",
    Pharmacies = "Pharmacies",
    GroupLabTest = "GroupLabTest",
    PatientLabTest = "PatientLabTest",
    HistoryItem = "HistoryItem",
    PractitionerAssignedPractitioner = "PractitionerAssignedPractitioner",
    AvailablePractitioners = "AvailablePractitioners",
    PatientDirectory = "PatientDirectory",
    LabTests = "LabTests",
    GroupItem = "GroupItem",
    PatientHistoryType = "PatientHistoryType",
    GroupFolder = "GroupFolder",
    Folders = "Folders",
    PatientStatus = "PatientStatus",
    PatientPaneling = "PatientPaneling",
    Flag = "Flag",
    PatientClinicalQuestionsCount = "PatientClinicalQuestionsCount",
    PatientDocumentFolder = "PatientDocumentFolder",
    PatientDocumentFolderPdf = "PatientDocumentFolderPdf",
    PatientPanelingMetrics = "PatientPanelingMetrics",
    GoalItem = "GoalItem",
    PatientGoal = "PatientGoal",
    HpcMvcaMetrics = "HpcMvcaMetrics",
    QuestionsCompletedPerHpc = "QuestionsCompletedPerHpc",
    PatientChartNotes = "PatientChartNotes",
    CareplanPatientMedications = "CareplanPatientMedications",
    CareplanPatientConditions = "CareplanPatientConditions",
    CareplanInvestigations = "CareplanInvestigations",
    PatientPool = "PatientPool",
    PatientPoolPractitioners = "PatientPoolPractitioners",
    Reminder = "Reminder",
    PatientReminder = "PatientReminder",
    PatientGoalReminder = "PatientGoalReminder",
    UnansweredQuestionsPerSP = "UnansweredQuestionsPerSP",
    UnansweredQuestionsPerDiagnostic = "UnansweredQuestionsPerDiagnostic",
    PatientPoolOccupancy = "PatientPoolOccupancy",
    Role = "Role",
    GroupRole = "GroupRole",
    DeclineReason = "DeclineReason",
    Report = "Report",
    PatientSyncRecord = "PatientSyncRecord",
    PatientSummary = "PatientSummary",
    GpDashboard = "GpDashboard",
    PatientAliases = "PatientAliases",
    AiPromptDefinition = "AiPromptDefinition",
    PatientHousehold = "PatientHousehold",
    PatientPoolItem = "PatientPoolItem",
    PatientPoolItemAttachment = "PatientPoolItemAttachment",
    PatientPoolItemWorkflowStatus = "PatientPoolItemWorkflowStatus",
    EntityChangeLogs = "EntityChangeLogs",
    TimeLine = "TimeLine",
    SnapShot = "SnapShot",
}
