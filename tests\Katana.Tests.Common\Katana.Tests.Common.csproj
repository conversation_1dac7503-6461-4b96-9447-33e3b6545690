﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <IsTestProject>true</IsTestProject>
    <AnalysisLevel>latest</AnalysisLevel>
  </PropertyGroup>
  <ItemGroup>
    <Using Include="Sieve.Models.SieveModel" Alias="SieveModel" />
    <Using Include="Aoshield.Core.DataAccess.Sieve" />
    <Using Include="Sieve.Services" />
    <!--Models-->
    <Using Include="Aoshield.Core.Entities.Models.BaseEntityDto" Alias="BaseEntityDto" />
    <Using Include="Aoshield.Core.Entities.Models.BaseAddDto" Alias="BaseAddDto" />
    <Using Include="Aoshield.Core.Entities.Models.BaseUpdateDto" Alias="BaseUpdateDto" />
    <Using Include="Aoshield.Core.Entities.Models.ConfirmationNoteDto" Alias="ConfirmationNoteDto" />
    <!--Actions-->
    <Using Include="Aoshield.Core.EntityActions.EntityAction" Alias="EntityAction" />
    <Using Include="Aoshield.Core.EntityActions.GlobalActions" Alias="GlobalActions" />
    <Using Include="Aoshield.Core.EntityActions.HttpMetadata" Alias="HttpMetadata" />
    <Using Include="Aoshield.Core.EntityActions.IActionable" Alias="IActionable" />
    <Using Include="Aoshield.Core.EntityActions.IActionableDto" Alias="IActionableDto" />
    <Using Include="Aoshield.Core.EntityActions.RolesBasedCanDelegate" Alias="RolesBasedCanDelegate" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoFixture" Version="4.18.1" />
    <PackageReference Include="Bogus" Version="35.6.3" />
    <PackageReference Include="FluentAssertions" Version="8.2.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\Katana.Database\Katana.Database.csproj" />
    <ProjectReference Include="..\..\src\Katana.Services\Katana.Services.csproj" />
  </ItemGroup>
</Project>
