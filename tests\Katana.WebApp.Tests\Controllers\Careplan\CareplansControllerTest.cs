﻿using Bogus;
using Katana.Core.Entities;
using Katana.Services.Careplans.Models;
using Katana.Services.Common.Models;
using Katana.WebApp.Tests.Common;
using System.Net;
using Xunit;

namespace Katana.WebApp.Tests.Controllers.Careplan
{

    /// <summary>
    /// Crud Controller Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerCrudTests(KatanaWebApplicationFactory<Startup> factory) : ControllerCrudAndBatchActionsTestsBase<CareplanDto>(factory, "/api/Careplans")
    {
    }

    /// <summary>
    /// Notes Controller Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerNotesTests(KatanaWebApplicationFactory<Startup> factory) : ControllerNotesTestsBase(factory, "/api/Careplans")
    {
    }

    /// <summary>
    /// Workflow Status Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerWorkflowStatusTests(KatanaWebApplicationFactory<Startup> factory) : ControllerWorkflowStatusTestsBase(factory, "/api/Careplans")
    {
    }

    /// <summary>
    /// Status Controller Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerStatusTests(KatanaWebApplicationFactory<Startup> factory) : ControllerStatusTestsBase(factory, "/api/Careplans")
    {
    }

    /// <summary>
    /// Validation Status Controller Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerValidationStatusTests(KatanaWebApplicationFactory<Startup> factory) : ControllerValidationStatusTestsBase(factory, "/api/Careplans")
    {
    }

    /// <summary>
    /// Controller Custom Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    public class CareplansControllerCustomTests(KatanaWebApplicationFactory<Startup> factory) : ControllerTestsBase(factory, "/api/Careplans")
    {

        /// <summary>
        /// Test Update Cases Notes of Careplans.
        /// </summary>
        [Fact]
        public async Task Test_PutCareplanCasesNotes()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(p => p.Id, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(obj);

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/1/casesnotes", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test update careplan recommnedation.
        /// </summary>
        [Fact]
        public async Task Test_PutCareplanRecommendations()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(p => p.Id, x => 1)
                .Generate();
            var content = Utilities.GetRequestContent(obj);

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/1/recommendations", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Update ConsultationReason of Careplans.
        /// </summary>
        [Fact]
        public async Task Test_PutCareplanConsultationReason()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(p => p.Id, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(obj);

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/1/consultationreason", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Assign Practitioner Sp To Careplan.
        /// </summary>
        [Fact]
        public async Task Test_AssignSpToCareplan()
        {
            // Arrange
            var obj = new Faker<AssignPractitionerDto>()
                .RuleFor(p => p.Id, x => 1)
                .RuleFor(p => p.Practitioner, x => new PractitionerCommonDto() { Id = 1, UserId = 1 })
                .Generate();

            var content = Utilities.GetRequestContent(new AssignPractitionerDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/AssignSp", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }


        /// <summary>
        /// Test Assign Practitioner Itp To Careplan.
        /// </summary>
        [Fact]
        public async Task Test_AssignItpToCareplan()
        {
            // Arrange
            var obj = new Faker<AssignPractitionerDto>()
                .RuleFor(p => p.Id, x => 1)
                .RuleFor(p => p.Practitioner, x => new PractitionerCommonDto() { Id = 1, UserId = 1 })
                .Generate();

            var content = Utilities.GetRequestContent(new AssignPractitionerDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/AssignItp", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test that careplan send back
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_SendBack()
        {
            // Arrange
            var obj = new Faker<ConfirmationNoteDto>()
                .RuleFor(c => c.Id, x => 1)
                .RuleFor(c => c.Note, x => x.Random.Words(3))
                .Generate();

            var content = Utilities.GetRequestContent(new ConfirmationNoteDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/sendBack", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Pick up careplan by user
        /// </summary>
        [Fact]
        public async Task Test_PickUpCareplanByUser()
        {
            // Arrange
            var obj = new Faker<PickUpEntityDto>()
                .RuleFor(p => p.Id, x => 1)
                .RuleFor(p => p.LockUserId, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(new PickUpEntityDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/PickUp", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Generate Careplan from Fax
        /// </summary>
        [Fact]
        public async Task Test_GenerateFromFax()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(f => f.Id, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(new BaseUpdateDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/Generate", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Tests getting mine careplans.
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_GetMine()
        {
            // Act
            var response = await HttpClient.GetAsync($"{Endpoint}/Mine");

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test that careplan OCode Exclude/Include from billing
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_DoNotBillOCode()
        {
            // Arrange
            var obj = new Faker<DoNotBillOCodeDto>()
                .RuleFor(c => c.Id, x => 1)
                .RuleFor(c => c.DoNotBillOcode, BillingExclusionType.Temporary)
                .Generate();

            var content = Utilities.GetRequestContent(new DoNotBillOCodeDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/doNotBillOCode", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test that careplan RCode Exclude/Include from billing
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_DoNotBillRCode()
        {
            // Arrange
            var obj = new Faker<DoNotBillRCodeDto>()
                .RuleFor(c => c.Id, x => 1)
                .RuleFor(c => c.DoNotBillRcode, BillingExclusionType.Temporary)
                .Generate();

            var content = Utilities.GetRequestContent(new DoNotBillRCodeDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/doNotBillRCode", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Adjust Request Date
        /// </summary>
        [Fact]
        public async Task Test_AdjustmentCareplanRequestDate()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(f => f.Id, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(new BaseUpdateDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/AdjustmentRequestDate", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Automatic SP Assignment
        /// </summary>
        [Fact]
        public async Task Test_AutoAssignSp()
        {
            // Arrange
            var obj = new Faker<BaseUpdateDto>()
                .RuleFor(f => f.Id, x => 1)
                .Generate();

            var content = Utilities.GetRequestContent(new BaseUpdateDto[] { obj });

            //Act
            var response = await HttpClient.PutAsync($"{Endpoint}/AdjustmentRequestDate", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Tests Specialty assignment to CarePlan
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_AssignTranscribersToGroup()
        {
            // Arrange
            var obj = new AssignSpecialtyToCareplanDto[]
            {
                new() { Id = 1, SpecialtyId = 1 }
            };
            var content = Utilities.GetRequestContent(obj);

            var response = await HttpClient.PutAsync($"{Endpoint}/assignSpecialty", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Tests getting attention care plans.
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_GetAttention()
        {
            // Act
            var response = await HttpClient.GetAsync($"{Endpoint}/Attention");

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }

        /// <summary>
        /// Test Cancel Care plan
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task Test_CancelCareplan()
        {
            // Arrange
            var obj = new ConfirmationNoteDto[]
            {
                new() { Id = 1, Note = new Faker().Random.Words(2) }
            };
            var content = Utilities.GetRequestContent(obj);

            var response = await HttpClient.PutAsync($"{Endpoint}/Cancel", content);

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        }
    }

    /// <summary>
    /// Attachments Controller Tests
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="factory"></param>
    public class CareplansControllerAttachmentsTests(KatanaWebApplicationFactory<Startup> factory) : ControllerAttachmentsTestsBase(factory, "/api/Careplans")
    {
    }
}

