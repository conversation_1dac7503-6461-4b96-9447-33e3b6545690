using Aoshield.Core.Entities.Abstractions;
using Katana.Core.Entities.Abstractions;

namespace Katana.Core.Entities
{
    /// <summary>
    /// Severity Code
    /// </summary>
    public enum SeverityCode
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
        Unknown,
        Se<PERSON>,
        Moderate,
        Mild
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }

    /// <summary>
    /// Reaction Code
    /// </summary>
    public enum ReactionCode
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
        Unknown,
        <PERSON><PERSON>H<PERSON>,
        <PERSON>shMaculopapular,
        <PERSON>shLocalContact,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        Angiodema,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        Other
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }

    /// <summary>
    /// Life Stage Type
    /// </summary>
    public enum LifeStageType
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
        Unknown,
        <PERSON><PERSON>,
        Infant,
        Child,
        Adolescent,
        Adult
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }

    /// <summary>
    /// Patient Allergy
    /// </summary>
    public class PatientAllergy : BaseEntity, ISynchronizableEntity
    {
        /// <summary>
        /// Patient Id
        /// </summary>
        public int PatientId { get; set; }

        /// <summary>
        /// Allergy Id
        /// </summary>
        public int AllergyId { get; set; }

        /// <summary>
        /// Allergy
        /// </summary>
        public Allergy Allergy { get; set; }

        /// <summary>
        /// Diagnosis Date of the allergy.
        /// </summary>
        public string DiagnosedAge { get; set; }

        /// <summary>
        /// Reaction Date
        /// </summary>
        public DateTime? ReactionDate { get; set; }

        /// <summary>
        /// Life Stage
        /// </summary>
        public LifeStageType LifeStageType { get; set; }

        /// <summary>
        /// Life Stage Description
        /// </summary>
        public string LifeStageDescription { get; set; }

        /// <summary>
        /// Severity Code
        /// </summary>
        public SeverityCode SeverityCode { get; set; }

        /// <summary>
        /// Reaction Code
        /// </summary>
        public ReactionCode ReactionCode { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Notes { get; set; }

        /// <inheritdoc cref="ISynchronizableEntity.SyncRefId"/>
        public int? SyncRefId { get; set; }

        /// <inheritdoc cref="ISynchronizableEntity.SyncDate"/>
        public DateTime? SyncDate { get; set; }
    }
}
