using Aoshield.Core.DataAccess.AzureFileStorage;
using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Services.Core.Exceptions;
using AutoFixture;
using AutoMapper;
using FluentAssertions;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Services.Tests.Common;
using Katana.Services.UserInquiryForms;
using Katana.Services.UserInquiryForms.Models;
using Katana.Tests.Common.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Sieve.Models;
using Xunit;

namespace Katana.Services.Tests.Services.UserInquiryForms;

/// <summary>
/// Answer tests
/// </summary>
/// <remarks>
/// Ctor
/// </remarks>
/// <param name="configurationFixture"></param>
public class UserFormServiceAnswerTests(ConfigurationFixture configurationFixture) : ServicesUnitTestConfiguration(
    configurationFixture)
{
    private readonly Fixture _fixture = new();


    /// <summary>
    /// Test queries
    /// </summary>
    [Fact]
    public async Task Queries_Test()
    {
        //Arrange
        var form = InitMasterForms();
        await Krudder.Add(form, null);

        var azureService = new Mock<IAzureFileStorageService>();
        azureService.Setup(x => x.ListFiles<InquiryFormStep>(It.IsAny<int>(),
                It.IsAny<SieveModel>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(() =>
                new PagedResults<BlobItemDto>([], 0, 1, 10, 1, 1, null, null,
                    null));
        var uut = new UserFormService(Krudder,
            new Mock<ILogger<UserFormService>>().Object,
            new Mock<IMapper>().Object,
            azureService.Object,
            new Mock<IValidator<UserInquiryForm>>().Object,
            new Mock<IValidator<UserInquiryFormQuestion>>().Object,
            new Mock<IServiceProvider>().Object,
            new Mock<IOptions<SieveOptions>>().Object
        );

        await uut.InitForms([form.Id],
            [await Krudder.GetCurrentUserId()],
            CancellationToken.None);

        //Acts - Asserts
        var formsResult = await uut.GetInquiryForms();
        formsResult.Length.Should().Be(1);
        formsResult[0].Type.Should().Be(InquiryFormType.IntakeForm);

        var steps = await uut.GetInquiryFormSteps(formsResult.First().Id);
        steps.Length.Should().Be(form.Steps.Count);
        foreach (var step in steps)
        {
            var questions =
                await uut.GetInquiryStepQuestions(step.Id);
            questions.Length.Should()
                .Be(form.Steps.First(x => x.Id == step.MasterInquiryFormStepId)
                    .Questions.Count);
        }
    }

    /// <summary>
    /// Test answers
    /// </summary>
    [Fact]
    public async Task Answer_Test()
    {
        //Arrange
        var form = InitMasterForms();
        await Krudder.Add(form, null);
        var uut = new UserFormService(Krudder,
            new Mock<ILogger<UserFormService>>().Object,
            new Mock<IMapper>().Object,
            new Mock<IAzureFileStorageService>().Object,
            new Mock<IValidator<UserInquiryForm>>().Object,
            new Mock<IValidator<UserInquiryFormQuestion>>().Object,
            new Mock<IServiceProvider>().Object,
            new Mock<IOptions<SieveOptions>>().Object
        );

        await uut.InitForms([form.Id],
            [await Krudder.GetCurrentUserId()],
            CancellationToken.None);

        //Acts
        var formsResult = await uut.GetInquiryForms();
        var steps = await uut.GetInquiryFormSteps(formsResult.First().Id);
        var step1 = steps[0];
        var step1Questions =
            await uut.GetInquiryStepQuestions(step1.Id);

        //Good Answer
        var answers = step1Questions
            .Select(GenerateGoodAnswer)
            .ToArray();
        await uut.AnswerInquiryFormQuestions(answers);

        //Assert
        var dbAnswers = await Krudder.Set<UserInquiryFormQuestion>()
            .Where(x => answers.Select(a => a.Id).Contains(x.Id))
            .ToArrayAsync();

        foreach (var answer in dbAnswers)
        {
            answer.Answer.Should().Be(answers.First(x => x.Id == answer.Id).Answer);
        }
    }


    private QuestionAnswerDto GenerateGoodAnswer(UserInquiryFormQuestionDto question)
    {
        var answer = question.QuestionType switch
        {
            QuestionTypes.String => _fixture.Create<string>(),
            QuestionTypes.LongString => _fixture.Create<string>(),
            QuestionTypes.Number => "0",
            QuestionTypes.Boolean => "true",
            QuestionTypes.MultiplePick => question.QuestionValues,
            QuestionTypes.SinglePick => question.QuestionValues.Split(',')[0],
            QuestionTypes.Signature => string.Empty,
            QuestionTypes.Patient => string.Empty,
            _ => throw new ServiceException()
        };
        return new QuestionAnswerDto() { Id = question.Id, Answer = answer };
    }

    //private QuestionAnswerDto GenerateBadAnswer(UserInquiryFormQuestion question)
    //{
    //    var answer = question.QuestionType switch
    //    {
    //        QuestionTypes.String => null,
    //        QuestionTypes.LongString => null,
    //        QuestionTypes.Number => null,
    //        QuestionTypes.Boolean => null,
    //        QuestionTypes.MultiplePick => _fixture.Create<string>(),
    //        QuestionTypes.SinglePick => _fixture.Create<string>(),
    //        QuestionTypes.Signature => null,
    //        QuestionTypes.Patient => null,
    //        _ => throw new ServiceException()
    //    };
    //    return new QuestionAnswerDto() { Id = question.Id, Answer = answer };
    //}

    private InquiryForm InitMasterForms()
    {
        var masterForm1 = new InquiryForm()
        {
            Name = _fixture.Create<string>(),
            Description = _fixture.Create<string>(),
            Type = InquiryFormType.IntakeForm,
            UserType = UserType.Patient,
            Steps =
            [
                new()
                {
                    Header = _fixture.Create<string>(),
                    Description = _fixture.Create<string>(),
                    ImageUrl = _fixture.Create<string>(),
                    Questions =
                    [
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Number,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.String,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.Boolean,
                            QuestionValues = _fixture.Create<string>(),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.SinglePick,
                            QuestionValues = string.Join(',',
                                new[] { _fixture.Create<string>(), _fixture.Create<string>() }),
                        },
                        new()
                        {
                            Question = _fixture.Create<string>(),
                            Entity = _fixture.Create<string>(),
                            EntityId = _fixture.Create<int>(),
                            Required = true,
                            QuestionType = QuestionTypes.MultiplePick,
                            QuestionValues = string.Join(',',
                                new[] { _fixture.Create<string>(), _fixture.Create<string>() }),
                        }
                    ]
                }
            ]
        };
        return masterForm1;
    }
}
