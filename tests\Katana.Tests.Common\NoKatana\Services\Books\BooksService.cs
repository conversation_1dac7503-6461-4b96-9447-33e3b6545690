﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.Entities.Models;
using FluentValidation;
using Katana.Services.PatientStatuses.Models;
using Katana.Tests.Common.NoKatana.Core.Entities;
using Katana.Tests.Common.NoKatana.Services.Books.Models;

namespace Katana.Tests.Common.NoKatana.Services.Books
{
    /// <summary>
    /// Book CRUD Service
    /// </summary>
    /// <remarks>
    /// Main constructor
    /// </remarks>
    public class BooksService(
        IKrudder krudder,
        IValidator<Book> validator) : IBooksService
    {
        private readonly IKrudder _krudder = krudder;
        private readonly IValidator<Book> _validator = validator;

        ///<inheritdoc/>
        public async Task<AddBookDto> Add(AddBookDto dto, CancellationToken cancellation = default)
        {
            var entity = _krudder.Map(dto, _krudder.Mapper.Map, new Book(), GlobalActions.Add);
            entity = await _krudder.Add(entity, _validator, cancellation: cancellation);
            dto.Id = entity.Id;
            return dto;
        }

        ///<inheritdoc/>
        public Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<BookDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<BookDto> GetById(int id, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DisableBatch(UpdateIsActiveStatusDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableBatch(UpdateIsActiveStatusDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<UpdateBookDto> Update(UpdateBookDto _, CancellationToken _1 = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();
    }
}
