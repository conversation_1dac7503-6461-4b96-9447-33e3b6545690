using Aoshield.Core.Entities.Models;
using Aoshield.Services.ServicesBus;
using Aoshield.Services.ServicesBus.Models;
using Aoshield.Services.ServicesBus.PubSub;

namespace Katana.WebApp.Tests.Services;

/// <summary>
/// Test Service
/// </summary>
public class DeadLetterMessageServiceTest : IDeadLetterMessageService
{
    /// <inheritdoc />
    public Task<AddDeadLetterMessageDto> Add(AddDeadLetterMessageDto dto,
        CancellationToken cancellation = default) =>
        Task.FromResult(new AddDeadLetterMessageDto() { Id = 1 });

    /// <inheritdoc />
    public Task<DeadLetterMessageDto> GetById(int id, CancellationToken cancellation = default) =>
        Task.FromResult(id <= 0 ? null : new DeadLetterMessageDto() { Id = id });

    /// <inheritdoc />
    public async Task<IPagedResults<DeadLetterMessageDto>> GetAsPagedResults(SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new PagedResults<DeadLetterMessageDto>(
            [],
            default,
            default, default, default, default, default, default, default));

    /// <inheritdoc />
    public Task<UpdateDeadLetterMessageDto> Update(UpdateDeadLetterMessageDto dto,
        CancellationToken cancellation = default) =>
        Task.FromResult(new UpdateDeadLetterMessageDto() { Id = dto.Id });

    /// <inheritdoc />
    public async Task<int?> Delete(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    /// <inheritdoc />
    public async Task<int?> Restore(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    ///<inheritdoc/>
    public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(confirmationNoteDtos.Select(x => x.Id)
            .ToArray());

    /// <inheritdoc />
    public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
        bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(confirmationNoteDtos.Select(x => x.Id)
            .ToArray());

    ///<inheritdoc/>
    public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    /// <inheritdoc />
    public Task SaveDeadLetterMessage<TMessage>(TMessage message, CancellationToken cancellationToken = default) where TMessage : SBMessageBase => throw new NotImplementedException();

    /// <inheritdoc />
    public Task<int> RequeueMessages(int[] ids, CancellationToken cancellationToken) => throw new NotImplementedException();

    /// <inheritdoc />
    public Task RequeueMessages(CancellationToken cancellationToken = default) => throw new NotImplementedException();
}
