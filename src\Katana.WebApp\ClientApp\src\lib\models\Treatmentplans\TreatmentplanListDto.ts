import { ClinicCommonDto } from "../Common/ClinicCommonDto";
import { PatientCommonDto } from "../Common/PatientCommonDto";
import { PractitionerCommonDto } from "../Common/PractitionerCommonDto";
import { DiagnosticCommonDto } from "../Common/DiagnosticCommonDto";
import { FaxCommonDto } from "../Common/FaxCommonDto";
import { TreatmentplanCommonActionsDto } from "./TreatmentplanCommonActionsDto";

/** TreatmentplanListDto */
export interface TreatmentplanListDto extends TreatmentplanCommonActionsDto {
    requestDate: string | null;
    fax: FaxCommonDto;
    clinic: ClinicCommonDto;
    patient: PatientCommonDto;
    diagnostic1: DiagnosticCommonDto;
    diagnostic2: DiagnosticCommonDto;
    np: PractitionerCommonDto;
    custodianGp: PractitionerCommonDto;
    processedDate: string | null;
    responseDate: string | null;
    labResults: boolean;
    imagingReports: boolean;
    consultReports: boolean;
    procedures: boolean;
    dischargeSummary: boolean;
    expediteRequest: boolean;
    deliveryFax: FaxCommonDto | null;
    deliveryDate: string | null;
    notesCount: number;
    newGPWorkflow: boolean;
}
