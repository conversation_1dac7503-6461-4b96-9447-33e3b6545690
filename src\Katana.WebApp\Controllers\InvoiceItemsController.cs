﻿using Katana.Services.InvoiceItems;
using Katana.Services.InvoiceItems.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// InvoiceItems Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceItemsController : ControllerBase
    {
        private readonly IInvoiceItemsService _invoiceItemsService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="invoiceItemsService">InvoiceItems CRUD service</param>
        public InvoiceItemsController(
            IInvoiceItemsService invoiceItemsService
        ) => _invoiceItemsService = invoiceItemsService;

        /// <summary>
        /// Get All InvoiceItems
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of items</returns>
        [HttpGet]
        public async Task<IPagedResults<InvoiceItemDto>> GetAllItems(
            [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _invoiceItemsService.GetAllItems(query, cancellation);

        /// <summary>
        /// Get All InvoiceItems
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of items</returns>
        [HttpGet("Mine")]
        public async Task<IPagedResults<InvoiceItemDto>> GetMineItems(
            [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _invoiceItemsService.GetMineItems(query, cancellation);

        /// <summary>
        /// Get InvoiceItems by Invoice Id
        /// </summary>
        /// <param name="id">Invoice's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of items</returns>
        [HttpGet("Invoice/{id}")]
        public async Task<IPagedResults<InvoiceItemDto>> GetItems(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _invoiceItemsService.GetItemsByInvoice(id, query, cancellation);

        /// <summary>
        /// Get InvoiceItems by Invoice Id
        /// </summary>
        /// <param name="id">Invoice's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of items</returns>
        [HttpGet("Invoice/{id}/mine")]
        public async Task<IPagedResults<InvoiceItemDto>> GetMineItemsByInvoice(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _invoiceItemsService.GetMineItemsByInvoice(id, query, cancellation);

        /// <summary>
        /// Get Item by Id
        /// </summary>
        /// <param name="id">Item's id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Item dto</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<InvoiceItemDto>> GetItem(int id,
            CancellationToken cancellation = default)
        {
            var dto = await _invoiceItemsService.GetById(id, cancellation);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update Item
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateInvoiceItemDto>> PutInvoiceItem(int id,
            UpdateInvoiceItemDto dto, CancellationToken cancellation = default) =>
            id != dto.Id
                ? BadRequest()
                : await _invoiceItemsService.Update(dto, cancellation: cancellation);

        /// <summary>
        /// Mark external billing bulk operation
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("MarkExternalBillingBulk")]
        public async Task<ActionResult<int>> MarkExternalBillingBulk(int[] ids,
            CancellationToken cancellation = default) =>
            await _invoiceItemsService.MarkExternalBillingBulk(ids, cancellation: cancellation);

        /// <summary>
        /// Mark Clinic aid billing bulk operation
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("MarkClinicAidBillingBulk")]
        public async Task<ActionResult<int>> MarkClinicAidBillingBulk(int[] ids,
            CancellationToken cancellation = default) =>
            await _invoiceItemsService.MarkClinicAidBillingBulk(ids, cancellation: cancellation);

        /// <summary>
        /// Add note
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Notes")]
        public async Task<ActionResult<AddInvoiceItemNoteDto>> PostNote(AddInvoiceItemNoteDto addDto)
        {
            var dto = await _invoiceItemsService.AddNote(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Note by Id
        /// </summary>
        /// <param name="id">Note id</param>
        /// <returns></returns>
        [HttpGet("Notes/{id}")]
        public async Task<ActionResult<InvoiceItemNoteDto>> GetNote(int id)
        {
            var dto = await _invoiceItemsService.GetNoteById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">InvoiceItem's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Notes")]
        public async Task<IPagedResults<InvoiceItemNoteDto>> GetNotes(int id, [FromQuery] SieveModel query) =>
            await _invoiceItemsService.GetNotesAsPagedResults(id, query);

        /// <summary>
        /// Update note
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Notes/{id}")]
        public async Task<ActionResult<UpdateInvoiceItemNoteDto>> PutNote(int id, UpdateInvoiceItemNoteDto dto) =>
            id != dto.Id ? BadRequest() : await _invoiceItemsService.UpdateNote(dto);

        /// <summary>
        /// Delete Note
        /// </summary>
        /// <param name="id">Id of the note to delete</param>
        /// <returns></returns>
        [HttpDelete("Notes/{id}")]
        public async Task<ActionResult<int?>> DeleteNote(int id) =>
            await _invoiceItemsService.DeleteNote(id);

        /// <summary>
        /// Delete Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to delete</param>
        /// <returns></returns>
        [HttpDelete("DeleteNoteBatch")]
        public async Task<int[]> DeleteNoteBatch(int[] ids) =>
            await _invoiceItemsService.DeleteNoteBatch(ids);

        /// <summary>
        /// Restore Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to restore</param>
        /// <returns></returns>
        [HttpDelete("RestoreNoteBatch")]
        public async Task<int[]> RestoreNoteBatch(int[] ids) =>
            await _invoiceItemsService.RestoreNoteBatch(ids);

        /// <summary>
        /// Excludes or includes a invoice item from/ in billing
        /// </summary>
        /// <param name="dtoArr">Contains id of TranscriberGroup and id(s) of fax(es) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("DoNotBill")]
        public async Task<int> DoNotBill(DoNotBillInvoiceItemDto[] dtoArr,
            CancellationToken cancellation) =>
            await _invoiceItemsService.DoNotBill(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Ignore errors in items
        /// </summary>
        /// <param name="dtoArr">Contains id of TranscriberGroup and id(s) of fax(es) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("IgnoreErrors")]
        public async Task<int> IgnoreErrors(IgnoreErrorsInvoiceItemDto[] dtoArr,
            CancellationToken cancellation) =>
            await _invoiceItemsService.IgnoreErrors(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Release Invoice Items
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>number released Invoice items</returns>
        [HttpPut("PickUp")]
        public async Task<List<int>> PickUp(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _invoiceItemsService.PickUp(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Release Invoice Items
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>number released Invoice items</returns>
        [HttpPut("Release")]
        public async Task<List<int>> Release(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _invoiceItemsService.Release(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Associate Billing settings
        /// </summary>
        /// <param name="dtoArr">Contains id of invoiceItems and BillingSettings(s) to associate</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("Associate")]
        public async Task<int> AssociateBillingSetting(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
        await _invoiceItemsService.AssociateBillingSetting(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Update Claim Number
        /// </summary>
        /// <param name="dto">Dto's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>dto</returns>
        [HttpPut("ResubmitInvoiceItem")]
        public async Task<ResubmitInvoiceItemDto> ResubmitInvoiceItem(ResubmitInvoiceItemDto dto,
            CancellationToken cancellation) =>
        await _invoiceItemsService.ResubmitInvoiceItem(dto, cancellation: cancellation);
    }
}
