﻿using Aoshield.Core.Entities.Models;
using Katana.Services.InquiryFormQuestions.Model;
using Katana.Services.InquiryFormSteps;
using Katana.Services.InquiryFormSteps.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// InquiryFormStep CRUD service
    /// </summary>
    public class InquiryFormStepsServiceTest : IInquiryFormStepsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddInquiryFormStepDto> Add(AddInquiryFormStepDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddInquiryFormStepDto());

        ///<inheritdoc/>
        public async Task<IPagedResults<InquiryFormStepDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<InquiryFormStepDto>(
                [], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<InquiryFormStepDto> GetById(int id,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new InquiryFormStepDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateInquiryFormStepDto> Update(UpdateInquiryFormStepDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateInquiryFormStepDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Utils

        /// <inheritdoc/>
        public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) =>
            new($"http://test.com/{fileName}");

        /// <inheritdoc/>
        public async Task<IPagedResults<InquiryFormQuestionDto>> GetQuestionsByInquiryFormStepId(
            int id, SieveModel query, CancellationToken cancellation = default) => await Task.Run(
            () => new PagedResults<InquiryFormQuestionDto>([],
                default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public Task<int> ReassignStep(ReassignInquiryFormStepDto[] dtoArr, CancellationToken cancellation = default) => Task.Run(() => 0);

        #endregion
    }
}
