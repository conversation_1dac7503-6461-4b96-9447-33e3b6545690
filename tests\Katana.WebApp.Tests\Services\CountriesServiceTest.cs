﻿using Aoshield.Core.Entities.Models;
using Katana.Services.Countries;
using Katana.Services.Countries.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Country CRUD service
    /// </summary>
    public class CountriesServiceTest : ICountriesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddCountryDto> Add(AddCountryDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddCountryDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<CountryDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<CountryDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<CountryDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new CountryDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateCountryDto> Update(UpdateCountryDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateCountryDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
