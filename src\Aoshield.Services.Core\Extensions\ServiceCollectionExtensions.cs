
using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage;
using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities;
using Aoshield.Core.Exceptions;
using Aoshield.Services.Core.AzureDevOps;
using Aoshield.Services.Core.Hangfire;
using Aoshield.Services.Core.Pdf;
using Aoshield.Services.Core.PowerBi;
using Aoshield.Services.Core.SharepointFileStorage;
using Aoshield.Services.Core.UrlSigning;
using Aoshield.Services.Core.UrlSigning.Models;
using Aoshield.Services.Core.Validation.Extensions;
using Aoshield.Services.Export.Core.Exceptions;
using Aoshield.Services.Import.Core.Exceptions;
using Aoshield.Services.Messaging.ChatHub;
using Aoshield.Services.Messaging.Exceptions;
using Aoshield.Services.Messaging.Extensions;
using Aoshield.Services.Messaging.Faxes;
using Aoshield.Services.Messaging.RingCentral;
using FluentValidation;
using Hangfire;
using Hangfire.SqlServer;
using Hellang.Middleware.ProblemDetails;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Microsoft.Graph;
using System.Reflection;
using IUser = Aoshield.Core.Entities.Abstractions.IUser;
using ProblemDetailsFactory = Aoshield.Services.Core.ApiProblemDetails.ProblemDetailsFactory;

namespace Aoshield.Services.Core.Extensions
{
    /// <summary>
    /// Application Builder extentions
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds AOShield Core infrastructure
        /// </summary>
        /// <typeparam name="TDBContext">The type of the database context</typeparam>
        /// <typeparam name="TUser">The type of the user</typeparam>
        /// <typeparam name="TServicesAssemblyMarker">The marker interface for services assembly</typeparam>
        /// <typeparam name="TValidatorsAssemblyMarker">The marker interface for validators assembly</typeparam>
        /// <typeparam name="TAutomappersAssemblyMarker">The marker interface for automappers assembly</typeparam>
        /// <typeparam name="TEntitiesAssemblyMarker">The marker interface for entities assembly</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <param name="env">The web host environment</param>
        /// <param name="sqlserverOptionsBuilder">Callback to apply sql configuration</param>
        /// <returns>void</returns>
        private static void AddDataAccessInfrastructure<TDBContext, TUser, TServicesAssemblyMarker, TValidatorsAssemblyMarker,
            TAutomappersAssemblyMarker, TEntitiesAssemblyMarker>(
            this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env, Action<SqlServerDbContextOptionsBuilder> sqlserverOptionsBuilder = null)
            where TDBContext : DbContext
            where TUser : User<TUser>, IUser, new()
            where TServicesAssemblyMarker : class
            where TValidatorsAssemblyMarker : class
            where TAutomappersAssemblyMarker : class
            where TEntitiesAssemblyMarker : class
        {
            //Adding Db Configuration
            var krudderDbConfigurationSection = configuration.GetSection("KrudderDbConfiguration");
            //Krudder
            services.Configure<KrudderDbConfiguration>(configuration.GetSection("KrudderDbConfiguration"));
            var krudderDbConfiguration = new KrudderDbConfiguration();
            krudderDbConfigurationSection.Bind(krudderDbConfiguration);

            var isTesting = env is null || env.IsTesting();
            var isDevelopment = env is not null && env.IsDevelopment();

            services.AddDbContext<TDBContext>(options =>
            {
                if (isDevelopment)
                {
                    options.UseLoggerFactory(LoggerFactory.Create(builder => builder.AddDebug()));
                    options.EnableSensitiveDataLogging();
                }

                // We override the DB Context object during testing, so let's
                // prevent any attempt to connect to the other Db(s) such as Prod or Dev.
                if (!isTesting)
                {
                    void sqlserverOptionsBuilderBase(SqlServerDbContextOptionsBuilder builder)
                    {
                        builder.EnableRetryOnFailure().MaxBatchSize(krudderDbConfiguration.DbMaxBatchSize);
                        builder.CommandTimeout(krudderDbConfiguration.DbCommandTimeout);
                        if (sqlserverOptionsBuilder is not null)
                        {
                            sqlserverOptionsBuilder(builder);
                        }
                    }
                    options.UseSqlServer(configuration.GetConnectionString(env.EnvironmentName), sqlserverOptionsBuilderBase);
                }
            });

            services.AddScoped<IDataContext>(provider =>
            {
                var dbContext = provider.GetRequiredService<TDBContext>();
                return new DataContext(dbContext);
            });

            services.AddScoped<IKrudder, Krudder<TUser>>();
            services.AddScoped<IKrudder<TUser>, Krudder<TUser>>();
            services.AddScoped<DataContextTransactionTracker>();

            services.AddAosSieveAssemblyMarker(Assembly.GetAssembly(typeof(IKrudder)));

            // Feature management
            services.AddFeatureManagement();

            services.AddCrudServicesFromAssemblyContaining<TServicesAssemblyMarker>();

            services.AddValidationServicesFromAssembly<TServicesAssemblyMarker, TValidatorsAssemblyMarker, TUser>();

            services.AddCheckStatusValidationServices<TServicesAssemblyMarker, TUser>(configuration, env);

            // Automapper profiles
            //
            services.AddAosAutoMapper(typeof(TAutomappersAssemblyMarker));

            //Sieve
            services.AddAosSieveAssemblyMarker(Assembly.GetAssembly(typeof(TServicesAssemblyMarker)));
            services.AddAosSieveAssemblyMarker(Assembly.GetAssembly(typeof(ICommonFeatures)));
            services.AddAosSieve(configuration);

            //Pdf Generator
            services.AddPdfGenerator();

            // Azure File Storage Services
            //
            services.AddAzureStorageService(configuration.GetSection("AzureStorage"));

            // SharePoint File Storage Services
            //
            services.AddSharePointStorageService(configuration.GetSection("SharepointStorage"));

            // Azure DevOps Services
            //
            services.AddAzureDevOpsService<TUser>(configuration.GetSection("AzureDevOps"));

            // Powerbi Report Services
            //
            services.AddPowerBiService(configuration.GetSection("Powerbi"));

            if (!isTesting)
            {
                services.AddAosMediator<TServicesAssemblyMarker>();
                services.AddFaxes(configuration.GetSection("RingCentral"));
                services.AddHangfire(configuration, env.EnvironmentName);
            }

            var developmentAllowedOrigins = krudderDbConfiguration.DevelopmentAllowedOrigins.Split(",", StringSplitOptions.RemoveEmptyEntries);
            //defining development cors policy
            services.AddCors(options =>
            {
                options.AddPolicy("DevelopmentCORS",
                    builder =>
                    {
                        builder
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .WithOrigins(developmentAllowedOrigins)
                            .AllowCredentials();
                    });
            });
            //defining production cors policy
            var productionAllowedOrigins = krudderDbConfiguration.ProductionAllowedOrigins.Split(",", StringSplitOptions.RemoveEmptyEntries);
            services.AddCors(options =>
            {
                options.AddPolicy("ProductionCORS",
                    builder =>
                    {
                        builder
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .WithOrigins(productionAllowedOrigins)
                            .SetIsOriginAllowedToAllowWildcardSubdomains()
                            .AllowCredentials();
                    });
            });
        }

        public static IServiceCollection AddDataAccessInfrastructure<TDBContext, TUser, TUserDto, TAddUserDto, TUpdateUserDto,
            TServicesAssemblyMarker, TValidatorsAssemblyMarker, TAutomappersAssemblyMarker, TEntitiesAssemblyMarker,
            TSieveCustomSort, TSieveCustomFilter>(
            this IServiceCollection services,
            IConfiguration configuration,
            IWebHostEnvironment env,
            Action<SqlServerDbContextOptionsBuilder> sqlserverOptionsBuilder = null)
            where TDBContext : DbContext
            where TUser : User<TUser>, IUser, new()
            where TServicesAssemblyMarker : class
            where TValidatorsAssemblyMarker : class
            where TAutomappersAssemblyMarker : class
            where TEntitiesAssemblyMarker : class
            where TSieveCustomSort : class, ISieveCustomSortMethods
            where TSieveCustomFilter : class, ISieveCustomFilterMethods
        {
            services.AddDataAccessInfrastructure<TDBContext, TUser, TServicesAssemblyMarker, TValidatorsAssemblyMarker,
                TAutomappersAssemblyMarker, TEntitiesAssemblyMarker>(configuration, env, sqlserverOptionsBuilder);

            //Sieve custom sort and filter
            services.AddScoped<ISieveCustomSortMethods, TSieveCustomSort>();
            services.AddScoped<ISieveCustomFilterMethods, TSieveCustomFilter>();

            return services;
        }

        /// <summary>
        /// Adds AOShield Core infrastructure
        /// </summary>
        /// <typeparam name="TDBContext">The type of the database context</typeparam>
        /// <typeparam name="TUser">The type of the user</typeparam>
        /// <typeparam name="TUserDto">The type of the user dto</typeparam>
        /// <typeparam name="TAddUserDto">The type of the add user dto</typeparam>
        /// <typeparam name="TUpdateUserDto">The type of the update user dto</typeparam>
        /// <typeparam name="TServicesAssemblyMarker">The marker interface for services assembly</typeparam>
        /// <typeparam name="TEntitiesAssemblyMarker">The marker interface for entities assembly</typeparam>
        /// <typeparam name="TSieveCustomSort">The type of SieveCustomSort</typeparam>
        /// <typeparam name="TSieveCustomFilter">The type of SieveCustomFilter</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <param name="env">The web host environment</param>
        /// <param name="sqlserverOptionsBuilder">Callback to apply sql configuration</param>
        /// <returns>IServiceCollection</returns>
        public static IServiceCollection AddDataAccessInfrastructure<TDBContext, TUser, TUserDto, TAddUserDto, TUpdateUserDto,
            TServicesAssemblyMarker, TEntitiesAssemblyMarker,
            TSieveCustomSort, TSieveCustomFilter>(
            this IServiceCollection services,
            IConfiguration configuration,
            IWebHostEnvironment env,
            Action<SqlServerDbContextOptionsBuilder> sqlserverOptionsBuilder = null)
            where TDBContext : DbContext
            where TUser : User<TUser>, IUser, new()
            where TServicesAssemblyMarker : class
            where TEntitiesAssemblyMarker : class
            where TSieveCustomSort : class, ISieveCustomSortMethods
            where TSieveCustomFilter : class, ISieveCustomFilterMethods
        {
            services.AddDataAccessInfrastructure<TDBContext, TUser, TUserDto, TAddUserDto, TUpdateUserDto, TServicesAssemblyMarker, TServicesAssemblyMarker,
                TServicesAssemblyMarker, TEntitiesAssemblyMarker, TSieveCustomSort, TSieveCustomFilter>(configuration, env, sqlserverOptionsBuilder);

            return services;
        }

        /// <summary>
        /// Add faxes Service
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">Faxes Configuration</param>
        public static void AddFaxes(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<FaxesConfiguration>(configuration);

            //RingCentral's client
            services.AddScoped<IRingCentralClient, RingCentralClient>();
        }

        /// <summary>
        /// Adds AOShield Problem Details middleware.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>IServiceCollection</returns>
        public static IServiceCollection AddAosProblemDetails(this IServiceCollection services)
        {
            services.AddProblemDetails(opt =>
            {
                // General Options
                //
                opt.IncludeExceptionDetails = (ctx, ex) =>
                {
                    // Fetch services from HttpContext.RequestServices
                    var env = ctx.RequestServices.GetRequiredService<IHostEnvironment>();
                    return env.IsProduction();
                };

                // Log everything
                //
                opt.ShouldLogUnhandledException = (HttpContext ctx, Exception ex, ProblemDetails pd) => true;

                // You can configure the middleware to re-throw certain types of exceptions, all exceptions or based on a predicate.
                // This is useful if you have upstream middleware that needs to do additional handling of exceptions.
                opt.Rethrow<NotSupportedException>();

                // This will map NotImplementedException to the 501 Not Implemented status code.
                opt.MapToStatusCode<NotImplementedException>(StatusCodes.Status501NotImplemented);

                // This will map HttpRequestException to the 503 Service Unavailable status code.
                opt.MapToStatusCode<HttpRequestException>(StatusCodes.Status503ServiceUnavailable);

                // Because exceptions are handled polymorphically, this will act as a "catch all" mapping, which is why it's added last.
                // If an exception other than NotImplementedException and HttpRequestException is thrown, this will handle it.
                //opt.MapToStatusCode<Exception>(StatusCodes.Status500InternalServerError);

                // Sets Instance to the request URI as stated by the ProblemDetails standard
                opt.OnBeforeWriteDetails = (HttpContext ctx, ProblemDetails pd) => pd.Instance = $"[{ctx.Request.Method}] {ctx.Request.Path}";

                // Exception to Error code mapping
                //
                opt.Map<Exception>(exception => exception switch
                {
                    //Ring Central
                    //
                    RetryRingCentralException ex => ProblemDetailsFactory.FromException(ex),

                    // Import
                    //
                    DuplicatedImportException ex => ProblemDetailsFactory.FromException(ex),

                    ImportException ex => ProblemDetailsFactory.FromException(ex),

                    // Export
                    //
                    ExportException ex => ProblemDetailsFactory.FromException(ex),

                    // Service
                    //
                    ServiceException ex => ProblemDetailsFactory.FromException(ex),
                    ReadableException ex => ProblemDetailsFactory.FromException(ex),

                    // Validation
                    CrudServiceValidationException ex => ProblemDetailsFactory.FromException(ex, errors: ex.InnerException?.Errors),

                    // Sieve
                    SieveMethodNotFoundException => ProblemDetailsFactory.FromException(exception),

                    // Sieve
                    SieveIncompatibleMethodException => ProblemDetailsFactory.FromException(exception),

                    // Sieve
                    SieveException => ProblemDetailsFactory.FromException(exception),

                    OperationCanceledException => ProblemDetailsFactory.FromException<OperationCanceledException>("The operation was canceled"),

                    // Catch all
                    //
                    Exception => ProblemDetailsFactory.FromException(exception),

                    // Default case (code won't reach this point anyway) to make the linter happy  #pragma IDE0072
                    //
                    _ => throw new NotImplementedException(),
                });
            });

            return services;
        }

        /// <summary>
        /// Add Azure Storage Service
        /// Functions to Upload, Download and Delete Files
        /// into Azure storage container
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">Configuration with Section "AzureStorage"</param>
        public static void AddAzureStorageService(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.Configure<AzureStorageContext>(configuration);
            services.AddScoped<IAzureFileStorageService, AzureFileStorageService>();
        }

        /// <summary>
        /// Add hangfire
        /// </summary>
        /// <param name="services">The Services collection</param>
        /// <param name="configuration">Configuration</param>
        /// <param name="env">Web host environment</param>
        /// <returns>IServiceCollection</returns>
        public static IServiceCollection AddHangfire(this IServiceCollection services, IConfiguration configuration, string databaseConnectionStringName)
        {
            services.Configure<HangfireSettings>(
                configuration.GetSection(nameof(HangfireSettings)));
            var settings = new HangfireSettings();
            configuration.GetSection(nameof(HangfireSettings)).Bind(settings);

            services.Configure<HangfireSettings>(
                configuration.GetSection(nameof(HangfireSettings)));
            services.AddHangfire(config =>
                config.UseSqlServerStorage(configuration.GetConnectionString(databaseConnectionStringName),
                new SqlServerStorageOptions
                {
                    EnableHeavyMigrations = true //migrate data storage from previous versions
                }));

            services.AddSingleton<IRecurringJobsManager, RecurringJobsManager>();

            services.AddHangfireServer(options =>
            {
                options.WorkerCount = settings.WorkerCount;
                options.Queues = [settings.Queue];
            });
            return services;
        }

        /// <summary>
        /// Add SharePoint Storage Service
        /// Functions to Upload, Download and Delete Files
        /// </summary>
        /// <param name="services">The Services collection</param>
        /// <param name="configuration">Configuration</param>
        public static void AddSharePointStorageService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<SharepointStorage, SharepointStorageService>();
            services.Configure<SharepointStorageContext>(configuration);
        }

        /// <summary>
        /// Adds pdf generator
        /// </summary>
        /// <param name="services">The Services collection</param>
        public static void AddPdfGenerator(this IServiceCollection services) => services.AddSingleton<IPdfGenerator, CustomPdfGenerator>();

        /// <summary>
        /// Add Azure DevOps Service
        /// Functions to manage Azure DevOps projects and work items
        /// </summary>
        /// <param name="services">The Services collection</param>
        /// <param name="configuration">Configuration with Section "AzureDevops"</param>
        public static void AddAzureDevOpsService<TUser>(this IServiceCollection services,
            IConfiguration configuration) where TUser : User<TUser>, IUser, new()
        {
            services.Configure<AzureDevOpsSettings>(configuration);
            services.AddScoped<IAzureDevOpsService, AzureDevOpsService<TUser>>();
            services.AddAosAutoMapper(typeof(WorkItemAutomapper));
        }

        /// <summary>
        /// Powerbi report Service
        /// Functions to export reports to different formats
        /// </summary>
        /// <param name="services">The Services collection</param>
        /// <param name="configuration">Configuration with Section "AzureStorage"</param>
        public static void AddPowerBiService(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.Configure<PowerbiContext>(configuration);

            services.AddScoped<IPowerbiApiService, PowerbiApiService>();
        }

        /// <summary>
        /// Adds AOShield SignalR
        /// </summary>
        /// <typeparam name="TUser">The type of the user</typeparam>
        /// <param name="services">The Services collection</param>
        /// <param name="onConnectedHandler">Connected Handler</param>
        /// <param name="claims">List of claims</param>
        public static void AddAosSignalR<TUser>(this IServiceCollection services, string applicationName, Func<TUser, string[]> onConnectedHandler = null,
            params string[] claims)
             where TUser : User<TUser>, IUser, new()
        {
            //SignalR
            services.AddSignalR(op =>
            {
                op.HandshakeTimeout = TimeSpan.FromSeconds(20);
            }).AddNewtonsoftJsonProtocol().AddAzureSignalR(options =>
            {
                options.ApplicationName = applicationName;
                if (claims != null)
                {
                    // pick up necessary claims
                    options.ClaimsProvider = context =>
                    {
                        return context.User is null ? [] : context.User.Claims.Where(c => claims.Contains(c.Value));
                    }; //we are not using any particular claim for hub related fucntionality
                }
                else
                {
                    options.ClaimsProvider = context => []; //we are not using any particular claim for hub related fucntionality
                }
            });
            services.AddScoped(provider =>
            {
                return new OnConnectedHandlerWrapper<TUser>(onConnectedHandler);
            });
            services.AddScoped<IChatHub, ChatHub<TUser>>();
        }

        /// <summary>
        /// Adds AOShield Common Middleware
        /// </summary>
        /// <param name="app">The app builder</param>
        /// <param name="env">The web host evironment</param>
        public static void AddAosCommonMiddleware(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Routing
            //
            app.UseRouting();
            var isDevelopment = env is not null && env.IsDevelopment();
            var isProduction = env is not null && env.IsProduction();

            if (isDevelopment)
            {
                app.UseCors("DevelopmentCORS"); //allow chat hub to work in development environment
            }
            else if (isProduction)
            {
                app.UseCors("ProductionCORS");
            }

            // Authentication and Authorization
            //
            app.UseAuthentication();
            app.UseAuthorization();
        }

        /// <summary>
        /// AOShield Messaging and Events services
        /// </summary>
        /// <param name="services">The Services collection</param>
        /// <param name="configuration">The configuration</param>
        public static void AddAosUrlSigning(this IServiceCollection services, IConfiguration configuration)
        {
            //Url sign
            services.Configure<UrlSingConfiguration>(configuration.GetSection(nameof(UrlSingConfiguration)));
            services.AddSingleton<IUrlSignService, UrlSignService>();
        }
    }
}
