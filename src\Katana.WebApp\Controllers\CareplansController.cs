﻿using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Services.Audit;
using Aoshield.Core.Services.Audit.Models;
using Aoshield.Services.Core.UrlSigning;
using Katana.Core.Entities;
using Katana.Services.CareplanResponseTemplates.Models;
using Katana.Services.Careplans;
using Katana.Services.Careplans.Models;
using Katana.Services.ClinicPractitioners.Models;
using Katana.Services.Common.Models;
using Katana.Services.Dashboards.Model;
using Katana.Services.DeclineReasons.Models;
using Katana.Services.InvoiceItems.Models;
using Katana.Services.Treatmentplans.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Careplans Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CareplansController : ControllerBase
    {
        private readonly ICareplansService _careplansService;

        private readonly IAuditStorageProvider _auditStorageProvider;

        //We are keeping this for EventsGrid further development
        //private string EventType => HttpContext.Request.Headers["aeg-event-type"].FirstOrDefault();


        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="careplansService">CRUD service</param>
        /// <param name="auditStorageProvider">Auditing Service</param>
        public CareplansController(ICareplansService careplansService,
            IAuditStorageProvider auditStorageProvider)
        {
            _careplansService = careplansService;
            _auditStorageProvider = auditStorageProvider;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<CareplanListDto>> GetCareplans([FromQuery] SieveModel query) =>
        await _careplansService.GetAsPagedResults(query);

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("cps")]
        public async Task<IPagedResults<CareplanListDto>>
            GetCpsCareplans([FromQuery] SieveModel query) =>
             await _careplansService.GetCpsAsPagedResults(query);


        /// <summary>
        /// Get entity by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns>Entity with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<CareplanDto>> GetCareplan(int id)
        {
            var dto = await _careplansService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        //We are keeping this for EventsGrid further development
        ///// <summary>
        ///// Hook for grid events
        ///// </summary>
        ///// <param name="cancellation"></param>
        ///// <returns></returns>
        //[HttpPost("EventsWebHook")]
        //[AllowAnonymous]
        //public async Task<IActionResult> EventsWebHook(CancellationToken cancellation)
        //{
        //    if (EventType.Equals("SubscriptionValidation")) //hook validation during Subscription Creation
        //    {
        //        var events = await BinaryData.FromStreamAsync(Request.Body, cancellation);
        //        var eventGridEvents = EventGridEvent.ParseMany(events);
        //        foreach (var eventGridEvent in eventGridEvents)
        //        {
        //            // Handle system events
        //            if (eventGridEvent.TryGetSystemEventData(out var eventData))
        //            {
        //                // Handle the subscription validation event
        //                if (eventData is SubscriptionValidationEventData subscriptionValidationEventData)
        //                {
        //                    var responseData = new SubscriptionValidationResponse()
        //                    {
        //                        ValidationResponse = subscriptionValidationEventData.ValidationCode
        //                    };
        //                    return new OkObjectResult(responseData);
        //                }
        //            }
        //        }
        //        return new OkObjectResult(string.Empty);
        //    }
        //    //await Task.Delay(5000);
        //    //await _careplansService.UpdateValidationStatus(cancellation);
        //    return new OkObjectResult(string.Empty);
        //}

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateCareplanDto>> PutCareplan(int id,
            UpdateCareplanDto dto,
            CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.Update(dto, cancellation: cancellation);

        /// <summary>
        /// Copy respose and Replace the specify properties
        /// </summary>
        /// <param name="id">Careplan Id</param>
        /// <param name="template">Template</param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/CopyResponseTemplate")]
        public async Task<ActionResult<CareplanResponseTemplateDto>> CopyResponseTemplate(int id,
            CareplanResponseTemplateDto template,
            CancellationToken cancellation) =>
            await _careplansService.CopyResponseTemplate(id, template.Id,
                cancellation: cancellation);


        /// <summary>
        /// Add entity
        /// </summary>
        /// <param name="addDto"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost]
        public async Task<ActionResult<AddCareplanDto>> PostCareplan(AddCareplanDto addDto,
            CancellationToken cancellation)
        {
            var dto = await _careplansService.Add(addDto, cancellation: cancellation);

            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeleteCareplan(int id,
            CancellationToken cancellation = default) =>
            await _careplansService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities with note to deleted</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _careplansService.DeleteBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _careplansService.RestoreBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestoreCareplan(int id) =>
            await _careplansService.Restore(id, notify: false);

        /// <summary>
        /// Add note to Careplan
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Notes")]
        public async Task<ActionResult<AddCareplanNoteDto>> PostNote(AddCareplanNoteDto addDto)
        {
            var dto = await _careplansService.AddNote(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Note by Id
        /// </summary>
        /// <param name="id">Note id</param>
        /// <returns></returns>
        [HttpGet("Notes/{id}")]
        public async Task<ActionResult<NoteDto>> GetNote(int id)
        {
            var dto = await _careplansService.GetNoteById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">Careplan's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Notes")]
        public async Task<IPagedResults<CareplanNoteDto>> GetNotes(int id, [FromQuery] SieveModel query) =>
            await _careplansService.GetNotesAsPagedResults(id, query);

        /// <summary>
        /// Update note
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Notes/{id}")]
        public async Task<ActionResult<UpdateCareplanNoteDto>> PutNote(int id, UpdateCareplanNoteDto dto) =>
            id != dto.Id ? BadRequest() : await _careplansService.UpdateNote(dto);

        /// <summary>
        /// Delete Note
        /// </summary>
        /// <param name="id">Id of the note to delete</param>
        /// <returns></returns>
        [HttpDelete("Notes/{id}")]
        public async Task<ActionResult<int?>> DeleteNote(int id) =>
            await _careplansService.DeleteNote(id);

        /// <summary>
        /// Delete Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to delete</param>
        /// <returns></returns>
        [HttpDelete("DeleteNoteBatch")]
        public async Task<int[]> DeleteNoteBatch(int[] ids) =>
            await _careplansService.DeleteNoteBatch(ids);

        /// <summary>
        /// Restore Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to restore</param>
        /// <returns></returns>
        [HttpDelete("RestoreNoteBatch")]
        public async Task<ActionResult<int[]>> RestoreNoteBatch(int[] ids) =>
            await _careplansService.RestoreNoteBatch(ids);

        /// <summary>
        /// Get workflow statuses
        /// </summary>
        /// <param name="id">Careplan's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Workflowstatus")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<CareplanWorkflowStatus>>>
            GetWorkflowStatusLogs(int id, [FromQuery] SieveModel query) =>
            await _careplansService.GetWorkflowStatusLogsAsPagedResults(id, query);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">Careplan's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLog(int id,
            [FromQuery] SieveModel query) =>
            await _careplansService.GetStatusLogsAsPagedResults(id, query);

        /// <summary>
        /// Update property ConsultationReason
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/ConsultationReason")]
        public async Task<ActionResult<UpdateCareplanConsultationReasonDto>>
            PutCareplanConsultationReason(int id,
                UpdateCareplanConsultationReasonDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanConsultationReason(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Update Recommendation
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/Recommendations")]
        public async Task<ActionResult<UpdateCareplanRecommendationsDto>> PutCareplanRecommendation(
            int id,
            UpdateCareplanRecommendationsDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanRecommendations(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Update Patient Medications
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/PatientMedications")]
        public async Task<ActionResult<UpdateCareplanPatientMedicationsDto>> PutCareplanPatientMedications(
            int id,
            UpdateCareplanPatientMedicationsDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanPatientMedications(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Update Patient Conditions
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/PatientConditions")]
        public async Task<ActionResult<UpdateCareplanPatientConditionsDto>> PutCareplanPatientConditions(
            int id,
            UpdateCareplanPatientConditionsDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanPatientConditions(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Update Patient Investigations
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/Investigations")]
        public async Task<ActionResult<UpdateCareplanInvestigationsDto>> PutCareplanInvestigations(
            int id,
            UpdateCareplanInvestigationsDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanInvestigations(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Update Case Notes Unified
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/CaseNotesUnified")]
        public async Task<ActionResult<UpdateCareplanCaseNotesUnifiedDto>> PutCareplanCaseNotesUnified(int id,
            UpdateCareplanCaseNotesUnifiedDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanCaseNotesUnified(dto, cancellation: cancellation);

        /// <summary>
        /// Update property CasesNotes
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/CasesNotes")]
        public async Task<ActionResult<UpdateCareplanCasesNotesDto>> PutCareplanCasesNotes(int id,
            UpdateCareplanCasesNotesDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _careplansService.UpdateCareplanCasesNotes(dto, cancellation: cancellation);

        /// <summary>
        /// Associates the Clinic and the Gp
        /// </summary>
        /// <param name="dtoArr">Care plan Ids</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("AssociateClinicGp")]
        public async Task<int> AssociateClinicGp(AddClinicPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.AssociateClinicGp(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a practitioner Sp item from/ in Careplan
        /// </summary>
        /// <param name="dtoArr">Contains id of practitioner and id(s) of Careplan(s) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignSpEcc")]
        public async Task<int> ManualAssignSpEcc(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ManualAssignSpEcc(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a practitioner Itp item from/ in Careplan
        /// </summary>
        /// <param name="dtoArr">Contains id of practitioner and id(s) of Careplan(s) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignItpEcc")]
        public async Task<int> ManualAssignItpEcc(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ManualAssignItpEcc(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Generate Careplan As Draft
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("CreateDraft")]
        public async Task<ActionResult<int>> CreateEccCareplanAsDraft(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.CreateEccAsCareplanAsDraft(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Approve Careplan on Draft and Continue to Itp Pending
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("ContinueToItpEcc")]
        public async Task<ActionResult<int>> ContinueEccCareplanToItpPending(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ContinueEccCareplanToItpPending(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Continue careplan on workflow
        /// Approve Careplans by Itp
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("ApproveItp")]
        public async Task<ActionResult<int>> ApproveItp(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ApproveItp(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Approve Careplans by Sp
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("ApproveSp")]
        public async Task<ActionResult<int>> ApproveSp(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ApproveSp(dtoArr, cancellation: cancellation);

        ///// <summary>
        ///// Continue careplan on workflow
        ///// </summary>
        ///// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        ///// <param name="cancellation">Cancellation token</param>
        ///// <returns></returns>
        //[HttpPut("Continue")]
        //public async Task<ActionResult<int>> ContinueCareplan(BaseUpdateDto[] dtoArr,
        //    CancellationToken cancellation) =>
        //    await _careplansService.ContinueCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a assigned user in Careplan
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the careplan(s) assigned by the use to review it</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("PickUp")]
        public async Task<List<int>> PickUpCareplanByUser(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.PickUp(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Realese Careplan
        /// </summary>
        /// <param name="dtoArr">Release the identification of the careplan(s) assigned by the use to review it</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Release")]
        public async Task<List<int>> ReleaseCareplan(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ReleaseCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// BackInPool action
        /// </summary>
        /// <param name="dtoArr">Careplans ids</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("BackInPool")]
        public async Task<ActionResult<int>>
            BackInPool(BaseUpdateDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.BackInPool(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Duplicated action
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("Reloop")]
        public async Task<ActionResult<int>> Reloop(CareplanReloopActionDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ReloopEcc(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Send Back Careplans
        /// </summary>
        /// <param name="dtoArr">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("SendBack")]
        public async Task<ActionResult<int>> SendBackCareplan(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.SendBackCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Cancel careplan
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the cp(s) and a note to add to each entity</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Treatmentplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Cancel")]
        public async Task<int> CancelCareplan(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.CancelCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Get list of attachments
        /// </summary>
        /// <param name="id">Careplan's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Attachments")]
        public async Task<IPagedResults<BlobItemDto>> GetListFiles(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _careplansService.ListFiles(id, query, cancellation);

        /// <summary>
        /// Upload attachments
        /// </summary>
        /// <param name="id">Careplan's id</param>
        /// <param name="files">List of files to upload</param>
        /// <param name="overrideExisting">Override file if existing</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>
        [HttpPost("{id}/Attachments")]
        public async Task<ActionResult> UploadFiles(int id, [FromForm] List<IFormFile> files,
            bool overrideExisting, CancellationToken cancellation)
        {
            var blobItems =
                await _careplansService.UploadFiles(id, files, overrideExisting, cancellation);
            return Ok((FileName: string.Join(",", blobItems.Select(f => f.Name)), Result: "Ok"));
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("Attachments/{id}/uri")]
        public Task<ActionResult> GetFileSasUri(int id, [FromQuery] string fileName)
        {
            var result = _careplansService.GeneratePublicUri(id, fileName);
            return Task.FromResult<ActionResult>(Ok(new { Url = result.ToString() }));
        }

        /// <summary>
        /// Delete attachment
        /// </summary>
        /// <param name="dtos"> DeleteStorageFileDto []</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>BlobItemDto</returns>
        [HttpDelete("Attachments")]
        public async Task<ActionResult<BlobItemDto[]>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) =>
            Ok(await _careplansService.DeleteFiles(dtos, cancellation));

        /// <summary>
        /// Generate Care plan from Fax
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of fax(es)</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan Dto</returns>
        [HttpPut("Generate")]
        public async Task<ActionResult<int>> GenerateFromFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.GenerateFromFax(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Get all GP mine careplans
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("GPMine")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansGPMine([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetCareplansForGP(query, cancellation);

        /// <summary>
        /// Get all ITP mine careplans
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("ITPMine")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansITPMine([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetCareplansForITP(query, cancellation);

        /// <summary>
        /// Get all SP mine careplans
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("SPMine")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansSPMine([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetCareplansForSP(query, cancellation);

        /// <summary>
        /// Get SP approved summary by quota
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("ApprovedSummary")]
        public async Task<ActionResult<QuotasCardDto>> GetApprovedSummary(
            CancellationToken cancellation = default)
        {
            var result = await _careplansService.GetApprovedSummary(cancellation);
            return Ok(result);

        }

        /// <summary>
        /// Get SP requested summary by quota
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("RequestedSummary")]
        public async Task<ActionResult<QuotasCardDto>> GetRequestedSummary(
            CancellationToken cancellation = default)
        {
            var result = await _careplansService.GetRequestedSummary(cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Get Download signed link
        /// </summary>
        /// <param name="id"></param>
        /// <param name="urlSignService"></param>
        /// <returns></returns>
        [HttpGet("PdfVersionLink/{id}")]
        [Authorize]
        public ActionResult GetPdfVersionLink([FromRoute] int id,
            [FromServices] IUrlSignService urlSignService)
        {
            var relativeEndpoint = $"api/careplans/PdfVersion/{id}";
            var endpoint = $"{Request.Scheme}://{Request.Host.Value}/{relativeEndpoint}";
            var signedUrl = urlSignService.SingUrl(endpoint);
            return Ok(new { Url = signedUrl });
        }

        /// <summary>
        /// Download PDF version
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("PdfVersionUrl/{id}")]
        public async Task<UrlDto> GetPdfVersionUrl([FromRoute] int id,
            CancellationToken cancellation)
        {
            var url = await _careplansService.GetPdfVersionUrl(id, cancellation);
            return new UrlDto { Url = url };
        }

        /// <summary>
        /// Automatic Uplaod Fax Delivery
        /// </summary>
        /// <param name="dtoArr">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AutomaticUploadFaxDelivery")]
        public async Task<ActionResult<int>> AutomaticUploadFaxDelivery(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.AutomaticUploadFaxDelivery(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a care plan OCode from/ in billing
        /// </summary>
        /// <param name="dtoArr">Contains id of care plan to excludes or includes from/ in billing</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("DoNotBillOCode")]
        public async Task<int> DoNotBillOCode(DoNotBillOCodeDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.DoNotBillOCode(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a care plan RCode from/ in billing
        /// </summary>
        /// <param name="dtoArr">Contains id of care plan to excludes or includes from/ in billing</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("DoNotBillRCode")]
        public async Task<int> DoNotBillRCode(DoNotBillRCodeDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.DoNotBillRCode(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.DisableBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.EnableBatch(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Adjustment to Careplan Request Date
        /// </summary>
        /// <param name="dtoArr">Identification of the careplan(s) to adjust the request date</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AdjustmentRequestDate")]
        public async Task<int> AdjustmentCareplanRequestDate(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.AdjustCareplanRequestDateEcc(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Auto Assign SPs for Ecc Workflow
        /// </summary>
        /// <param name="dtoArr">Identification of the careplan(s) to assign sps automatically</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from over posting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AutoAssignSp")]
        public async Task<int> AutoAssignSpEcc(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.AutoAssignSpEcc(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Assign specialty
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("assignSpecialty")]
        public async Task<int> AssignSpecialtyEccCareplan(
            [FromBody] AssignSpecialtyToCareplanDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.AssignSpecialtyEccCareplan(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Get attention care plans
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>PagedResults</returns>
        [HttpGet("Attention")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansAttention([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetCareplansAttention(query, cancellation);

        /// <summary>
        /// Get care plans assigned to the authenticated user or blocked by the authenticated user and that are from an expedited request
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>PagedResults</returns>
        [HttpGet("AttentionMine")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansAttentionMine([FromQuery] SieveModel query,
            CancellationToken cancellation = default) => await _careplansService.GetCareplansAttentionMine(query, cancellation);

        /// <summary>
        /// Create Audit Careplans Pdf Export Request
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="cancellationToken"> </param>
        [HttpGet("createAuditCareplansPdfExportRequest")]
        public async Task<ActionResult<AuditCareplanExportRequestDto>> CreateAuditCareplansPdfExportRequest(AddAuditCareplanExportRequestDto dto,
            CancellationToken cancellationToken) => Ok(await _careplansService.CreateAuditCareplansPdfExportRequest(dto, cancellationToken));

        /// <summary>
        /// Retry Processing Audit Careplans Pdf Export Request Failed
        /// </summary>
        /// <param name="cancellationToken"> </param>
        [HttpPost("retryAuditCareplansPdfExportRequest")]
        public async Task<ActionResult<int>> RetryByExportAuditCareplanPdfsFailed(CancellationToken cancellationToken)
            => Ok(await _careplansService.RetryByExportAuditCareplanPdfsFailed(cancellationToken));

        /// <summary>
        /// Get All InvoiceItems by careplan id
        /// </summary>
        /// <param name="id">Careplan Id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of items</returns>
        [HttpGet("{id}/InvoiceItems")]
        public async Task<IPagedResults<CareplanInvoiceItemDto>> GetInvoiceItemsByCareplanId(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _careplansService.GetInvoiceItemsByCareplanId(id, query, cancellation);

        /// <summary>
        /// Get care plans for service provider responses.
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>PagedResults</returns>
        [HttpGet("SpResponse")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplansForReviewSpResponses([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetCareplansForReviewSpResponses(query, cancellation);

        /// <summary>
        /// Complete Sp Response for GPs
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the careplan(s) and the note to add to each entity</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Count of careplans processed</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("ReviewSpResponse")]
        public async Task<int> ReviewSpResponse(ReviewSpResponseDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.ReviewSpResponseCpsCareplan(dtoArr, cancellation);

        /// <summary>
        /// Get Patient Medications of careplan by id
        /// </summary>
        /// <param name="careplanId">Careplan's id</param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("PatientMedications/{careplanId}")]
        public async Task<CareplanPatientMedicationsDto> GetCareplanPatientMedications(int careplanId, CancellationToken cancellation)
            => await _careplansService.GetCareplanPatientMedications(careplanId, cancellation);

        /// <summary>
        /// Get Patient Conditions of careplan by id
        /// </summary>
        /// <param name="careplanId">Careplan's id</param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("PatientConditions/{careplanId}")]
        public async Task<CareplanPatientConditionsDto> GetCareplanPatientConditions(int careplanId, CancellationToken cancellation)
            => await _careplansService.GetCareplanPatientConditions(careplanId, cancellation);

        /// <summary>
        /// Get Investigations of careplan by id
        /// </summary>
        /// <param name="careplanId">Careplan's id</param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("Investigations/{careplanId}")]
        public async Task<CareplanInvestigationsDto> GetCareplanInvestigations(int careplanId, CancellationToken cancellation)
            => await _careplansService.GetCareplanInvestigations(careplanId, cancellation);

        /// <summary>
        /// Assign Diagnostic
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("assignDiagnostic1")]
        public async Task<int> AssignDiagnosticCpsCareplan([FromBody] AssignDiagnosticDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.AssignDiagnosticCpsCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Send Back Clinical Questions to Dispatch
        /// </summary>
        /// <param name="dtoArr">Contain the id(s) of Careplan(s) to approve</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("SendBackDispatch")]
        public async Task<ActionResult<int>> SendBackClinicalQuestionsToFullDispatch(SendBackToItpDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.SendBackCpsCareplanToFullDispatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Close Clinical Question
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the clinical question(s) to close it with the status</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Treatmentplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("CloseClinicalQuestion")]
        public async Task<int> CloseClinicalQuestion(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.CloseClinicalQuestion(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Get all unanswered questions
        /// </summary>
        /// <param name="query">Sieve parameters</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("UnansweredClinicalQuestions")]
        public async Task<IPagedResults<CareplanListDto>> GetUnansweredClinicalQuestions([FromQuery] SieveModel query,
            CancellationToken cancellation = default) =>
            await _careplansService.GetUnansweredClinicalQuestions(query, cancellation);

        /// <summary>
        /// ReAsk Clinical Question
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellationToken"></param>
        [HttpPost("ReAskClinicalQuestion")]
        public async Task<ReAskClinicalQuestionDto> ReAskClinicalQuestion(ReAskClinicalQuestionDto dtoArr, CancellationToken cancellationToken = default) =>
            await _careplansService.ReAskClinicalQuestion(dtoArr, cancellationToken);

        /// <summary>
        /// Reloop to Itp
        /// </summary>
        /// <param name="dtoArr">Contain the id of Careplan to send back</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("ReloopToItp")]
        public async Task<ActionResult<int>> ReloopToItp(SendBackToItpDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.ReloopCareplanToItpForNewGpWorkflow(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Reloop to Sp
        /// </summary>
        /// <param name="dtoArr">Contain the id of Careplan to send back</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("ReloopToSp")]
        public async Task<ActionResult<int>> ReloopToSp(SendBackToSpDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.ReloopCareplanToSpForNewGpWorkflow(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a practitioner Itp item from/ in Careplan
        /// </summary>
        /// <param name="dtoArr">Contains id of practitioner and id(s) of Careplan(s) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("ChangeSpItpNewGPFlow")]
        public async Task<int> ManualAssignItpCpsCareplan(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ManualAssignItpCpsCareplan(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Excludes or includes a practitioner sp item from/ in Careplan
        /// </summary>
        /// <param name="dtoArr">Contains id of practitioner and id(s) of Careplan(s) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("ChangeSpNewGPFlow")]
        public async Task<int> ManualAssignSpNewGpWorkflow(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.ManualAssignSpNewGpWorkflow(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Set confirmed review for careplans
        /// </summary>
        /// <param name="dtoArr">Array of ConfirmedReviewDto</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Task of int</returns>
        [HttpPut("ConfirmedReview")]
        public async Task<int> SetConfirmedReview(ConfirmedReviewDto[] dtoArr, CancellationToken cancellation) =>
            await _careplansService.SetConfirmedReview(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Decline Clinical Questions
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the treatmentpla(s) to decline it with the status</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Treatmentplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("DeclineClinicalQuestion")]
        public async Task<int> DeclineClinicalQuestion(DeclineClinicalQuestionDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.DeclineClinicalQuestion(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Generate Clinical Question
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPost("GenerateClinicalQuestionFromPatient")]
        public async Task<ActionResult<AddClinicalQuestionDto[]>> GenerateClinicalQuestionFromPatient(AddClinicalQuestionDto[] dto, CancellationToken cancellation = default)
        {
            var result = await _careplansService.GenerateClinicalQuestionFromPatient(dto, cancellation);
            return StatusCode(StatusCodes.Status201Created, result);
        }

        /// <summary>
        /// Generate Clinical Question
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPost("GenerateClinicalQuestion")]
        public async Task<ActionResult<AddClinicalQuestionDto[]>> GenerateClinicalQuestion(AddClinicalQuestionDto[] dto, CancellationToken cancellation = default)
        {
            var result = await _careplansService.GenerateClinicalQuestion(dto, cancellation);
            return StatusCode(StatusCodes.Status201Created, result);
        }

        /// <summary>
        /// Approve Clinical Question
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellationToken"></param>
        [HttpPut("ApproveClinicalQuestion")]
        public async Task<int> ApproveClinicalQuestion(BaseUpdateDto[] dtoArr, CancellationToken cancellationToken = default) => await _careplansService.ApproveClinicalQuestion(dtoArr, cancellationToken);

        /// <summary>
        /// Update Clinical Question
        /// </summary>
        /// <param name="dtos"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("ClinicalQuestion")]
        public async Task<ActionResult<int>> UpdateClinicalQuestion(
            UpdateClinicalQuestionDto[] dtos, CancellationToken cancellation) =>
             await _careplansService.UpdateClinicalQuestion(dtos, cancellation: cancellation);

        /// <summary>
        /// Get Clinical Questions by Patient
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("{patientId}/ClinicalQuestions")]
        public async Task<IPagedResults<ClinicalQuestionDto>> GetClinicalQuestionsByPatient(int patientId, [FromQuery] SieveModel query,
            CancellationToken cancellation = default) => await _careplansService.GetClinicalQuestionsByPatient(patientId, query, cancellation);

        /// <summary>
        /// Get entity by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation"></param>
        /// <returns>Entity with provided id</returns>
        [HttpGet("{id}/ClinicalQuestion")]
        public async Task<ActionResult<TreatmentplanDto>> GetClinicalQuestion(int id, CancellationToken cancellation = default)
        {
            var dto = await _careplansService.GetClinicalQuestionById(id, cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get clinical questions
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>TreatmentplanDto</returns>
        [HttpGet("ClinicalQuestions")]
        public async Task<IPagedResults<ClinicalQuestionDto>> GetClinicalQuestions([FromQuery] SieveModel query, CancellationToken cancellation = default) => await _careplansService.GetClinicalQuestions(query, cancellation);

        /// <summary>
        /// Get decline reasons for clinical questions
        /// </summary>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Decline reasons</returns>
        [HttpGet("DeclineReasonForCQ")]
        public async Task<IPagedResults<DeclineReasonDto>> GetDeclineReasonForCQ([FromQuery] SieveModel query, CancellationToken cancellation = default) => await _careplansService.GetDeclineReasonForCQ(query, cancellation);

        /// <summary>
        /// Dispatch Clinical Questions for Dev
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("dispatchClinicalQuestionsDev")]
        public async Task<ActionResult<int>> DispatchClinicalQuestionsForTest(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _careplansService.DispatchClinicalQuestionsForTests(dtoArr, cancellation: cancellation);

        ///// <summary>
        ///// Migrate Dispatched CQ To CP
        ///// </summary>
        ///// <returns>List</returns>
        //[HttpGet("MigrateDispatchedCQToCP")]
        //public async Task<bool> MigrateDispatchedCQToCP([FromQuery] string patientsIds = null, CancellationToken cancellation = default)
        //{
        //    await _careplansService.MigrateDispatchedCQToCP(patientsIds, cancellation);
        //    return true;
        //}

        ///// <summary>
        ///// Get all entities
        ///// </summary>
        ///// <returns>List</returns>
        //[HttpGet("MigrateNonDispatchedCQToCP")]
        //public async Task<bool> MigrateNonDispatchedCQToCP([FromQuery] string patientsIds = null, CancellationToken cancellation = default)
        //{
        //    await _careplansService.MigrateNonDispatchedCQToCP(patientsIds, cancellation);
        //    return true;
        //}

        ///// <summary>
        ///// Get all entities
        ///// </summary>
        ///// <returns>List</returns>
        //[HttpGet("MigratePendingWorkflowStatusLogs")]
        //public async Task<bool> MigratePendingWorkfloStatusLogs(CancellationToken cancellation = default)
        //{
        //    await _careplansService.MigratePendingWorkflowStatusLogs(cancellation);
        //    return true;
        //}

        /// <summary>
        /// Get available/assigned careplans
        /// </summary>
        /// <param name="query"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("AvailableCareplans")]
        public async Task<IPagedResults<CareplanListDto>> AvailableCareplans([FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _careplansService.GetAvailableCareplans(query, cancellation: cancellation);

        /// <summary>
        /// Get upcoming careplans for SP approval
        /// </summary>
        /// <param name="query"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("UpcomingForSP")]
        public async Task<IPagedResults<CareplanListDto>> UpcomingForSP([FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _careplansService.GetUpcomingCareplansForSPs(query, cancellation: cancellation);

        /// <summary>
        /// Download UploadFax PDF version
        /// </summary>
        /// <param name="id">Fax Id</param>
        /// <param name="cancellation">Cancellation Token</param>
        /// <returns>Upload Fax Pdf Version Url</returns>
        [HttpGet("UploadFaxPdfVersionUrl/{id}")]
        public async Task<UrlDto> GetUploadFaxPdfVersionUrl([FromRoute] int id,
            CancellationToken cancellation)
        {
            var url = await _careplansService.GetUploadFaxPdfVersion(id, cancellation);
            return new UrlDto { Url = url };
        }

        /// <summary>
        /// Process Upload Faxes
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPost("ProcessUploadFaxes")]
        public async Task<ActionResult> ProcessUploadFaxes(CancellationToken cancellation)
        {
            var result = await _careplansService.CronProcessUploadFax(cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Process Upload Faxes
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("UpdateCareplansToDoNotBill")]
        public async Task<int> UpdateCareplansToDoNotBill(CancellationToken cancellation) => await _careplansService.UpdateCareplansToDoNotBill(cancellation);

        /// <summary>
        /// GetPagedEntityChanges
        /// </summary>
        /// <param name="id"></param>
        /// <param name="filters"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("Audit/{id?}")]
        public async Task<IPagedResults<ListEntityChangeLogDto>> GetPagedEntityChanges(int? id,
            [FromQuery] ChangeLogQuery filters, CancellationToken cancellation) =>
            await _auditStorageProvider.GetPagedEntityChanges<Patient>(id, filters, cancellation);

        /// <summary>
        /// GetPagedEntityChanges
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="endDate"></param>
        /// <param name="cancellation"></param>
        /// <param name="startDate"></param>
        /// <returns></returns>
        [HttpGet("TimeLine/{entityId}")]
        public async Task<EntityChangesDto> GetPagedEntityChanges(int entityId,
            DateTime? startDate = null, DateTime? endDate = null,
            CancellationToken cancellation = default) =>
            await _auditStorageProvider.GetEntityChanges<Patient>(entityId,
            startDate, endDate, cancellation);

        /// <summary>
        /// GetSnapShot
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("SnapShot/{entityId}")]
        public async Task<EntitySnapshotDto> GetSnapShot(int entityId,
            CancellationToken cancellation = default)
        {
            var entity = new Careplan { Id = entityId };
            var date = DateTime.UtcNow;
            return await _auditStorageProvider.GetEntitySnapshot(entity, date, cancellation);
        }
    }
}
