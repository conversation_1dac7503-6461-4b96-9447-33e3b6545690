import { Gender } from "../BaseUserEntityDto";
import { CareplanCommonDto } from "../Common/CareplanCommonDto";
import { InvoiceCommonDto } from "../Invoices/InvoiceCommonDto";
import { PatientDto } from "../Patients/PatientDto";
import { InvoiceItemAhsDto } from "./InvoiceItemAhsDto";

export enum InvoiceItemBillingStatus {
    Billed = 0,
    NotBilled = 1,
    Rejected = 2,
    Underpaid = 3,
}

export interface InvoiceItemDto extends InvoiceItemAhsDto {
    invoice: InvoiceCommonDto;
    careplan: CareplanCommonDto | null;
    faxId: number | null;
    faxName: string;
    clinicId: number | null;
    clinicName: string;
    patient: PatientDto;
    patientId: number | null;
    patientPhn: string;
    patientFirstName: string;
    patientLastName: string;
    patientGender: Gender | null;
    patientAddress: string;
    patientCity: string;
    patientPhnProvinceId: number | null;
    patientPhnProvinceAbbrev: string;
    gpId: number | null;
    gpPractId: number | null;
    gpDisplayName: string;
    spId: number | null;
    spPractId: number | null;
    spDisplayName: string;
    itpId: number | null;
    itpDisplayName: string;
    typeId: number | null;
    typeName: string;
    diagnostic1Id: number | null;
    diagnostic1Name: string;
    diagnostic1Code: string;
    requestDate: string | null;
    responseDate: string | null;
    itpApprovedDate: string | null;
    doNotBill: boolean;
    ignoreErrors: boolean;
    billedDate: string | null;
    billingStatus: InvoiceItemBillingStatus | null;
    billingClinicId: number | null;
    billingClinicName: string;
    baNumber: string;
    duplicated: boolean;
    encounterCount: number | null;
    validationNotesFilter: string;
    expired: boolean;
    expiredCount: number;
    notesCount: number;
    sharedPercentage: number;
    billingSettingId: number | null;
}
