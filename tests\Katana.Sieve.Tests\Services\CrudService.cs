﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Katana.Sieve.Tests.Services
{
    /// <summary>
    /// CRUD Service
    /// </summary>
    /// <remarks>
    /// Constructor
    /// </remarks>
    /// <param name="dbContext"></param>
    /// <param name="sieveProcessor"></param>
    /// <param name="options"></param>
    public class CrudService(ApplicationDbContext dbContext, ISieveProcessor sieveProcessor, IOptions<SieveOptions> options)
    {
        private readonly ApplicationDbContext _dbContext = dbContext;
        private readonly ISieveProcessor _sieveProcessor = sieveProcessor;
        private readonly IOptions<SieveOptions> _options = options;

        /// <summary>
        /// Get all entities with pagination
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="query"></param>
        /// <param name="sieveProcessor"></param>
        /// <returns></returns>
        public async Task<IPagedResults<TEntity>> GetAsPagedResults<TEntity>(SieveModel query, ISieveProcessor sieveProcessor = null) where TEntity : class
        {
            var source = _dbContext.Set<TEntity>().AsNoTracking();
            var processor = sieveProcessor ?? _sieveProcessor;

            return await processor.GetPagedResultsAsync(query, source, _options);
        }

        /// <summary>
        /// Get all entities with pagination
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <returns></returns>
        public async Task<IList<TEntity>> GetAll<TEntity>() where TEntity : class
        {
            var source = _dbContext.Set<TEntity>().AsNoTracking();

            return await source.ToListAsync();
        }
    }
}
