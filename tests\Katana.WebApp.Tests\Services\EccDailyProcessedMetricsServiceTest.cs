﻿using Aoshield.Core.Entities.Models;
using Katana.Services.EccDailyProcessedMetrics;
using Katana.Services.EccDailyProcessedMetrics.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// EccDailyProcessedMetric CRUD service
    /// </summary>
    public class EccDailyProcessedMetricsServiceTest : IEccDailyProcessedMetricsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddEccDailyProcessedMetricDto> Add(AddEccDailyProcessedMetricDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddEccDailyProcessedMetricDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<EccDailyProcessedMetricDto>> GetAsPagedResults(
            SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<EccDailyProcessedMetricDto>([],
                    default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<EccDailyProcessedMetricDto> GetById(int id,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new EccDailyProcessedMetricDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateEccDailyProcessedMetricDto> Update(UpdateEccDailyProcessedMetricDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateEccDailyProcessedMetricDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id)
                    .ToArray(), cancellation);

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id)
                    .ToArray(), cancellation);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id, cancellation);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
