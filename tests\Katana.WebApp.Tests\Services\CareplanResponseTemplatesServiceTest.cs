﻿using Aoshield.Core.Entities.Models;
using Katana.Services.CareplanResponseTemplates;
using Katana.Services.CareplanResponseTemplates.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// CareplanResponseTemplate CRUD service
    /// </summary>
    public class CareplanResponseTemplatesServiceTest : ICareplanResponseTemplatesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddCareplanResponseTemplateDto> Add(AddCareplanResponseTemplateDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddCareplanResponseTemplateDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<CareplanResponseTemplateDto>> GetAsPagedResults(
            SieveModel query, CancellationToken cancellation = default)
            => await Task.Run(() =>
                new PagedResults<CareplanResponseTemplateDto>(
                    [], default, default, default, default,
                    default, default, default, default));

        /// <inheritdoc/>
        public async Task<CareplanResponseTemplateDto> GetById(int id,
            CancellationToken cancellation = default)
            => await Task.Run(() => id <= 0 ? null : new CareplanResponseTemplateDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateCareplanResponseTemplateDto> Update(
            UpdateCareplanResponseTemplateDto dto, CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateCareplanResponseTemplateDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id)
                .ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id)
                .ToArray());

        ///<inheritdoc/>
        public async Task<IPagedResults<CareplanResponseTemplateDto>>
            GetResponseTemplatesByPractitioner(int? practitionerId, SieveModel query,
                CancellationToken cancellation = default)
            => await Task.Run(() =>
                new PagedResults<CareplanResponseTemplateDto>(
                    [], default, default, default, default,
                    default, default, default, default));

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
