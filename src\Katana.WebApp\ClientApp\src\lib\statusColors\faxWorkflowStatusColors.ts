import { FaxWorkflowStatus } from "./../models/Faxes/FaxWorkflowStatus";
import {
    BlueStatus,
    DeepOrange,
    GreyStatus,
    OrangeStatus,
    RedStatus,
    TealStatus,
    YellowStatus,
} from "./allStatusColors";

export const FaxWorkflowStatusColors = new Map<FaxWorkflowStatus, string>();
FaxWorkflowStatusColors.set(FaxWorkflowStatus.New, BlueStatus);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.Grouped, OrangeStatus);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.Assigned, TealStatus);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.BeingProcess, DeepOrange);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.Pending, YellowStatus);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.Processed, GreyStatus);
FaxWorkflowStatusColors.set(FaxWorkflowStatus.Cancelled, RedStatus);
