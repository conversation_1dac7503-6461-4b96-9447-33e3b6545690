﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Validation;
using Aoshield.Services.ServicesBus.Entities;
using Bogus;
using Katana.Core.Entities;

namespace Katana.Tests.Common.Utils.Seeders;

/// <summary>
/// Test Data Seeder.
/// </summary>
/// <remarks>
/// Main constructor.
/// </remarks>
/// <param name="katanaDbContext">Context</param>
/// <param name="entitiesQty">Dictionary with the number of entities to create per entity type.</param>
/// <param name="maxQtyPerEntity">
/// Max number of entities to create per entity type, if EntitiesQty is null
/// or if there not a specified quantity for a given entity type.
/// </param>
public partial class TestDataSeeder(IKrudder<User> katanaDbContext, IDictionary<Type, int> entitiesQty = null, int maxQtyPerEntity = 10)
{
    /// <summary>
    /// Max number of entities to create per entity type, if EntitiesQty is null
    /// or if there not a specified quantity for a given entity type.
    /// </summary>
    public int MaxQtyPerEntity { get; } =
            maxQtyPerEntity; // default is 400, which is enough to have pagination (maxpage is set to 300 in Sieve config). For test ajust this value in 10

    /// <summary>
    /// Dictionary with the number of entities to create per entity type.
    /// </summary>
    public IDictionary<Type, int> EntitiesQty { get; } = entitiesQty ?? new Dictionary<Type, int>();

    /// <summary>
    /// Krudder object used by the seeder.
    /// </summary>
    public IKrudder<User> Krudder { get; } = katanaDbContext;

    /// <summary>
    /// Returns the number of objects of a given Entity type in the test DB
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public int GetEntityQty<T>() =>
        EntitiesQty.TryGetValue(typeof(T), out var qty) ? qty : MaxQtyPerEntity;

    private static int GetRandomId(int maxValue) => new Random().Next(0, maxValue);

    /// <summary>
    /// Returns the random value of a enum specification.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private static T RandomEnumValue<T>() => Enum.GetValues(typeof(T)).Cast<T>()
        .OrderBy(x => new Random().Next()).FirstOrDefault();


    private void SeedEntity<T>(Func<IList<T>> factory) where T : BaseEntity
    {
        var entities = factory.Invoke();
        Krudder.AddBatch(entities.ToList(), null).Wait();

        EntitiesQty[typeof(T)] = entities.Count;
    }

    #region Seeders

    /// <summary>
    /// Seed database.
    /// </summary>
    /// <param name="skipLast">
    /// When adding entities relations, the skipLast elements are left without any parent-to-child DB relation.
    /// This enables easy mutatable operations that may have DB contrains, such as DELETEs.
    /// Default is 1 -- the last entity of each type is skipped from being related.
    ///
    /// Example:
    ///   If we have 50 Countries and 100 Provinces, where the Provice entity depends on Country, the data
    ///   seeders will leave the last skipLast Countries without any relations to Provinces. This leaves
    ///   the last skipLast Country IDs without relations.
    /// </param>
    public void Seed(int? skipLast = null)
    {
        // Make sure you call the seeders in the right order so that
        // seeders of child entities can link the new objects to already-created parent entities.
        // E.g. If Provices depend on Countries, by the time you seed Provice entities,
        // the Country entities should already been created.
        //
        SeedCountries(GetEntityQty<Country>());
        SeedProvinces(GetEntityQty<Province>(), skipLast);
        SeedDiagnostics(GetEntityQty<Diagnostic>());
        SeedDiagnosticAlias(GetEntityQty<DiagnosticAlias>(), skipLast);
        SeedEducationalCareplan(GetEntityQty<EducationalCareplan>());
        SeedUsers(GetEntityQty<User>());
        SeedReferralSpecialties(GetEntityQty<ReferralSpecialty>());
        SeedReferralSpecialists(GetEntityQty<ReferralSpecialist>(), skipLast);
        SeedNotes<ReferralSpecialistNote>(GetEntityQty<ReferralSpecialistNote>());
        SeedTStatusLogs<ReferralSpecialistStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedClinics(GetEntityQty<Clinic>(), skipLast);
        SeedNotes<ClinicNote>(GetEntityQty<ClinicNote>());
        SeedTStatusLogs<ClinicStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedPractitioners(GetEntityQty<Practitioner>(), skipLast);
        SeedTStatusLogs<PractitionerStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedNotes<PractitionerNote>(GetEntityQty<PractitionerNote>());
        SeedClinicPractitioners(GetEntityQty<ClinicPractitioner>());
        SeedPatients(GetEntityQty<Patient>(), skipLast);
        SeedNotes<PatientNote>(GetEntityQty<PatientNote>());
        SeedTStatusLogs<PatientStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedCareplans(GetEntityQty<Careplan>(), skipLast);
        SeedNotes<CareplanNote>(GetEntityQty<BaseNoteEntity>());
        SeedTStatusLogs<CareplanStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedTStatusLogs<CareplanWorkflowStatusLog, CareplanWorkflowStatus>(
            CareplanWorkflowStatus.Draft,
            GetEntityQty<BaseStatusLogEntity<CareplanWorkflowStatus, User>>());
        SeedFaxes(GetEntityQty<Fax>(), skipLast);
        SeedNotes<FaxNote>(GetEntityQty<FaxNote>());
        SeedTStatusLogs<FaxStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedReferral(GetEntityQty<Referral>());
        SeedNotes<ReferralNote>(GetEntityQty<ReferralNote>());
        SeedTStatusLogs<ReferralStatusLog, Status>(Status.Enabled,
            GetEntityQty<BaseStatusLogEntity<Status, User>>());
        SeedTStatusLogs<ReferralWorkflowStatusLog, ReferralWorkflowStatus>(
            ReferralWorkflowStatus.Open,
            GetEntityQty<BaseStatusLogEntity<ReferralWorkflowStatus, User>>());
        SeedTStatusLogs<FaxWorkflowStatusLog, FaxWorkflowStatus>(FaxWorkflowStatus.New,
            GetEntityQty<BaseStatusLogEntity<FaxWorkflowStatus, User>>());
        SeedTranscribersGroup(GetEntityQty<TranscribersGroup>());
        SeedTranscribersManager(GetEntityQty<TranscribersManager>(), skipLast);
        SeedTranscriber(GetEntityQty<Transcriber>());
        SeedUnitsOfMeasurement(GetEntityQty<UnitOfMeasurement>());
        SeedMedications(GetEntityQty<Medication>(), skipLast);
        SeedSocialHistories(GetEntityQty<SocialHistory>());
        SeedPatientSocialHistories(GetEntityQty<PatientSocialHistory>());
        SeedVitalSigns(GetEntityQty<VitalSign>());
        SeedPatientDiagnostic(GetEntityQty<PatientDiagnostic>());
        SeedPatientMedication(GetEntityQty<PatientMedication>(), skipLast);
        SeedPatientVitalSigns(GetEntityQty<PatientVitalSign>());
        SeedAllergies(GetEntityQty<Allergy>());
        SeedInquiryForm(GetEntityQty<InquiryForm>());
        SeedPatientAllergies(GetEntityQty<PatientAllergy>());
        SeedNotes<TreatmentplanNote>(GetEntityQty<TreatmentplanNote>());
        SeedEccDailyProcessedMetrics(GetEntityQty<EccDailyProcessedMetric>());
        SeedNotificationTemplates();
        SeedCareplanResponseTemplates(GetEntityQty<CareplanResponseTemplate>(),
            skipLast);
        SeedInquiryFormSteps(GetEntityQty<InquiryFormStep>(), skipLast);
        SeedInquiryFormQuestion(GetEntityQty<InquiryFormQuestion>(), skipLast);
        SeedDeadLetterMessages(GetEntityQty<DeadLetterMessage>());
    }

    /// <summary>
    /// Seed Notes for entities
    /// Generic method to create a EntityNote
    /// </summary>
    /// <param name="qty"></param>
    /// <param name="factory"></param>
    public void SeedNotes<TNote>(int? qty = null,
        Func<IList<TNote>> factory = null)
        where TNote : BaseNoteEntity
    {
        var _factory = factory ?? (() =>
            new Faker<TNote>()
                .RuleFor(p => p.ParentId, x => 1)
                .RuleFor(n => n.Notes,
                    x => x.Random.ClampString(x.Lorem.Paragraph(), StringLengths.Medium))
                .RuleFor(p => p.AuthorId, Krudder.GetCurrentUserId().Result)
                .Generate(qty ?? GetEntityQty<TNote>()));

        SeedEntity(_factory);
    }

    /// <summary>
    /// Seed Entity Status Log and Workflowstaus for entities
    /// </summary>
    /// <param name="status"></param>
    /// <param name="qty"></param>
    /// <param name="factory"></param>
    public void SeedTStatusLogs<TStatusLog, TEnum>(TEnum status,
        int? qty = null, Func<IList<TStatusLog>> factory = null)
        where TStatusLog : BaseStatusLogEntity<TEnum, User> where TEnum : Enum
    {
        var Count = 1;
        var _factory = factory ?? (() =>
            new Faker<TStatusLog>()
                .RuleFor(p => p.ParentId, x => Count++)
                .RuleFor(p => p.Notes, x => x.Lorem.Lines(3))
                .RuleFor(p => p.Value, x => status)
                .RuleFor(p => p.AuthorId, x => Krudder.GetCurrentUserId().Result)
                .Generate(qty ?? GetEntityQty<TStatusLog>()));

        SeedEntity(_factory);
    }

    #endregion
}
