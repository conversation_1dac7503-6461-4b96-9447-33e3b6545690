﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Validation;
using Bogus;
using Katana.Core.Entities;
using Katana.Core.Enums;
using Katana.Services.Careplans.Models;
using Katana.Services.Tests.Common.Interfaces;

namespace Katana.Services.Tests.Common.FakeGenerators
{
    /// <summary>
    /// Careplan Fake generator
    /// </summary>
    public class CareplanFakeGenerator : IFakeGenerator<Careplan, AddCareplanDto, UpdateCareplanDto>, IFakeGenerator
    {
        ///<inheritdoc/>
        public Task<Careplan> Generate(IKrudder krudder)
        {
            var dto =
                new Faker<Careplan>()
                   .RuleFor(c => c.CareplanImportId, x => x.UniqueIndex)
                   .RuleFor(c => c.ClinicId, x => krudder.Set<Clinic>().Select(c => c.Id).FirstOrDefault())
                   .RuleFor(c => c.PatientId, x => krudder.Set<Patient>().Select(p => p.Id).FirstOrDefault())
                   .RuleFor(c => c.Diagnostic1Id, x => krudder.Set<Diagnostic>().Select(d => d.Id).FirstOrDefault())
                   .RuleFor(c => c.SpecialtyId, x => krudder.Set<ReferralSpecialty>().Select(s => s.Id).FirstOrDefault())
                   .RuleFor(c => c.GpId, x => krudder.Set<Practitioner>().Select(p => p.Id).FirstOrDefault())
                   .RuleFor(c => c.Recommendations, x => x.Random.ClampString(x.Lorem.Paragraph(), StringLengths.Max))
                   .RuleFor(c => c.RequestDate, x => x.Date.Future().ToUniversalTime())
                   .RuleFor(c => c.ItpApprovedDate, x => x.Date.Future().ToUniversalTime())
                   .RuleFor(c => c.ResponseDate, x => x.Date.Future().ToUniversalTime())
                   .RuleFor(c => c.BilledDate, x => x.Date.Future().ToUniversalTime())
                   .RuleFor(c => c.CasesNotes, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                   .RuleFor(c => c.ValidatedAt, x => x.Date.Past().ToUniversalTime())
                   .RuleFor(c => c.WorkflowStatus, x => CareplanWorkflowStatus.GpApproved)
                   .RuleFor(c => c.Status, x => Status.Enabled)
                   .RuleFor(c => c.ValidationStatus, x => x.Random.Enum<ValidationStatus>())
                   .RuleFor(c => c.ProcessingType, (f, x) =>
                   {
                       return ProcessingType.CPS;
                   })
                   .RuleFor(c => c.ProcessingLoop, (f, x) =>
                   {
                       return x.ProcessingType.Equals(ProcessingType.ECC)
                           ? f.PickRandom(new ProcessingLoop[] { ProcessingLoop.Hmed, ProcessingLoop.OtherClinic })
                           : ProcessingLoop.Unknown;
                   })
                   .RuleFor(c => c.ConsultationReason, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                   .RuleFor(c => c.Recommendations, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                   .Generate();
            return Task.FromResult(dto);
        }

        ///<inheritdoc/>
        public Task<AddCareplanDto> GenerateAdd(IKrudder krudder)
        {
            var dto =
                new Faker<AddCareplanDto>()
                   .RuleFor(c => c.ClinicId, x => krudder.Set<Clinic>().Select(c => c.Id).FirstOrDefault())
                   .RuleFor(c => c.PatientId, x => krudder.Set<Patient>().Select(p => p.Id).FirstOrDefault())
                   .RuleFor(c => c.Diagnostic1Id, x => krudder.Set<Diagnostic>().Select(d => d.Id).FirstOrDefault())
                   .RuleFor(c => c.SpecialtyId, x => krudder.Set<ReferralSpecialty>().Select(s => s.Id).FirstOrDefault())
                   .RuleFor(c => c.GpId, x => krudder.Set<Practitioner>().Select(p => p.Id).FirstOrDefault())
                   .RuleFor(c => c.Recommendations, x => x.Random.ClampString(x.Lorem.Paragraph(), StringLengths.Max))
                   .RuleFor(c => c.RequestDate, x => x.Date.Future().ToUniversalTime())
                   .RuleFor(c => c.WorkflowStatus, x => CareplanWorkflowStatus.GpApproved)
                   .RuleFor(c => c.ProcessingType, (f, x) =>
                   {
                       return ProcessingType.CPS;
                   })
                   .RuleFor(c => c.ProcessingLoop, (f, x) =>
                   {
                       return x.ProcessingType.Equals(ProcessingType.ECC)
                           ? f.PickRandom(new ProcessingLoop[] { ProcessingLoop.Hmed, ProcessingLoop.OtherClinic })
                           : ProcessingLoop.Unknown;
                   })
                   .RuleFor(c => c.ConsultationReason, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                   .RuleFor(c => c.Recommendations, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                   .Generate();
            return Task.FromResult(dto);
        }

        ///<inheritdoc/>
        public Task<UpdateCareplanDto> GenerateUpdate(IKrudder krudder)
        {
            var dto =
                new Faker<UpdateCareplanDto>()
                   .RuleFor(c => c.ClinicId, x => krudder.Set<Clinic>().Select(c => c.Id).FirstOrDefault())
                   .RuleFor(c => c.PatientId, x => krudder.Set<Patient>().Select(p => p.Id).FirstOrDefault())
                   .RuleFor(c => c.Diagnostic1Id, x => krudder.Set<Diagnostic>().Select(d => d.Id).FirstOrDefault())
                   .RuleFor(c => c.GpId, x => krudder.Set<Practitioner>().Select(p => p.Id).FirstOrDefault())
                   .Generate();
            return Task.FromResult(dto);
        }
    }
}
