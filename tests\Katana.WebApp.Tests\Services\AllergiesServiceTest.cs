﻿using Aoshield.Core.Entities.Models;
using Katana.Core.Entities;
using Katana.Services.Allergies;
using Katana.Services.Allergies.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Allergy CRUD service
    /// </summary>
    public class AllergiesServiceTest : IAllergiesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddAllergyDto> Add(AddAllergyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddAllergyDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<AllergyDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<AllergyDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<AllergyDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new AllergyDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateAllergyDto> Update(UpdateAllergyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateAllergyDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public Task<List<Allergy>> SynchronizeAllergies(EmrAllergyDto emrAllergy, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
