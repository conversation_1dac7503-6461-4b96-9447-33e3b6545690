﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Katana.Services.TranscriberManagers;
using Katana.Services.TranscriberManagers.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// TranscribersManager CRUD service
    /// </summary>
    public class TranscriberManagersServiceTest : ITranscriberManagersService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddTranscribersManagerDto> Add(AddTranscribersManagerDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddTranscribersManagerDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscribersManagerDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<TranscribersManagerDto>([],
                    default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<TranscribersManagerDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new TranscribersManagerDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateTranscribersManagerDto> Update(UpdateTranscribersManagerDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateTranscribersManagerDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<int> AssignToTranscriberGroup(AssignTranscribersManagerGroupDto[] dto) =>
            await Task.Run(() => dto.Length);

        #endregion
    }
}
