import { BaseUserEntityDto } from "../BaseUserEntityDto";
import { ClinicCommonDto } from "../Common/ClinicCommonDto";
import { PharmacyCommonDto } from "../Common/PharmacyCommonDto";
import { PractitionerCommonDto } from "../Common/PractitionerCommonDto";
import { NoteDto } from "../Notes/NoteDto";
import { ProvinceDto } from "../Provinces/ProvinceDto";
import { PatientPoolItemDto } from "./PatientPoolItemDto";

export interface PrepanelingPatientDto extends BaseUserEntityDto {
    patientId: number | null;
    patientPoolItemId: number | null;
    syncRefId: number | null;
    syncDate: string | null;
    age: number | null;
    pronouns: string;
    addressType: string;
    preferredContactPhone: string;
    phn: string;
    otherPhn: string;
    phnProvince: ProvinceDto;
    pharmacy: PharmacyCommonDto;
    diagnoses: string;
    custodianGp: PractitionerCommonDto;
    notesCount: number;
    patientStatusId: number | null;
    allowEmailCommunication: boolean;
    prePaneling: boolean;
    prePanelingStartDate: string;
    clinicId: number | null;
    clinic: ClinicCommonDto;
    hasAiSummary: boolean;
    ineligible: boolean;
    cqRequests: PatientPoolItemDto[];
    notes: NoteDto[];
    lastContactedByHPC: string | null;
}
