export enum CQPatientPoolItemWorkflowStatus {
    InPool = 1,
    Started = 2,
    InReview = 3,
    Completed = 4,
    PendingAIQuestions = 5,
    PendingOCR = 7,
    PendingPool = 8,
    Failed = 9,
    PatientUpdated = 10,
    Cancelled = 11,
}

export const PatientFilterableStatuses: string[] = [
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.Started],
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.InReview],
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.Completed],
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.PendingAIQuestions],
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.PendingOCR],
    CQPatientPoolItemWorkflowStatus[CQPatientPoolItemWorkflowStatus.PendingPool],
];

// The order was changed to display the filter of Request Statuses in order of processing.
export enum CQPatientPoolItemUpcomingWorkflowStatus {
    PendingPool = 8,
    PendingOCR = 7,
    PendingAIQuestions = 5,
    Started = 2,
}
