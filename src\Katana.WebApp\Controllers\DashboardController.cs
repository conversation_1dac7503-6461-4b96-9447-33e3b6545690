﻿using Katana.Core.Entities;
using Katana.Core.Enums;
using Katana.Services.Dashboards;
using Katana.Services.Dashboards.Model;
using Katana.Services.Patients.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Dashboard Services Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DashboardsController : ControllerBase
    {
        private readonly IDashboardService _dashboardService;
        private readonly IKrudder _krudder;

        /// <summary>
        /// Public Constructor
        /// </summary>
        /// <param name="krudder">Krudder</param>
        /// <param name="dashboardService">Dashboard Service</param>
        public DashboardsController(IKrudder krudder, IDashboardService dashboardService)
        {
            _krudder = krudder;
            _dashboardService = dashboardService;
        }

        /// <summary>
        /// Get Careplans Summary Information
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns></returns>
        [HttpGet("Careplans/{userId}")]

        public async Task<CareplanDashboardDto> GetCareplansDashboarsInformation(int userId)
            => await _dashboardService.GetCareplanSummary(userId);
        /// <summary>
        /// Get Hpus Dashboard Information
        /// </summary>
        /// <returns></returns>
        [HttpGet("CareplansByStatus/{processingType}")]
        // [AllowAnonymous]
        public async Task<CareplanByStatusDashboardDto[]> GetCareplansByStatusDashboarsInformation(ProcessingType processingType)
            => await _dashboardService.GetCareplanByStatus(processingType);

        /// <summary>
        /// Get the summarized information of the Faxes
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("ManageFaxes")]
        public async Task<ActionResult<FaxDashboardDto>> GetManageFaxesSummary(CancellationToken cancellation)
        {
            var result = await _dashboardService.GetManageFaxSummary(cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Count referrals records for each active status
        /// </summary>
        /// <returns></returns>
        [HttpGet("Referrals/CountByActiveStatus")]
        public async Task<ActionResult<Dictionary<ReferralWorkflowStatus, int>>> GetCountReferralByActiveStatus()
        {
            var result = await _dashboardService.CountReferralByActiveStatus();
            return Ok(result);
        }

        /// <summary>
        /// Gets a transcriber manager group summary of faxes assigned and processed by each transcriber
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("TranscriberManagers/FaxSummaryByGroup")]
        public async Task<ActionResult<TranscriberFaxSummaryDashboardDto[]>> GetTranscriberManagerSummary(CancellationToken cancellation) =>
            await _dashboardService.GetTranscriberManagerSummary(cancellation);


        /// <summary>
        /// Get the summarized information of the faxes by group from transcriber manager
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("TranscriberManagers/AssignedFaxesByGroup")]
        public async Task<ActionResult<AssignedFaxCountDto>> GetManageFaxesSummaryByGroup(CancellationToken cancellation)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            var result = await _dashboardService.GetManageFaxSummaryByGroup(authorId, cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Get the summarized information of Current Billing
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("BillingSummary")]
        public async Task<ActionResult<BillingSummaryDashboardDto>> GetBillingSummary(CancellationToken cancellation)
        {
            var result = await _dashboardService.GetBillingSummary(cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Get the summarized information of the request quotas
        /// </summary>
        /// <param name="practitionerId">Practitioner Id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("QuotasSummaryRequesting/{practitionerId}")]
        public async Task<ActionResult<QuotasCardDto>> GetQuotasSummaryRequesting(int practitionerId, CancellationToken cancellation)
        {
            var result = await _dashboardService.GetQuotasSummaryRequesting(practitionerId, cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Get the summarized information of the approved quotas
        /// </summary>
        /// <param name="practitionerId">Practitioner Id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("QuotasSummaryApproving/{practitionerId}")]
        public async Task<ActionResult<QuotasCardDto>> GetQuotasSummaryApproving(int practitionerId, CancellationToken cancellation)
        {
            var result = await _dashboardService.GetQuotasSummaryApproving(practitionerId, cancellation);
            return Ok(result);
        }

        /// <summary>
        /// Get Patient Paneling Metrics
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpGet("GetPatientPanelingMetrics")]
        public async Task<ActionResult<PatientPanelingMetricsDto>> GetPatientPanelingMetrics(CancellationToken cancellation) =>
            Ok(await _dashboardService.GetPatientPanelingMetrics(cancellation));

        /// <summary>
        /// Get Completed Patients By HPC in the last 7 days
        /// </summary>
        /// <returns></returns>
        [HttpGet("CompletedPatientsByHpcMvcaLast7Days")]
        public async Task<ActionResult<CompletedPatientsByHpcMvcaDashboardDto[]>> GetCompletedPatientsByHpcInLastXDays() =>
            Ok(await _dashboardService.GetTopCompletedPatientsByEachHpc());

        /// <summary>
        /// Get the HPC metrics.
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>The HPC metrics</returns>
        [HttpGet("HpcMvcaMetrics")]
        public async Task<HpcMvcaMetricsDto> GetHpcMvcaMetrics(CancellationToken cancellation) =>
            await _dashboardService.CalculatedMetricsForHpcMvca(cancellation);

        /// <summary>
        /// Get the summarized information for Hpc Lead.
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>An array of SummaryForHpcLeadDashboardDto</returns>
        [HttpGet("SummaryForHPCLead")]
        public async Task<QuestionsCompletedByHpcDto[]> GetSummaryForHpcLead(CancellationToken cancellation) =>
            await _dashboardService.GetClinicalQuestionsForEachHpc(cancellation);

        /// <summary>
        /// Get the summarized information for GP ITP Lead.
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>An array of SummaryForGpItpLeadDashboardDto</returns>
        [HttpGet("GpClinicQuestionsMetrics")]
        public async Task<GpClinicalQuestionDto> GpClinicQuestionsMetrics(CancellationToken cancellation) =>
            await _dashboardService.GetGpClinicQuestionsMetrics(cancellation);

        /// <summary>
        /// Get top 10 of SPs with more re-asked questions
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>An array of UnansweredQuestionsPerSPDto</returns>
        [HttpGet("GetSummaryForUnansweredQuestionsBySp")]
        public async Task<UnansweredQuestionsPerSPDto[]> GetSummaryForUnansweredQuestionsBySp(CancellationToken cancellation) =>
            await _dashboardService.GetUnansweredQuestionsForEachSp(cancellation);

        /// <summary>
        /// Get top 10 of diagnostics with more re-asked questions
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>An array of UnansweredQuestionsPerDiagnosticDto</returns>
        [HttpGet("GetSummaryForUnansweredQuestionsByDiagnostic")]
        public async Task<UnansweredQuestionsPerDiagnosticDto[]> GetSummaryForUnansweredQuestionsByDiagnostic(CancellationToken cancellation) =>
            await _dashboardService.GetUnansweredQuestionsForEachDiagnostic(cancellation);

        /// <summary>
        /// Get the Patient Pool Occupancy.
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>An array of PatientPoolOccupancyDto</returns>
        [HttpGet("PatientPoolOccupancy")]
        public async Task<PatientPoolOccupancyDto[]> GetPatientPoolOccupancy(CancellationToken cancellation) =>
                   await _dashboardService.GetPatientPoolOccupancy(cancellation);
    }
}
