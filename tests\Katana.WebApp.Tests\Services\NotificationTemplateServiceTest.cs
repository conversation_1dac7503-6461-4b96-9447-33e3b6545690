using Aoshield.Core.Entities.Models;
using Aoshield.Core.Validation;
using Aoshield.Services.Notification.Core.Entities;
using Aoshield.Services.Notification.Core.Interfaces;
using Aoshield.Services.Notification.Core.Models;
using FluentValidation;

namespace Katana.WebApp.Tests.Services;

/// <summary>
/// Fake notification template service
/// </summary>
public class NotificationTemplateServiceTest : INotificationTemplateService, IValidationStatusLogsService<NotificationTemplate>
{
    /// <inheritdoc />
    IValidator<NotificationTemplate> IValidationStatusLogsService<NotificationTemplate>.Validator => null;


    /// <inheritdoc />
    public async Task<AddNotificationTemplateDto> Add(AddNotificationTemplateDto dto, CancellationToken cancellation = default) =>
        await Task.Run(() => new AddNotificationTemplateDto(), cancellation);

    /// <inheritdoc />
    public Task<NotificationTemplateDto>
        GetById(int id, CancellationToken cancellation = default) =>
        Task.FromResult(id <= 0 ? null : new NotificationTemplateDto { Id = 1 });

    /// <inheritdoc />
    public async Task<IPagedResults<NotificationTemplateListDto>> GetAsPagedResults(SieveModel query,
        CancellationToken cancellation = default) =>
        await Task.Run(() => new PagedResults<NotificationTemplateListDto>(
            [], default,
            default, default, default, default, default, default, default));

    /// <inheritdoc />
    public Task<UpdateNotificationTemplateDto> Update(UpdateNotificationTemplateDto dto, CancellationToken cancellation = default) =>
        Task.FromResult(new UpdateNotificationTemplateDto { Id = 1 });

    /// <inheritdoc />
    public async Task<int?> Delete(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    /// <inheritdoc />
    public async Task<int?> Restore(int id, bool notify = true,
        CancellationToken cancellation = default) =>
        await Task.Run(() => id);

    ///<inheritdoc/>
    public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default)
        => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(confirmationNoteDtos.Select(x => x.Id).ToArray());

    /// <inheritdoc />
    public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
        CancellationToken cancellation = default) =>
        Task.FromResult(confirmationNoteDtos.Select(x => x.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    ///<inheritdoc/>
    public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
        await Task.Run(() => dtos.Select(d => d.Id).ToArray());

    /// <inheritdoc />
    public IPagedResults<NotificationProfileDto> GetNotificationProfiles(SieveModel query) =>
        new PagedResults<NotificationProfileDto>([], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public Task<NotificationTemplateDto> GetByIdAggregated(int id,
        CancellationToken cancellation = default) =>
        Task.FromResult(id <= 0 ? null : new NotificationTemplateDto { Id = 1 });

    /// <inheritdoc />
    public IPagedResults<NotificationActionDto> GetNotificationProfileActions() =>
        new PagedResults<NotificationActionDto>(
            [], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public IPagedResults<NotificationTypeDto> GetNotificationProfileTypes(SieveModel query) =>
        new PagedResults<NotificationTypeDto>(
            [], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public IPagedResults<NotificationRecipientDto>
        GetNotificationProfileRecipients(SieveModel query) =>
        new PagedResults<NotificationRecipientDto>([], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public IPagedResults<TemplateTagDto>
        GetProfileTagsByEntityTypeAsPagedResults(SieveModel query) =>
        new PagedResults<TemplateTagDto>([], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public IPagedResults<TemplateAttachmentTagDto> GetProfileAttachmentsTagsByEntityTypeAsPagedResults(SieveModel query) =>
        new PagedResults<TemplateAttachmentTagDto>([], default,
            default, default, default, default, default, default, default);

    /// <inheritdoc />
    public IQueryable<NotificationTemplate> ConfigureSet(IQueryable<NotificationTemplate> set, Type triggeringEntity, ValidationTriggerAction action, IEnumerable<int> triggeringEntityIds, string rulesSet, bool fromCrud) =>
        set;

    /// <inheritdoc />
    public Task SetValidationContextData(NotificationTemplate entityToValidate, IEnumerable<NotificationTemplate> entitiesToValidate, string rulesSet, IDictionary<string, object> contextData, CancellationToken cancellation) =>
        Task.CompletedTask;

    /// <inheritdoc />
    public Task Handle(SBValidationInquiryMessage notification, CancellationToken cancellationToken) => Task.CompletedTask;
}
