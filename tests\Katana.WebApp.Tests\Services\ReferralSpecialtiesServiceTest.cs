﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Katana.Services.PractitionerSpecialties.Model;
using Katana.Services.ReferralSpecialties;
using Katana.Services.ReferralSpecialties.Models;
using Katana.Services.SpecialtyDiagnostics.Model;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// ReferralSpecialty CRUD service
    /// </summary>
    public class ReferralSpecialtiesServiceTest : IReferralSpecialtiesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddReferralSpecialtyDto> Add(AddReferralSpecialtyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddReferralSpecialtyDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<ReferralSpecialtyDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<ReferralSpecialtyDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<ReferralSpecialtyDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new ReferralSpecialtyDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateReferralSpecialtyDto> Update(UpdateReferralSpecialtyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateReferralSpecialtyDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        /// <inheritdoc />
        public async Task<IPagedResults<PractitionerSpecialtyDto>> GetSpecialtiesByPractitionerId(
            int id,
            SieveModel query) => await Task.Run(() =>
            new PagedResults<PractitionerSpecialtyDto>(
                [], default, default, default, default,
                default, default, default, default));

        #endregion

        #region Custom

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        #endregion

        /// <inheritdoc />
        public Task<AddSpecialtyDiagnosticDto[]> AddDiagnosticsToSpecialty(AddSpecialtyDiagnosticDto[] specialtyDiagnosticDtos) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DeleteDiagnosticsFromSpecialty(ConfirmationNoteDto[] dtoArr) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DisableDiagnosticsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableDiagnosticsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();
    }
}
