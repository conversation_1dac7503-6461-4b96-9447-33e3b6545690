using Aoshield.Core.DataAccess;
using Aoshield.Services.Notification.Core.Entities;
using Aoshield.Services.Notification.Core.Models;
using Katana.Services.Tests.Common.Interfaces;

namespace Katana.Services.Tests.Common.FakeGenerators;

/// <summary>
/// Patient Fake Generator
/// </summary>
public class NotificationTemplateFakeGenerator : IFakeGenerator<NotificationTemplate, AddNotificationTemplateDto, UpdateNotificationTemplateDto>,
    IFakeGenerator
{
    ///<inheritdoc/>
    public Task<NotificationTemplate> Generate(IKrudder _)
    {
        var dto = new NotificationTemplate()
        {
            //Action = TreatmentplansActions.Approve.ToString(),
            //Name = nameof(Treatmentplan),
            //Template = nameof(Treatmentplan),
            //EntityType = nameof(Treatmentplan),
            //Recipients =
            //[
            //    new NotificationTemplateRecipient()
            //    {
            //        NotificationType = NotificationType.Email,
            //        Type = NotificationRecipientType.Property, Value = "Patient.User.Email"
            //    }
            //]
        };
        return Task.FromResult(dto);
    }

    ///<inheritdoc/>
    public Task<AddNotificationTemplateDto> GenerateAdd(IKrudder _)
    {
        var dto = new AddNotificationTemplateDto()
        {
            //Action = TreatmentplansActions.Approve.ToString(),
            //Name = nameof(Treatmentplan),
            //Template = nameof(Treatmentplan),
            //EntityTypeName = typeof(Treatmentplan).AssemblyQualifiedName,
            //Recipients =
            //[
            //    new()
            //    {
            //        NotificationType = NotificationType.Email,
            //        Type = NotificationRecipientType.Property,
            //        Value = "Patient.User.Email"
            //    }
            //]
        };
        return Task.FromResult(dto);
    }

    ///<inheritdoc/>
    public Task<UpdateNotificationTemplateDto> GenerateUpdate(IKrudder _)
    {
        var dto = new UpdateNotificationTemplateDto()
        {
            //Action = TreatmentplansActions.Approve.ToString(),
            //Name = nameof(Treatmentplan),
            //Template = nameof(Treatmentplan),
            //EntityTypeName = typeof(Treatmentplan).AssemblyQualifiedName,
            //Recipients =
            //[
            //    new NotificationTemplateRecipientDto()
            //    {
            //        NotificationType = NotificationType.Email,
            //        Type = NotificationRecipientType.Property, Value = "Patient.User.Email"
            //    }
            //]
        };
        return Task.FromResult(dto);
    }
}
