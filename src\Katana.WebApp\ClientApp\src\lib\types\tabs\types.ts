import { ProtectedResource } from "Components/Common/Hooks/ResourceProtected/types";

export interface TabType {
    id: string;
    label: string;
    component: JSX.Element;
    disabled?: boolean;
    hidden?: boolean;
    roleBasedProtected?: ProtectedResource[];
    excludedRoles?: ProtectedResource[];
}

export enum HPUTabType {
    All = "all",
    Attention = "attention",
    Assigned = "assigned",
    Mine = "mine",
}

export enum CarePlanTabType {
    All = "all",
    Attention = "attention",
    AttentionMine = "attentionMine",
    CPS = "cps",
    Assigned = "assigned",
    Mine = "mine",
    SpResponse = "spResponse",
    Unanswered = "Unanswered",
    Available = "Available",
    Upcoming = "Upcoming",
    EntityChangeLog = "EntityChangeLog",
}

export enum PractitionerTabType {
    DETAILS = "details",
    SPECIALTIES = "specialties",
    DIAGNOSTICS = "diagnostics",
    ALIASES = "aliases",
    ASSIGNED_GP = "assignedGp",
}

export enum InvoiceItemTabType {
    All = "all",
    Mine = "mine",
    Attachments = "Attachments",
}

export enum PatientTabType {
    All = "all",
    Mine = "mine",
    PatientPaneling = "patientPaneling",
    Lock = "lock",
    Available = "available",
    Completed = "completed",
    AllCompleted = "allCompleted",
    Ineligible = "ineligible",
    AssignedPractitionerHpcPatients = "assignedPractitionerHpcPatients",
    AssignedPractitionerVcaPatients = "assignedPractitionerVcaPatients",
    PrePaneling = "prePanelingPatients",
    UpcomingGPPatients = "upComingGPPatients",
    UpcomingHPCPatients = "UpcomingHPCPatients",
    UpcomingMVCAPatients = "UpcomingMVCAPatients",
    UpcomingHPCLeadPatients = "UpcomingHPCLeadPatients",
    AllVcaPatients = "AllVcaPatients",
    EntityChangeLog = "EntityChangeLog",
}
export enum SpecialtyTabType {
    DIAGNOSTICS = "diagnostics",
    PRACTITIONERS = "practitioners",
}

export enum DeferredRequestTabType {
    All = "all",
    Mine = "mine",
    Crons = "crons",
}

export enum ExportsTabType {
    All = "all",
    Mine = "mine",
}

export enum PatientPoolsTabType {
    All = "all",
    Mine = "mine",
}

export enum GroupTabType {
    MEMBERS = "members",
    ROLES = "roles",
}

export enum FaxTabType {
    Mine = "mine",
    Outgoing = "outgoing",
    Incoming = "incoming",
}

export enum QualityReviewHPCTabType {
    Locked = "locked",
    Available = "available",
    UpcomingHPCPatients = "upcomingHPCPatients",
    AllUpcoming = "allUpcoming",
    Mine = "mine",
    Unanswered = "unanswered",
}

export enum ClinicalQuestionsTabType {
    ClinicalQuestions = "clinicalQuestions",
    AllUpcoming = "allUpcoming",
}

export enum QualityReviewMVCATabType {
    Locked = "locked",
    Available = "available",
    UpcomingMVCAatients = "upcomingMVCAPatients",
    Unanswered = "unanswered",
    Mine = "mine",
}
