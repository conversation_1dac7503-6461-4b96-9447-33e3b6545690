import { AllergiesPage } from "Components/Allergies/AllergiesPage";
import { BillingSettingsPage } from "Components/BillingSettings/BillingSettingsPage";
import { BulkValidationsPage } from "Components/BulkValidations/BulkValidationsPage";
import { CareplanResponseTemplatesPage } from "Components/CareplanResponseTemplates/CareplanResponseTemplatesPage";
import { CareplanDetailsPage } from "Components/Careplans/CareplanDetailsPage";
import { CareplansDashboard } from "Components/Careplans/CareplansDashboard/CareplansDashboard";
import { ClinicsPage } from "Components/Clinics/ClinicsPage";
import { AuthorizedUsersPage } from "Components/Common/AuthorizedUsers/AuthorizedUsersPage";
import { ACCOUNT_ACCESS_PAGE_PATH } from "Components/Common/AuthorizedUsers/constants";
import { CountriesPage } from "Components/Countries/CountriesPage";
import { DeadLetterMessagePage } from "Components/DeadLetterMessages/DeadLetterMessagePage";
import { DeferredUserInvitesPage } from "Components/DeferredUserInvite/DeferredUserInvitesPage";
import { DiagnosticAliasesPage } from "Components/DiagnosticAliases/DiagnosticAliasesPage";
import { DiagnosticsPage } from "Components/Diagnostics/DiagnosticsPage";
import { EccDailyProcessedMetricsPage } from "Components/EccDailyProcessedMetrics/EccDailyProcessedMetricsPage";
import { EducationalCareplanDetailsPage } from "Components/EducationalCareplans/EducationalCareplanDetailsPage";
import { EducationalCareplansPage } from "Components/EducationalCareplans/EducationalCareplansPage";
import { ExportsDashboard } from "Components/Exports/ExportsDashboard/ExportsDashboard";
import { FaxesAutoAssignmentPlanPage } from "Components/Faxes/FaxAutoAssignmentPlanPage";
import { FaxDetailsPage } from "Components/Faxes/FaxDetailsPage";
import { FaxesDashboard } from "Components/Faxes/FaxesDashboard/FaxesDashboard";
import { KatanaGroupsPage } from "Components/Groups/GroupsPage";
import { UserScopedGroupUserRelationPage } from "Components/GroupUser/UserScopedGroupMemebersPage";
import { HomePage } from "Components/HomePage";
import { ImportEventDetailsPage } from "Components/Import/ImportEventDetailsPage";
import { ImportEventsPage } from "Components/Import/ImportEventsPage";
import { InquiryFormQuestionsPage } from "Components/InquiryFormQuestions/InquiryFormQuestionsPage";
import { InquiryFormDetailsPage } from "Components/InquiryForms/InquiryFormDetailsPage";
import { InquiryFormsPage } from "Components/InquiryForms/InquiryFormsPage";
import { InquiryStepDetailsPage } from "Components/InquiryFormSteps/InquiryStepDetailsPage";
import { InquiryStepsPage } from "Components/InquiryFormSteps/InquiryStepsPage";
import { InvoicesPage } from "Components/Invoices/InvoicesPage";
import { NotificationTemplatesPage } from "Components/NotificationTemplates/NotificationTemplatesPage";
import { PatientDetailsPage } from "Components/Patients/PatientDetailsPage";
import { PractitionerAliasesPage } from "Components/Practitioners/PractitionerAliases/PractitionerAliasesPage";
import { PractitionerDetailsPage } from "Components/Practitioners/PractitionerDetailsPage/PractitionerDetailsPage";
import { PractitionersPage } from "Components/Practitioners/PractitionersPage";
import { ProvincesPage } from "Components/Provinces/ProvincesPage";
import { ReferralDetailsPage } from "Components/Referrals/ReferralDetailsPage";
import { ReferralsPage } from "Components/Referrals/ReferralsPage";
import { ReferralSpecialistsPage } from "Components/ReferralSpecialists/ReferralSpecialistsPage";
import { SpecialtiesPage } from "Components/ReferralSpecialties/ReferralSpecialtiesPage";
import { ReportsPage } from "Components/Reports/ReportsPage";
import { SocialHistoriesPage } from "Components/SocialHistories/SocialHistoriesPage";
import { TranscriberGroupsPage } from "Components/TranscriberGroups/TranscriberGroupsPage";
import { TranscriberGroupConfigPage } from "Components/TranscriberGroups/TranscribersGroupConfigPage";
import { TranscriberManagersPage } from "Components/TranscriberManagers/TranscriberManagersPage";
import { TranscribersPage } from "Components/Transcribers/TranscribersPage";
import { TreatmentplanDetailsPage } from "Components/Treatmentplans/TreatmentplanDetailsPage";
import { TreatmentplansDashboard } from "Components/Treatmentplans/TreatmentplansDashboard/TreatmentplansDashboard";
import { UnitsOfMeasurementPage } from "Components/UnitsOfMeasurement/UnitsOfMeasurementPage";
import { UsersPage } from "Components/Users/<USER>";
import { VitalSignsPage } from "Components/VitalSigns/VitalSignsPage";
import { AuthPermission, AuthResource } from "lib/auth/types";
import { flattenSitemap } from "lib/modules";
import { pagesId, UrlData, UrlMap } from "lib/modules/types";
import { AllInvoiceItemsDashboard } from "Components/Invoices/AllInvoiceItemsDashboard/AllInvoiceItemsDashboard";
import { InvoiceItemsDashboard } from "Components/Invoices/InvoiceItemsDashboard/InvoiceItemsDashboard";
import { DeferredMergesPage } from "Components/DeferredMerges/DeferredMergesPage";
import { BulkSearchIndexBuildsPage } from "Components/BulkBuildSearchIndex/BulkSearchIndexBuildsPage";
import AccuroPage from "Components/Accuro/AccuroPage";
import { PatientsDashboard } from "Components/Patients/PatientsDashboard/PatientsDashboard";
import { GroupLabTestsPage } from "Components/GroupLabTests/GroupLabTestsPage";
import { GroupItemsPage } from "Components/GroupItems/GroupItemsPage";
import { LabTestsPage } from "Components/LabTests/LabTestsPage";
import { PatientHistoryTypesPage } from "Components/PatientHistoryTypes/PatientHistoryTypesPage";
import { GroupFoldersPage } from "Components/GroupFolders/GroupFoldersPage";
import { FoldersPage } from "Components/Folders/FoldersPage";
import { PatientStatusesPage } from "Components/PatientStatuses/PatientStatusesPage";
import { SpecialtyDetailsPage } from "Components/ReferralSpecialties/SpecialtyDetailsPage/SpecialtyDetailsPage";
import { PatientsPanelingDashboard } from "Components/PatientPaneling/PanelingDashboard/PatientsPanelingDashboard";
import { FlagsPage } from "Components/Flags/FlagsPage";
import { DeferredRequestsDashboard } from "Components/DeferredRequests/DeferredRequestsDashboard/DeferredRequestsDashboard";
import { GoalItemsPage } from "Components/GoalItems/GoalItemsPage";
import { PatientPoolsDashboard } from "Components/PatientPools/PatientPoolsDashboard/PatientPoolsDashboard";
import { RemindersPage } from "Components/Reminder/RemindersPage";
import { KatanaRolesPage } from "Components/Roles/RolesPage";
import { GroupDetailsPage } from "Components/Groups/GroupDetailsPage";
import { ReportsManagementPage } from "Components/Reports/ReportsManagementPage";
import { PatientEMRSyncRecordTable } from "../../../Components/PatientEMRSyncRecord/PatientEMRSyncRecordPage";
import { DiagnosticDetailsPage } from "Components/Diagnostics/DiagnosticDetailsPage";
import { ClinicDetailsPage } from "Components/Clinics/ClinicDetailsPage";
import { DeclineReasonsPage } from "Components/DeclineReasons/DeclineReasonsPage";
import { PatientDetailsSummaryPage } from "Components/Patients/PatientDetailsSummaryPage";
import { AiPromptDefinitionsPage } from "Components/AiPromptDefinition/AiPromptDefinitionPage";
import { HPCPractitionersPage } from "Components/HPCs/HPCPractitionersPage";
import { RequestDetailsPage } from "Components/Patients/RequestDetailsPage";
import { EConsultsGPDashboard } from "Components/Careplans/EConsultsGP/EConsultsGPDashboard";
import { EConsultsSPDashboard } from "Components/Careplans/EConsultsSP/EConsultsSPDashboard";
import { EConsultsITPDashboard } from "Components/Careplans/EConsultsITP/EConsultsITPDashboard";
import { EConsultsOperationDashboard } from "Components/Careplans/EConsultsOperation/EConsultsOperationDashboard";
import { QualityReviewDashboardHPC } from "Components/Careplans/EConsultsQualityReviewHPCDashboard/QualityReviewHPCDashboard";
import { QualityReviewDashboardMVCA } from "Components/Careplans/EConsultsQualityReviewMVCADashboard/QualityReviewMVCADashboard";
import { ClinicalQuestionsDashboard } from "Components/Careplans/ClinicalQuestionsDashboard/ClinicalQuestionsDashboard";
import { EntityChangeLogPage } from "Components/EntityChangeLogs/EntityChangeLogPage";

/**
 * Describe the app pages sitemap
 *
 * @returns UrlData array
 */
export const sitemap: UrlData[] = [
    {
        id: pagesId.HOME,
        href: "",
        component: <HomePage />,
    },
    {
        id: pagesId.API,
        href: "api",
        component: <AccuroPage />,
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Accuro,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        childRoutes: [
            {
                id: pagesId.ACCURO,
                href: "accuro",
                component: <AccuroPage />,
            },
            {
                id: pagesId.ACCURO_CALLBACK,
                href: "accuro/Callback",
                component: <AccuroPage />,
            },
        ],
    },
    {
        id: pagesId.FAXES,
        href: "faxes",
        component: <FaxesDashboard />,
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Faxes,
                    role: [
                        AuthPermission.Manage,
                        AuthPermission.AssignTranscribersGroup,
                        AuthPermission.AssignTranscriber,
                        AuthPermission.Process,
                    ],
                },
            ],
        },
        childRoutes: [
            {
                id: pagesId.FAXES_DETAILS,
                href: ":id",
                component: <FaxDetailsPage />,
            },
            {
                id: pagesId.FAXES_ASSIGNMENT_PLAN,
                href: "assignment-plan",
                component: <FaxesAutoAssignmentPlanPage />,
            },
        ],
    },
    {
        id: pagesId.TRANSCRIBER,
        href: "transcribers",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.Transcribers, role: [AuthPermission.Manage] },
            ],
        },
        component: <TranscribersPage />,
    },
    {
        id: pagesId.TRANSCRIBER_MANAGERS,
        href: "transcriber-managers",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.TranscriberManagers,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <TranscriberManagersPage />,
    },
    {
        id: pagesId.TRANSCRIBER_GROUPS,
        href: "transcriber-groups",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.TranscriberManagers,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <TranscriberGroupsPage />,
        childRoutes: [
            {
                id: pagesId.TRANSCRIBER_GROUPS_CONFIG,
                href: "config/:id",
                component: <TranscriberGroupConfigPage />,
            },
        ],
    },
    {
        id: pagesId.HPUS,
        href: "hpus",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Treatmentplans,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Faxes,
                    role: [AuthPermission.Process],
                },
                {
                    resource: AuthResource.Treatmentplans,
                    role: [AuthPermission.Process],
                },
            ],
        },
        component: <TreatmentplansDashboard />,
        childRoutes: [
            {
                id: pagesId.HPU_DETAILS,
                href: ":id",
                component: <TreatmentplanDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.CARE_PLANS,
        href: "econsults",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage, AuthPermission.Process],
                },
                {
                    resource: AuthResource.CPS_Careplans,
                    role: [AuthPermission.Manage, AuthPermission.Process],
                },
                {
                    resource: AuthResource.ClinicalQuestion,
                    role: [AuthPermission.ReAsk],
                },
            ],
            name: "_pages:careplan.title",
        },
        component: <CareplansDashboard />,
        childRoutes: [
            {
                id: pagesId.CARE_PLANS_DETAILS,
                href: ":id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.HPU_ASSIGNED,
                href: "assigned/:id",
                component: <CareplanDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ECONSULTS_OPERATION,
        href: "econsults-operation",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultsOperationDashboard,
                    role: [AuthPermission.Read],
                },
            ],
            name: "_pages:careplan.title",
        },
        component: <EConsultsOperationDashboard />,
        childRoutes: [
            {
                id: pagesId.ECONSULTS_OPERATION_DETAILS,
                href: ":id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.HPU_OPERATION_ASSIGNED,
                href: "assigned/:id",
                component: <CareplanDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ECONSULTS_GP,
        href: "econsults-gp",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultsGPDashboard,
                    role: [AuthPermission.Read],
                },
            ],
            name: "_pages:careplan.title",
        },
        component: <EConsultsGPDashboard />,
        childRoutes: [
            {
                id: pagesId.ECONSULTS_GP_DETAILS,
                href: ":id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.ECONSULTS_GP_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ECONSULTS_SP,
        href: "econsults-sp",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultsSPDashboard,
                    role: [AuthPermission.Read],
                },
            ],
            name: "_pages:careplan.title",
        },
        component: <EConsultsSPDashboard />,
        childRoutes: [
            {
                id: pagesId.ECONSULTS_SP_DETAILS,
                href: ":id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.ECONSULTS_SP_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ECONSULTS_ITP,
        href: "econsults-itp",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultsITPDashboard,
                    role: [AuthPermission.Read],
                },
            ],
            name: "_pages:careplan.title",
        },
        component: <EConsultsITPDashboard />,
        childRoutes: [
            {
                id: pagesId.ECONSULTS_ITP_DETAILS,
                href: ":id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.ECONSULTS_ITP_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.EDUCATIONAL_CARE_PLANS,
        href: "educational-care-plans",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.CPS_Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <EducationalCareplansPage />,
        childRoutes: [
            {
                id: pagesId.EDUCATIONAL_CARE_PLANS_DETAILS,
                href: ":id",
                component: <EducationalCareplanDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.CARE_PLAN_RESPONSE_TEMPLATE,
        href: "econsult-response-templates",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.ResponseTemplate,
                    role: [AuthPermission.Write],
                },
            ],
            name: "_pages:careplanResponseTemplate.title",
        },
        component: <CareplanResponseTemplatesPage />,
    },
    {
        id: pagesId.DIAGNOSES,
        href: "diagnoses",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <DiagnosticsPage />,
        childRoutes: [
            {
                id: pagesId.DIAGNOSES_DETAILS,
                href: ":id",
                component: <DiagnosticDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.DIAGNOSES_ALIASES,
        href: "diagnoses-aliases",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <DiagnosticAliasesPage />,
    },
    {
        id: pagesId.REFERRALS,
        href: "referrals",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ReferralsPage />,
        childRoutes: [
            {
                id: pagesId.REFERRALS_DETAILS,
                href: ":id",
                component: <ReferralDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.REFERRALS_SPECIALISTS,
        href: "referral-specialists",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ReferralSpecialistsPage />,
    },
    {
        id: pagesId.PATIENTS,
        href: "patients",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Patients,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.PatientsMine,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.PatientGpMine,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.ClinicalQuestion,
                    role: [AuthPermission.Approve],
                },
            ],
        },
        component: <PatientsDashboard />,
        childRoutes: [
            {
                id: pagesId.PATIENTS_DETAILS,
                href: ":id",
                component: <PatientDetailsPage />,
                pageData: {
                    roleBasedProtected: [
                        {
                            resource: AuthResource.Referrals,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.Careplans,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.Patients,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.PatientsMine,
                            role: [AuthPermission.Read],
                        },
                        {
                            resource: AuthResource.ClinicalQuestion,
                            role: [AuthPermission.Approve],
                        },
                    ],
                },
            },
            {
                id: pagesId.PATIENTS_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
            {
                id: pagesId.PATIENTS_DETAILS_SUMMARY,
                href: ":id/summary",
                component: <PatientDetailsSummaryPage />,
                pageData: {
                    roleBasedProtected: [
                        {
                            resource: AuthResource.Referrals,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.Careplans,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.Patients,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.PatientsMine,
                            role: [AuthPermission.Read],
                        },
                        {
                            resource: AuthResource.ClinicalQuestion,
                            role: [AuthPermission.Approve],
                        },
                    ],
                },
            },
        ],
    },
    {
        id: pagesId.PATIENT_PANELING,
        href: "patient-paneling",
        pageData: {
            name: "_pages:patientPaneling.title",
            roleBasedProtected: [
                {
                    resource: AuthResource.ClinicalQuestion,
                    role: [AuthPermission.Propose],
                },
                {
                    resource: AuthResource.PatientsLocked,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.PatientsAvailable,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.PatientsCompleted,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.PatientsIneligible,
                    role: [AuthPermission.ReadAll],
                },
                {
                    resource: AuthResource.PatientHpcMine,
                    role: [AuthPermission.Read],
                },
                {
                    resource: AuthResource.PatientVcaMine,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <PatientsPanelingDashboard />,
        childRoutes: [
            {
                id: pagesId.PATIENTS_PANELING_REQUEST_DETAILS,
                href: "requestDetail/:id",
                component: <RequestDetailsPage />,
            },
            {
                id: pagesId.PATIENT_PANELING_DETAILS,
                href: "patientDetails/:id",
                component: <PatientDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.PATIENTS_HISTORY_TYPE,
        href: "history-types",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.EMRIntegration,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <PatientHistoryTypesPage />,
    },
    {
        id: pagesId.UNITS_OF_MEASUREMENT,
        href: "units-of-measurement",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <UnitsOfMeasurementPage />,
    },
    {
        id: pagesId.SOCIAL_HISTORIES,
        href: "social-histories",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <SocialHistoriesPage />,
    },
    {
        id: pagesId.VITAL_SIGNS,
        href: "vital-signs",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Referrals,
                    role: [AuthPermission.Manage],
                },
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <VitalSignsPage />,
    },
    {
        id: pagesId.ALLERGIES,
        href: "allergies",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Allergies,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <AllergiesPage />,
    },
    {
        id: pagesId.LAB_TESTS,
        href: "lab-tets",
        pageData: {
            roleBasedProtected: [],
        },
        component: <LabTestsPage />,
    },
    {
        id: pagesId.FOLDERS,
        href: "folders",
        pageData: {
            roleBasedProtected: [],
        },
        component: <FoldersPage />,
    },
    {
        id: pagesId.ECC_DAILY_METRICS,
        href: "ecc-daily-metrics",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Nps,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <EccDailyProcessedMetricsPage />,
    },
    {
        id: pagesId.INVOICES,
        href: "invoices",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Invoice,
                    role: [AuthPermission.Process],
                },
            ],
        },
        component: <InvoicesPage />,
        childRoutes: [
            {
                id: pagesId.INVOICE_ITEMS,
                href: ":id",
                component: <InvoiceItemsDashboard />,
            },
        ],
    },
    {
        id: pagesId.INVOICE_ALL_ITEMS,
        href: "invoice-items",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.InvoiceItem,
                    role: [AuthPermission.Process],
                },
            ],
        },
        component: <AllInvoiceItemsDashboard />,
    },
    {
        id: pagesId.IMPORT_EVENTS,
        href: "imported-data",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Faxes,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ImportEventsPage />,
        childRoutes: [
            {
                id: pagesId.IMPORT_EVENT_DETAILS,
                href: ":id",
                component: <ImportEventDetailsPage />,
            },
            {
                id: pagesId.IMPORT_EVENT_ENTITY_DISPLAY_NAME,
                href: ":entityDisplayName",
                component: <ImportEventsPage />,
            },
        ],
    },
    {
        id: pagesId.REPORTS,
        href: "reporting",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Reports,
                    role: [AuthPermission.ReadAll],
                },
                {
                    resource: AuthResource.ReportsNpmetrics,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <ReportsPage />,
    },
    {
        id: pagesId.USERS,
        href: "users",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Users,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <UsersPage />,
        childRoutes: [
            {
                id: pagesId.USER_MEMBERSHIP,
                href: "membership/:id",
                pageData: {
                    roleBasedProtected: [
                        {
                            resource: AuthResource.Faxes,
                            role: [AuthPermission.Manage],
                        },
                        {
                            resource: AuthResource.Referrals,
                            role: [AuthPermission.Manage],
                        },
                    ],
                },
                component: <UserScopedGroupUserRelationPage />,
            },
        ],
    },
    {
        id: pagesId.GROUPS,
        href: "user-groups",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Groups,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <KatanaGroupsPage />,
        childRoutes: [
            {
                id: pagesId.GROUP_DETAILS,
                href: ":id",
                component: <GroupDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ROLES,
        href: "roles",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Roles,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <KatanaRolesPage />,
    },
    {
        id: pagesId.CLINICS,
        href: "clinics",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Clinics,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ClinicsPage />,
        childRoutes: [
            {
                id: pagesId.CLINICS_DETAILS,
                href: ":id",
                component: <ClinicDetailsPage />,
            },
            {
                id: pagesId.PATIENT_EMR_SYNC_RECORDS,
                href: ":id/Practitioners/:id",
                component: <PatientEMRSyncRecordTable />,
            },
        ],
    },
    {
        id: pagesId.PRACTITIONERS,
        href: "practitioners",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Practitioners,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <PractitionersPage />,
        childRoutes: [
            {
                id: pagesId.PRACTITIONER_DETAILS,
                href: ":id",
                component: <PractitionerDetailsPage />,
            },
            {
                id: pagesId.PRACTITIONER_ALIASES,
                href: ":id/aliases",
                component: <PractitionerAliasesPage />,
            },
        ],
    },
    {
        id: pagesId.SPECIALTIES,
        href: "specialties",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Specialties,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <SpecialtiesPage />,
        childRoutes: [
            {
                id: pagesId.SPECIALTIES_DETAILS,
                href: ":id",
                component: <SpecialtyDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.COUNTRIES,
        href: "countries",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Countries,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <CountriesPage />,
    },
    {
        id: pagesId.PROVINCES,
        href: "provinces",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Provinces,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ProvincesPage />,
    },
    {
        id: pagesId.NOTIFICATIONS_TEMPLATES,
        href: "notification-templates",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Notifications_Templates,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <NotificationTemplatesPage />,
    },
    {
        id: pagesId.EXPORTS,
        href: "exported-data",
        component: <ExportsDashboard />,
    },
    {
        id: pagesId.BULK_VALIDATIONS,
        href: "bulk-validations",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.BulkValidations,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <BulkValidationsPage />,
    },
    {
        id: pagesId.BULK_SEARCH_INDEX_BUILDS,
        href: "bulk-search-index-builds",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.BulkSearchIndexBuilds,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <BulkSearchIndexBuildsPage />,
    },
    {
        id: pagesId.DEFERRED_REQUEST,
        href: "deferred-requests",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.DeferredRequests,
                    role: [AuthPermission.Read, AuthPermission.ReadAll],
                },
            ],
        },
        component: <DeferredRequestsDashboard />,
    },
    {
        id: pagesId.USER_INVITES,
        href: "invite-users",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Careplans,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <DeferredUserInvitesPage />,
    },
    {
        id: pagesId.DEFERRED_All_MERGES,
        href: "all-merges",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Merges,
                    role: [AuthPermission.ReadAll],
                },
            ],
        },
        component: <DeferredMergesPage responsibility="all" />,
    },
    {
        id: pagesId.DEFERRED_MERGES,
        href: "merges",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Merges,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <DeferredMergesPage responsibility="mine" />,
    },
    {
        id: pagesId.AUTHORIZED_USERS,
        href: ACCOUNT_ACCESS_PAGE_PATH,
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Impersonation,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <AuthorizedUsersPage />,
    },
    {
        id: pagesId.INQUIRY_FORMS,
        href: "inquiry-forms",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Workflow,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <InquiryFormsPage />,
        childRoutes: [
            {
                id: pagesId.INQUIRY_FORMS_DETAILS,
                href: ":id",
                component: <InquiryFormDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.INQUIRY_FORM_STEPS,
        href: "inquiry-form-steps",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Workflow,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <InquiryStepsPage />,
        childRoutes: [
            {
                id: pagesId.INQUIRY_FORM_STEPS_DETAILS,
                href: ":id",
                component: <InquiryStepDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.INQUIRY_FORM_QUESTIONS,
        href: "inquiry-form-questions",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Workflow,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <InquiryFormQuestionsPage />,
    },
    {
        id: pagesId.DEAD_LETTER_MESSAGES,
        href: "dead-letter-messages",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.DeadLetterMessages, role: [AuthPermission.Manage] },
            ],
        },
        component: <DeadLetterMessagePage />,
    },
    {
        id: pagesId.GROUP_ITEMS,
        href: "history-type-groups",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
            ],
        },
        component: <GroupItemsPage />,
    },
    {
        id: pagesId.GROUP_LAB_TESTS,
        href: "lab-test-groups",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
            ],
        },
        component: <GroupLabTestsPage />,
    },
    {
        id: pagesId.GROUP_FOLDERS,
        href: "folder-groups",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
            ],
        },
        component: <GroupFoldersPage />,
    },
    {
        id: pagesId.BILLING_SETTINGS,
        href: "billing-settings",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.BillingSettings, role: [AuthPermission.Manage] },
            ],
        },
        component: <BillingSettingsPage />,
    },
    {
        id: pagesId.PATIENT_STATUSES,
        href: "patient-statuses",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
            ],
        },
        component: <PatientStatusesPage />,
    },
    {
        id: pagesId.FLAGS,
        href: "chart-flags",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.EMRIntegration, role: [AuthPermission.Read] },
            ],
        },
        component: <FlagsPage />,
    },
    {
        id: pagesId.CLINICAL_QUESTIONS,
        href: "clinical-questions",
        pageData: {
            roleBasedProtected: [
                { resource: AuthResource.ClinicalQuestion, role: [AuthPermission.ReadAll] },
            ],
        },
        component: <ClinicalQuestionsDashboard />,
    },
    {
        id: pagesId.GOAL_ITEMS,
        href: "goal-items",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.GoalItems,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <GoalItemsPage />,
    },
    {
        id: pagesId.PATIENT_POOLS,
        href: "patient-pool-size",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.PatientPool,
                    role: [AuthPermission.ReadAll, AuthPermission.Read],
                },
            ],
        },
        component: <PatientPoolsDashboard />,
    },
    {
        id: pagesId.REMINDERS,
        href: "reminders",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Reminders,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <RemindersPage />,
    },
    {
        id: pagesId.REPORTS_SETTINGS,
        href: "reports-management",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Reports,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <ReportsManagementPage />,
    },
    {
        id: pagesId.DECLINE_REASON,
        href: "decline-reason",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.DeclineReasons,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <DeclineReasonsPage />,
    },
    {
        id: pagesId.AI_PROMPT_DEFINITIONS,
        href: "ai-prompts",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.AiPrompts,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <AiPromptDefinitionsPage />,
    },
    {
        id: pagesId.HPCS,
        href: "hpcs",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.PractitionersMine,
                    role: [AuthPermission.Read],
                },
            ],
            name: "CH Nurses",
        },
        component: <HPCPractitionersPage />,
        childRoutes: [
            {
                id: pagesId.HPCS_DETAILS,
                href: ":id",
                component: <PractitionerDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.QUALITY_REVIEW_HPC,
        href: "econsults-quality-review-chnurse",
        pageData: {
            name: "_pages:qualityReviewHPC.title",
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultQualityReviewHPCDashboard,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <QualityReviewDashboardHPC />,
        childRoutes: [
            {
                id: pagesId.QUALITY_REVIEW_HPC_PATIENT_DETAILS,
                href: "patient/:id",
                component: <PatientDetailsPage />,
            },
            {
                id: pagesId.QUALITY_REVIEW_HPC_CAREPLAN_DETAILS,
                href: "eConsult/:id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.QUALITY_REVIEW_HPC_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.QUALITY_REVIEW_MVCA,
        href: "econsults-quality-review-mvca",
        pageData: {
            name: "_pages:qualityReviewMVCA.title",
            roleBasedProtected: [
                {
                    resource: AuthResource.EConsultQualityReviewMVCADashboard,
                    role: [AuthPermission.Read],
                },
            ],
        },
        component: <QualityReviewDashboardMVCA />,
        childRoutes: [
            {
                id: pagesId.QUALITY_REVIEW_MVCA_PATIENT_DETAILS,
                href: "patient/:id",
                component: <PatientDetailsPage />,
            },
            {
                id: pagesId.QUALITY_REVIEW_MVCA_CAREPLAN_DETAILS,
                href: "eConsult/:id",
                component: <CareplanDetailsPage />,
            },
            {
                id: pagesId.QUALITY_REVIEW_MVCA_REQUEST_DETAILS,
                href: "requestDetails/:id",
                component: <RequestDetailsPage />,
            },
        ],
    },
    {
        id: pagesId.ENTITY_CHANGE_LOGS,
        href: "change-logs",
        pageData: {
            roleBasedProtected: [
                {
                    resource: AuthResource.Audit,
                    role: [AuthPermission.Manage],
                },
            ],
        },
        component: <EntityChangeLogPage />,
    },
];

export const urlsMap: UrlMap = flattenSitemap(sitemap);
