using Aoshield.Core.DataAccess;
using Aoshield.Core.Exceptions;
using Aoshield.Services.Export.Core.Exceptions;
using Aoshield.Services.Import.Core.Exceptions;
using Aoshield.Services.Messaging.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;

namespace Aoshield.Services.Core.ApiProblemDetails
{
    /// <summary>
    /// ProblemDetails factory.
    /// </summary>
    public static class ProblemDetailsFactory
    {
        public static ProblemDetails FromException<TException>(
        TException exception,
        IEnumerable<object> errors = null) where TException : Exception
        {
            var (code, codeName) = GetErrorCodeFromExceptionType(exception);

            var problem = new ProblemDetails
            {
                Type = exception.GetType().Name,
                Title = exception.GetType().Name,
                Detail = exception.Message
            };

            // Extensiones estándar de ProblemDetails
            problem.Extensions["code"] = code;
            problem.Extensions["codeName"] = codeName;
            problem.Extensions["errors"] = errors ?? GetInnerExceptionsAsErrors(exception);

            return problem;
        }

        /// <summary>
        /// Builds a ProblemDetails instance from an Exception object.
        /// </summary>
        /// <param name="exception">Exception</param>
        /// <param name="code">Code</param>
        /// <param name="codeName">Code Name</param>
        /// <param name="errors">Errors</param>
        /// <returns></returns>
        public static ProblemDetails FromException(
            Exception exception,
            int code,
            string codeName = null,
            IEnumerable<object> errors = null)
        {
            var problem = new ProblemDetails()
            {
                Type = exception.GetType().Name, // in this future, this should point to the Katana online API docs
                Title = exception.GetType().Name,
                Detail = exception.Message,
            };

            // This is the right way of extending the ProblemDetails class.
            // These props will appear in the same namespace of the serialized JSON object.
            //
            // See https://docs.microsoft.com/en-us/dotnet/api/microsoft.aspnetcore.mvc.problemdetails.extensions 
            problem.Extensions["code"] = code;
            problem.Extensions["codeName"] = codeName ?? code.ToString();
            problem.Extensions["errors"] = errors ?? GetInnerExceptionsAsErrors(exception);

            return problem;
        }

        /// <summary>
        /// Builds a ProblemDetails instance from an Exception object.
        /// </summary>
        /// <typeparam name="TException"></typeparam>
        /// <param name="message">Message</param>
        /// <param name="errors">Errors</param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        public static ProblemDetails FromException<TException>(
            string message,
            IEnumerable<object> errors = null) where TException : Exception
        {
            var constructor = typeof(TException).GetConstructor([typeof(string)]) ?? throw new InvalidOperationException($"The exception type '{typeof(TException).Name}' does not have a constructor.");
            var exception = (TException) constructor.Invoke([message]);

            var (code, codeName) = GetErrorCodeFromExceptionType(exception);
            var problem = new ProblemDetails()
            {
                Type = typeof(TException).Name, // in this future, this should point to the Katana online API docs
                Title = typeof(TException).Name,
                Detail = message,
            };

            // This is the right way of extending the ProblemDetails class.
            // These props will appear in the same namespace of the serialized JSON object.
            //
            // See https://docs.microsoft.com/en-us/dotnet/api/microsoft.aspnetcore.mvc.problemdetails.extensions 
            problem.Extensions["code"] = code;
            problem.Extensions["codeName"] = codeName ?? code.ToString();
            problem.Extensions["errors"] = errors ?? new List<ProblemDetailsError>
            {
                new() { Title = typeof(TException).Name, Detail = message }
            };

            return problem;
        }

        /// <summary>
        /// Gets the inner exceptions as a list of ProblemDetailsError objects.
        /// </summary>
        /// <typeparam name="TException"></typeparam>
        /// <param name="exception">Exception</param>
        /// <returns></returns>
        private static List<ProblemDetailsError> GetInnerExceptionsAsErrors<TException>(TException exception)
        where TException : Exception
        {
            var inner = exception.InnerException;
            var errors = new List<ProblemDetailsError>();

            while (inner != null)
            {
                errors.Add(new ProblemDetailsError() { Title = inner.GetType().Name, Detail = inner.Message });
                inner = inner.InnerException;
            }

            return errors;
        }

        /// <summary>
        /// Gets the error code and error code name from the exception type.
        /// </summary>
        /// <param name="exception">Exception</param>
        /// <returns></returns>
        private static (int Code, string CodeName) GetErrorCodeFromExceptionType(Exception exception)
        {
            return exception switch
            {
                ServiceException => ((int) ExceptionErrorCode.ServiceError, nameof(ExceptionErrorCode.ServiceError)),
                RetryRingCentralException => ((int) ExceptionErrorCode.RingCentralError, nameof(ExceptionErrorCode.RingCentralError)),
                DuplicatedImportException => ((int) ExceptionErrorCode.DuplicatedImport, nameof(ExceptionErrorCode.DuplicatedImport)),
                ImportException => ((int) ExceptionErrorCode.ImportError, nameof(ExceptionErrorCode.ImportError)),
                ExportException => ((int) ExceptionErrorCode.ExportError, nameof(ExceptionErrorCode.ExportError)),
                ReadableException => ((int) ExceptionErrorCode.ErrorWithReadableMessage, nameof(ExceptionErrorCode.ErrorWithReadableMessage)),
                CrudServiceValidationException => ((int) ExceptionErrorCode.ValidationError, nameof(ExceptionErrorCode.ValidationError)),
                SieveMethodNotFoundException => ((int) ExceptionErrorCode.SieveError, nameof(ExceptionErrorCode.SieveError)),
                SieveIncompatibleMethodException => ((int) ExceptionErrorCode.SieveError, nameof(ExceptionErrorCode.SieveError)),
                SieveException => ((int) ExceptionErrorCode.SieveError, nameof(ExceptionErrorCode.SieveError)),
                OperationCanceledException => ((int) ExceptionErrorCode.ErrorWithReadableMessage, nameof(ExceptionErrorCode.ErrorWithReadableMessage)),

                // Catch all
                _ => ((int) ExceptionErrorCode.Error, nameof(ExceptionErrorCode.Error))
            };
        }
    }
}
