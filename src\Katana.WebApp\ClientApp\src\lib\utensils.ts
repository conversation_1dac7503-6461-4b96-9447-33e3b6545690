import { IPublicClientApplication } from "@azure/msal-browser";
import { grey } from "@mui/material/colors";
import DOMPurify from "dompurify";
import { acquireToken } from "lib/auth/authUtils";
import { TokenAcquisitionHandler } from "lib/katanaApiClient/types";
import { RealtimeMessage } from "lib/katanaChatHub";
import { ChatType } from "lib/models/ChatType";
import { EntityName } from "lib/models/EntityName";
import { KTableAction } from "lib/muiDataTable/muiDataTableUtils";
import { ReactQueryKeys } from "lib/reactQuery/reactQueryKeys";
import { EnumIdNameTuple } from "lib/types/EnumIdNameTuple";
import { intersectionWith } from "lodash";
import moment, { MomentInput } from "moment";
import { QueryClient, QueryKey } from "@tanstack/react-query";
import { BaseEntityDto } from "./models/BaseEntityDto";
import { PagedResults } from "./models/PagedResults";
import { ValidationAction, ValidationTriggerAction } from "./models/ValidationAction";
import { BaseUpdateDto } from "./models/BaseUpdateDto";

/**
 * UTENSILS
 * ========
 *
 * Utility functions, classes, etc. that don't naturally fit elsewhere but are used all across the app.
 *
 * (please, think twice when adding stuff to this file and explore before if there isn't a better place for whatever you are trying to add)
 */

/**
 * Defines the default length for a small column
 */
export const KATANA_COLUMN_SHORT = 30;

/**
 * Defines the default length for a large column
 */
export const KATANA_COLUMN_MEDIUM = 40;

/**
 * Defines the default length for a large column
 */
export const KATANA_COLUMN_LONG = 50;

/**
 * Defines the default length for a large column
 */
export const KATANA_COLUMN_LONG100 = 100;

/**
 * Defines the default length for a large column
 */
export const KATANA_COLUMN_EXTRA_LONG = 150;

/**
 * Defines the default length for a large column
 */
export const KATANA_TRUNCATE_INBOX_NOTIFICATION = 55;

/**
 * Defines the menu drawer color
 */
export const NAVMENU_DRAWER_COLOR = grey[100]; //blueGrey[50]//

/**
 * Gets a formatted string based on a fetch Response object.
 *
 * @param response fetch() response object
 * @returns Promise<string> with a formatted message based on the passed Response object
 */
export const getResponseMessage = async (response: Response): Promise<string> => {
    return `Status: ${response.status} | Status text: ${
        response.statusText
    } | Response: ${await response.text()}`;
};

/**
 * Gets a formatted string used as for notifications.
 *
 * @param msg raw message
 * @param utc format the message with a UTC date
 * @returns Formatted notification message
 */
export const formatNotificationMessage = (msg: string, utc = true): string => {
    const d = utc ? moment.utc() : moment();
    const utcFlag = utc ? " (UTC)" : "";

    return `${d.format("hh:mm A")}${utcFlag} - ${msg}`;
};

/**
 * Check if an Object is empty or null
 *
 * @param obj Object to check if empty
 * @returns True indicating object is empty otherwise false
 */
export const isObjectEmpty = (obj?: Record<string, any>): boolean => {
    return !obj || !Object.values(obj).some((x) => x !== void 0);
};

/**
 * Get list of string from members (keys) of a given Enum
 *
 * @param enumType Enum Type
 * @returns Enum members as a list of strings
 */
export const getEnumKeys = (enumType: Record<string, string | number>): Array<string> =>
    // https://github.com/microsoft/TypeScript/issues/17198
    // https://www.angularjswiki.com/angular/names-of-enums-typescript/

    Object.keys(enumType).filter((x) => Object.values(enumType).includes(x));

/**
 * Sleep function (used only for testing and simulating delays)
 *
 * The function can be called as:  await sleep(1000)  or  sleep(1000).then(...)
 *
 * @param ms sleep time in milliseconds
 * @returns Empty Promise
 */
export const sleep = (ms: number): Promise<void> => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

/**
 * Returns a function that can be used to acquire tokens using the
 * given MSAL Instance
 *
 * This is a just utility method used in Katana.tsx. The method
 * is defined outside of the Auth and KatanaApiClient modules to avoid
 * coupling those two together - we need the TokenAcquisitionHandler type
 * defined in KatanaApiClient, and we are using the "acquireToken" method defined
 * in the Auth module. This method here kind of glues both modules together
 *
 * @param instance MsalInstance
 * @returns Function
 */
export const getAcquireTokenHandler = (instance: IPublicClientApplication): TokenAcquisitionHandler => {
    return () => acquireToken(instance);
};

/**
 * Short Date display format
 */
export const KATANA_SHORT_DATE_FORMAT = "YYYY-MM-DD";

/**
 * Short Date display format
 */
export const KATANA_EXTRA_SHORT_DATE_FORMAT = "MMM D";

/**
 * Long Date display format
 */
export const KATANA_LONG_DATE_FORMAT = "YYYY-MM-DD HH:mm";

/**
 * Long Date display format
 */
export const KATANA_LONG_EXPORT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

/**
 * Long Date display format
 */
export const KATANA_LONG_POST_DATE_FORMAT = "YYYY-MM-DDTHH:mm:ss";

/**
 * Pretty date format
 */
export const KATANA_PRETTY_DATE_FORMAT = "MMM DD, YYYY";

/**
 * Convert UTC to local time
 */

/**
 *
 * @param value UTC time
 * @param format Output local time format (KATANA_LONG_DATE_FORMAT by default)
 * @returns formatted local time
 */
export const utcToLocal = (value: MomentInput, format?: string): string =>
    value
        ? moment
              .utc(value)
              .local()
              .format(format ?? KATANA_LONG_DATE_FORMAT)
        : KATANA_EMPTY_FIELD;

/**
 * Flattens an object using the following keys naming scheme:
 *
 * Nested object:
 * {
 *   'name': 'John'
 *   'address': {
 *     'zip': 99999
 *   }
 * }
 *
 * Flattened object:
 * {
 *   'name': 'John'
 *   'address.zip': 9999,
 * }
 *
 * @example flattenObject(myObject)
 * @param obj Object to flatten
 * @param separator Nested property name separator (default ".")
 * @returns Flattened JSON object
 */
export const flattenObject = (obj: Record<string, any>, separator = "."): Record<string, any> => {
    // Adding an internal function to avoid exposing the parentKey argument
    // that's only used during recursion to create the nested props
    const _flattenObject = (obj: Record<string, any>, parentKey = ""): Record<string, any> => {
        if (parentKey !== "") parentKey += separator;

        const flattened: Record<string, any> = {};

        Object.keys(obj).forEach((key: string) => {
            if (typeof obj[key] === "object" && obj[key] !== null) {
                Object.assign(flattened, _flattenObject(obj[key], parentKey + key));
            } else {
                flattened[parentKey + key] = obj[key];
            }
        });

        return flattened;
    };

    return _flattenObject(obj);
};

/**
 * Converts an Enum type to a list of EnumListItems[] (i.e. tuples of {id-name})
 *
 * This method is mostly used with Select UI controls.
 *
 * @param enumType Enum type
 * @returns EnumListItems list
 */
export const getEnumIdValueTuple = (
    enumType: Record<string, string | number>
): Array<EnumIdNameTuple> => {
    // If the Enum has values of type number, Object.values() return both the enum
    // keys (as strings) AND values (as numbers). If Enum has type string, it only returns the keys as strings.
    //
    // In order to deal with both cases, we try to intersect the arrays returned by Object.keys() and Object.values()
    // so that we can determine exactly what is a key and what is a value based. If Enum has values of
    // type number, intersecting keys() and values() and keeping only the elements in the values() array
    // will gives us the true values. If Enum has values of type strings, that intersection will return an empty
    // array, so we can confidently keep the values() without any further treatment.

    const keys = Object.keys(enumType).filter((x) => Object.values(enumType).includes(x));

    let values = Object.values(enumType).filter((x) => !Object.keys(enumType).includes(x as any));
    if (!values.length) {
        values = Object.values(enumType);
    }

    return keys.map((k, i) => {
        return { id: values[i], name: k };
    });
};

/**
 * String constant to display Katana Company name
 */
export const KATANA_COMPANY = "Satori Group";

/**
 * String constant to display not applicable
 */
export const KATANA_NA_FIELD = "N/A";

/**
 * String constant to display not External billing
 */
export const KATANA_EXTERNAL_BILLING_FIELD = "External billing";

/**
 * String constant to display not Exclude billing
 */
export const KATANA_INCLUDE_BILLING_FIELD = "False";

/**
 * String constant to display (in some cases) if a field is empty
 */
export const KATANA_EMPTY_FIELD = "";

/**
 * String constant to display in some import item detail cases
 */
export const KATANA_EMPTY_TAG = "<empty>";

/**
 * String constant to display if a Status (or any other assignment-based field) is not assigned.
 *
 * This is required in cases where we want to provide an in-line edit link.
 */
export const KATANA_NOT_ASSIGNED_STATUS_FIELD = "Not Assigned";

/**
 * String constant to display if a date is expired
 */
export const KATANA_EXPIRED = "Expired";

/**
 * String constant to display if a field is invalid
 */
export const KATANA_INVALID_FIELD = "Invalid";

/**
 * String constant to display if a field is invalid
 */
export const KATANA_INFANT_FIELD = "Infant";

/**
 * String constant to display an empty field as a dash
 */
export const KATANA_DASH_FIELD = "-";

/**
 * Implements the "nameof" operator.
 *
 * Usage:
 *
 * interface Person {
 *    firstName: string;
 *   lastName: string;
 * }
 *
 * const personName = nameof<Person>("firstName");    //returns "firstName"
 *
 * Reference: https://schneidenbach.gitbooks.io/typescript-cookbook/content/nameof-operator.html
 *
 * @returns EnumListItems list
 */
export const nameofFactory =
    <T>() =>
    (name: keyof T): keyof T =>
        name;

/** Max Pagination page size */
export const KATANA_MAX_PAGE_SIZE = 300;

/** Server-side Autocomplete results page size */
// Set a value long enough to be useful but also to force scroll. We
// use the onScroll events to fire away more queries if need be
export const KATANA_AUTOCOMPLETE_PAGE_SIZE = 50;

/**
 * KDraftEditor default Controls
 */

export const KATANA_EDITOR_DEFAULT_CONTROLS = [
    "title",
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "undo",
    "redo",
    "numberList",
    "bulletList",
    "quote",
];

/**
 * Service worker registration search interval in milliseconds
 */

export const KATANA_SERVICE_WORKER_SEARCH_INTERVAL = 1000 * 60 * 10; // 20 minutes

/**
 *
 * @param selectedRowsActions The actions of every row selected
 * @returns All available actions depending on the type of action (single or multiple)
 */
export function getAvailableActions(selectedRowsActions?: KTableAction[][]): KTableAction[] {
    if (!selectedRowsActions || !selectedRowsActions.length) {
        return [];
    }
    if (selectedRowsActions.length > 1) {
        const multipleActions = selectedRowsActions.map((a) =>
            a.filter((a: any) => {
                return !a.hidden && !a.singleAction;
            })
        );
        let actions = multipleActions[0];
        let index = 1;
        while (actions.length && index < multipleActions.length) {
            actions = intersectionWith(actions, multipleActions[index++], (x, y) => x.text === y.text);
        }
        return actions;
    } else {
        return selectedRowsActions[0].filter((a: any) => {
            return !a.hidden && !a.hideOnSingle;
        });
    }
}

/**
 * Utility function to avoid duplicating the logic of invalidate query
 * message related to an Action type
 *
 * @param queryClient queryClient object
 * @param queryKey key to invalidate to query for
 */
export const invalidateAndCancelIfFetching = (queryClient: QueryClient, queryKey: QueryKey) => {
    // If currently fetching, cancel immediately and invalidate
    if (queryClient.isFetching(queryKey)) {
        queryClient.cancelQueries(queryKey);
    }
    queryClient.invalidateQueries(queryKey);
};

/**
 * Utility function to avoid duplicating the logic of processing a ChatHub
 * message related to a "ValidationEnd" action
 *
 * @param message chat hub message
 * @param queryClient queryClient object
 * @param queryKey key to invalidate to query for
 * @param entity entity related to the received validation message
 */
export const invalidateQueryWhenValidationEnds = (
    message: RealtimeMessage,
    queryClient: QueryClient,
    queryKey: ReactQueryKeys,
    entity: EntityName
) => {
    if (message.chatType === ChatType.Broadcast) {
        if (message.entity !== EntityName[entity]) {
            return;
        }
        if (message.action === ValidationAction[ValidationAction.ValidationEnd]) {
            // If currently fetching, cancel immediately and invalidate
            invalidateAndCancelIfFetching(queryClient, [queryKey]);
        }
    }
};

/**
 * Utility function to avoid duplicating the logic of processing a ChatHub
 * message related to a "ActionsUpdateEnd" action
 *
 * @param message chat hub message
 * @param queryClient queryClient object
 * @param queryKey key to invalidate to query for
 * @param entity entity related to the received validation message
 */
export const invalidateQueryWhenActionUpdateEnds = (
    message: RealtimeMessage,
    queryClient: QueryClient,
    queryKey: ReactQueryKeys,
    entity: EntityName
) => {
    if (message.chatType === ChatType.Broadcast) {
        if (message.entity !== EntityName[entity]) {
            return;
        }
        if (message.action === ValidationAction[ValidationAction.ActionsUpdateEnd]) {
            // If currently fetching, cancel immediately and invalidate
            invalidateAndCancelIfFetching(queryClient, [queryKey]);
        }
    }
};

/**
 * Utility function to avoid duplicating the logic of processing a ChatHub
 * message related to an Action type
 *
 * @param message chat hub message
 * @param queryClient queryClient object
 * @param queryKey key to invalidate to query for
 * @param entity entity related to the received validation message
 * @param action action related to the received validation message
 */
export const invalidateQueryWhenAction = (
    message: RealtimeMessage,
    queryClient: QueryClient,
    queryKey: ReactQueryKeys,
    entity: EntityName,
    action: ValidationAction
) => {
    if (message.chatType === ChatType.Broadcast) {
        if (message.entity !== EntityName[entity]) {
            return;
        }
        if (message.action === ValidationAction[action]) {
            invalidateAndCancelIfFetching(queryClient, [queryKey]);
        }
    }
};

type ReactQueriesData = [queryKey: QueryKey, data: PagedResults<BaseEntityDto> | undefined][];

/**
 * Utility function to avoid duplicating the logic of processing a ChatHub
 * message related to an Action type
 *
 * @param message chat hub message
 * @param queryClient queryClient object
 * @param entity entity related to the received validation message
 * @param disableInvalidateQueryWithCache disable invalidate query with cache check, if true normal invalidate query behavior was used
 * @param invalidateQueryWithAdd disable invalidate query with add check, if true normal invalidate query behavior was used
 * @param action action related to the received validation message
 * @param alternativeAction alternative action is used for apply a different action to invalidate the query for the rest of entity except the current when disableInvalidateQueryWithCache is false
 */
export const invalidateQueryWhenActionPlus = (
    message: RealtimeMessage,
    queryClient: QueryClient,
    entity: EntityName,
    disableInvalidateQueryWithCache = false,
    invalidateQueryWithAdd = false,
    action: ValidationAction = ValidationAction.ValidationEnd,
    alternativeAction: ValidationAction = ValidationAction.ValidationEnd
) => {
    if (!message.entity || message.chatType !== ChatType.Broadcast) return;
    const reactQuery = [message.entity];

    const isEntityMatch = message.entity === EntityName[entity];
    const isActionMatch = message.action === ValidationAction[action];
    const isAlternativeActionMatch = message.action === ValidationAction[alternativeAction];

    if (
        invalidateQueryWithAdd &&
        message.triggerAction === ValidationTriggerAction.EntityAdded &&
        isEntityMatch &&
        isActionMatch
    ) {
        invalidateAndCancelIfFetching(queryClient, reactQuery);
        return;
    }

    if (disableInvalidateQueryWithCache) {
        if (!isEntityMatch || !isActionMatch) return;
    } else {
        if ((isEntityMatch && !isActionMatch) || (!isEntityMatch && !isAlternativeActionMatch)) {
            return;
        }

        const cachedEntities: ReactQueriesData = queryClient.getQueriesData([message.entity]);
        const activeEntities = cachedEntities
            ?.filter((cache) => cache[1] !== undefined)
            .map((cache) => cache[1]?.items as BaseEntityDto[])
            .reduce((acc, val) => acc.concat(val), []);

        if (activeEntities && !activeEntities.some((x) => message.ids.includes(x.id))) {
            return;
        }
    }
    // Common invalidate query code, If currently fetching, cancel immediately and invalidate
    invalidateAndCancelIfFetching(queryClient, reactQuery);
};

/**
 * Utility format age value
 *
 * @param value entity related to the received validation message
 * @returns formate age value
 */
export const getAgeValue = (value?: number | null): string =>
    value !== null && value !== undefined
        ? value !== 0
            ? value.toString()
            : KATANA_INFANT_FIELD
        : KATANA_INVALID_FIELD;

/**
 * Sanitize Html using dompurify package
 *
 * @param html html
 * @returns Sanitized html
 */
export const sanitizedHtml = (html: string): string => DOMPurify.sanitize(html);

/**
 * String constant using in phone pattern
 */
export const KATANA_PHONE_PATTERN =
    /^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$/;

/**
 * String constant using in email pattern
 */
export const KATANA_EMAIL_PATTERN = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i;

/**
 * String constant containing the regex to match html classes
 */
export const HTML_CLASS_REGEX = /class="[^"]*"/g;

/**
 *  Clean Html inline styles
 * Remove all inline styles and dir="ltr" from html string
 *
 * @param htmlString html string
 * @returns cleaned html string
 */
export const cleanHmlInlineStyles = (htmlString: string) =>
    sanitizedHtml(htmlString.replaceAll(HTML_CLASS_REGEX, "").replaceAll('dir="ltr"', ""));

/**
 * Get the type name properties of an object.
 *
 * @param obj The object to get the type name properties from.
 * @returns An array of string representing the type name properties.
 */
export const getTypeNameProps = <T extends BaseUpdateDto>(obj: T): string[] => {
    return Object.keys(obj);
};

/**
 * Converts bytes to kilobytes
 *
 * @param bytes The number of bytes to convert
 * @param convertTo The unit to convert to
 * @param toFixed The number of decimal places to round to
 * @returns The number of bytes converted to the specified unit
 */
export const bytesConvertTo = (bytes = 0, convertTo = "MB", toFixed = 2) => {
    const units = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const index = units.indexOf(convertTo);
    const value = bytes / Math.pow(1000, index);
    return value.toFixed(toFixed);
};

/**
 * Convert Html text to plain text
 *
 * @param text html text
 * @returns plain text
 */
export const htmlToPlain = (text: string) => text.replace(/<.*?>/g, "");

/**
 * objectToUrlParams
 *
 * @param obj any
 * @returns string
 */
export const objectToUrlParams = (obj: any) => {
    return Object.entries(obj)
        .filter(([_, value]) => value !== null && value !== undefined) // Exclude null or undefined fields
        .map(([key, value]) => encodeURIComponent(key) + "=" + encodeURIComponent(value as any))
        .join("&");
};
