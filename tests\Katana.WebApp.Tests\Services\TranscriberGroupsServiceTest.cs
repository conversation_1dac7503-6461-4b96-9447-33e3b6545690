﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Services.TranscriberGroupConfigs.Models;
using Katana.Services.TranscriberGroups;
using Katana.Services.TranscriberGroups.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// TranscribersGroup CRUD service
    /// </summary>
    public class TranscriberGroupsServiceTest : ITranscriberGroupsService
    {
        /// <inheritdoc />
        public IValidator<TranscribersGroup> Validator { get; }

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddTranscribersGroupDto> Add(AddTranscribersGroupDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddTranscribersGroupDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscribersGroupDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<TranscribersGroupDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<TranscribersGroupDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new TranscribersGroupDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateTranscribersGroupDto> Update(UpdateTranscribersGroupDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateTranscribersGroupDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscribersGroupConfigDto>> GetConfigByGroupId(int groupId,
            SieveModel query)
            => await Task.Run(() =>
                new PagedResults<TranscribersGroupConfigDto>([],
                    default, default, default, default, default, default, default, default));

        #endregion
    }
}
