using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.EntityActions.Extensions;
using Aoshield.Core.Services.Attachments.Extensions;
using Aoshield.Core.Services.Audit.Extensions;
using Aoshield.Core.Services.Audit.Models;
using Aoshield.Core.Services.Reminder.Extensions;
using Aoshield.Core.Utils;
using Aoshield.Services.AI;
using Aoshield.Services.ApiCall.Core.Extensions;
using Aoshield.Services.Core;
using Aoshield.Services.Core.Extensions;
using Aoshield.Services.Core.Identity.Extensions;
using Aoshield.Services.Core.Identity.Models;
using Aoshield.Services.Core.Search.Extensions;
using Aoshield.Services.Core.UrlSigning.Filters;
using Aoshield.Services.EntityMerge.Core.Extensions;
using Aoshield.Services.Export.Core.Extensions;
using Aoshield.Services.Import.Core.Extensions;
using Aoshield.Services.Messaging.ChatHub;
using Aoshield.Services.Notification.Core.Extensions;
using Aoshield.Services.Notification.Core.Models;
using Aoshield.Services.ServicesBus.Extensions;
using Katana.Core.Entities;
using Katana.Database;
using Katana.Services;
using Katana.Services.Accuro;
using Katana.Services.Accuro.Models;
using Katana.Services.AppSettings.Models;
using Katana.Services.AzureAIIntegration;
using Katana.Services.Careplans;
using Katana.Services.Common;
using Katana.Services.Common.FaxesService;
using Katana.Services.Common.FaxesService.Messaging;
using Katana.Services.Common.Identity;
using Katana.Services.Common.Messaging;
using Katana.Services.Common.Models;
using Katana.Services.Common.Sieve;
using Katana.Services.EntityMerge.Profiles;
using Katana.Services.Export.Profiles.InvoiceItems;
using Katana.Services.HttpApiGateway.Extensions;
using Katana.Services.Import.Profiles.SpecialtyDiagnostics;
using Katana.Services.OpenAI.Extensions;
using Katana.Services.PatientHistoryItems;
using Katana.Services.Patients;
using Katana.Services.Templates.Profiles;
using Katana.Services.Treatmentplans;
using Katana.Services.Users.Models;
using Katana.Services.Validators;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace Katana.WebApp
{
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            Env = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Env { get; set; }


        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Controllers
            //
            services.AddControllers().AddNewtonsoftJson();

            // SPA
            //
            services.AddSpaStaticFiles(configuration => configuration.RootPath = "ClientApp/build");

            // Swagger
            //
            // Register the Swagger generator, defining 1 or more Swagger documents
            services.AddSwaggerGen(config =>
            {
                config.SwaggerDoc("v1",
                    new OpenApiInfo()
                    {
                        Title = "Katana API",
                        Description = "Katana API",
                        Version = "v1"
                    });
                config.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Katana.Core.xml"));
                config.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Katana.Services.xml"));
                config.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Katana.WebApp.xml"));
                config.AddSecurityDefinition("Bearer",
                    new OpenApiSecurityScheme
                    {
                        In = ParameterLocation.Header,
                        Description = "Please enter token",
                        Name = "Authorization",
                        Type = SecuritySchemeType.Http,
                        BearerFormat = "JWT",
                        Scheme = "bearer"
                    });
                config.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme, Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });
            // Adding memory Cache
            services.AddMemoryCache();

            // Services
            services.AddScoped<SignedUrlAttribute>();
            services.AddDataAccessInfrastructure<KatanaDbContext, User, UserDto, BaseAddUserDto, BaseUpdateUserDto, CareplansService, CareplanValidator, CareplansAutomapper, Careplan, SieveCustomSort, SieveCustomFilter>(Configuration, Env).
               AddEntityActions<CareplansActions, User, AppResources, AppAuthType>(Configuration).
               AddAosIdentityServices<User, UserDto, BaseAddUserDto, BaseUpdateUserDto, AppRoles, AppResources, AppAuthType>(Configuration,
                    currentUserConfigureSet: set =>
                    {
                        return set.Include(u => u.Transcriber)
                            .Include(u => u.TranscribersManager)
                            .Include(u => u.Practitioner).ThenInclude(p => p.AssignedPractitioners)
                            .Include(u => u.Practitioner).ThenInclude(p => p.CQPools)
                            .Include(u => u.Patient);
                    },
                    extraMappingHandler: (config, userDtoMappingExpression, _) =>
                    {
                        userDtoMappingExpression.
                            ForMember(dto => dto.TranscriberId, exp => exp.MapFrom<int?>(u => u.Transcriber != null ? u.Transcriber.Id : null)).
                            ForMember(dto => dto.TranscribersManagerId, exp => exp.MapFrom<int?>(u => u.TranscribersManager != null ? u.TranscribersManager.Id : null)).
                            ForMember(dto => dto.PractitionerId, exp => exp.MapFrom<int?>(u => u.Practitioner != null ? u.Practitioner.Id : null)).
                            ForMember(dto => dto.PatientId, exp => exp.MapFrom<int?>(u => u.Patient != null ? u.Patient.Id : null));

                        config.CreateMap<Transcriber, TranscriberCommonDto>();
                        config.CreateMap<TranscribersManager, TranscribersManagerCommonDto>();
                        config.CreateMap<Practitioner, PractitionerCurrentUserDto>();
                        config.CreateMap<Patient, PatientCommonDto>();
                    }, addActionBasedAuthorizationAttribute: true).
                AddAosApiCallServices<User, AppResources, AppAuthType>(Configuration, Env).
                AddAosMergeServices<PatientMergeProfile, Careplan, User, AppResources, AppAuthType>(Configuration, Env).
                AddAosImportServices<SpecialtyDiagnosticImportProfile, User, AppResources, AppAuthType>(Configuration).
                AddAosExportServices<InvoiceOCodeExportProfile, User, AppResources, AppAuthType>(Configuration).
                AddAosSearchServices<User, AppResources, AppAuthType>(Configuration, Env).
                AddAosReminders<User, AppResources, AppAuthType>(Configuration).
                AddAosAttachments<User, AppResources, AppAuthType>().
                RegisterServiceBusQueues<User, SBIncomingFaxMessageProcessor>(Configuration, Env).
                AddAosAuditServices<User>(Configuration, Env, AuditStorageType.Cosmos);

            services.AddScoped<ITreatmentplansService, TreatmentplansService>();

            services.AddAutoMapper(cfg =>
            {
                var accuroSection = Configuration.GetSection(nameof(AccuroSettings));
                var noteSection = Configuration.GetSection(nameof(NotesSettings));

                services.Configure<AccuroSettings>(accuroSection);
                services.Configure<NotesSettings>(noteSection);

                var accuroSettings = new AccuroSettings();
                var noteSettings = new NotesSettings();

                accuroSection.Bind(accuroSettings);
                noteSection.Bind(noteSettings);

                cfg.AddProfile(new AccuroIntegrationAutomapper(accuroSettings));
                cfg.AddProfile(new PatientHistoryItemAutomapper(accuroSettings));

                cfg.AddProfile(new CareplansAutomapper(noteSettings));
                cfg.AddProfile(new PatientAutomapper(noteSettings));
            });

            // Add IFaxable services
            var faxableServices = TypeUtils.GetTypesAssignableFrom<IFaxable>(Assembly.GetAssembly(typeof(TreatmentplansService)));

            #region Pending refactor


            foreach (var faxableServiceType in faxableServices)
            {
                services.AddScoped(typeof(IFaxable), faxableServiceType);
            }

            services.AddScoped<IAzureAIIntegrationService, AzureAIIntegrationService>();

            // Azure FormRecognizer
            //
            services.AddAosFormRecognizer<KatanaFormRecognizerConfiguration>(Configuration.GetSection("AzureFormRecognizer"));

            var platformsConfig = new PlatformsConfig
            {
                Email = [EmailPlatform.SendGrid],
                MsTeams = true
            };

            services.AddAosNotifications<User, CareplanTemplateProfile, AppResources, AppAuthType>(platformsConfig, Configuration, Env);

            if (!Env.IsTesting())
            {
                services.AddAosSignalR<User>("Katana", user =>
                {
                    var groups = new List<string>();
                    if (user.TranscribersManager != null)
                    {
                        groups.Add(KatanaChatHubGroup.TranscriberManagers.ToString());
                    }
                    if (user.Transcriber != null)
                    {
                        groups.Add(KatanaChatHubGroup.Transcribers.ToString());
                    }
                    return groups.ToArray();
                });
            }

            services.AddKatanaCoreServices(Configuration);


            // Open AI Services
            services.AddOpenAIServices(Configuration);

            #endregion

            // Application Insights & Telemetry collection
            //
            services.AddApplicationInsightsTelemetry();

            // ProblemDetails
            //
            //services.AddProblemDetailsConventions();  // This adds a list of problems. We may need this in the future.
            services.AddAosProblemDetails();

            // HttpApiGateway for Microservices
            services.AddHttpApiGatewayServices(Configuration);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app /*, IWebHostEnvironment env*/)
        {
            // Show Developer error page or ProblemDetails response based on the request type (UI or API?)
            //
            // (we need to wrap is "is ui request? in a middleware because the context object is not available in the
            // Configure method)
            // https://www.alexdresko.com/2019/08/30/problem-details-error-handling-net-core-3/
            //
            app.UseIfElse(IsUIRequest, Env);


            // Force HTTS
            //
            app.UseHttpsRedirection();

            app.AddAosCommonMiddleware(Env);

            // Register the Swagger generator and the Swagger UI middlewares
            //
            var featureManager = app.ApplicationServices.GetRequiredService<IFeatureManager>();

            if (!Env.IsTesting())
            {
                app.UseHangfire(KatanaServicesConfiguration.AddKatanaCronJobs, featureManager.IsEnabledAsync(ICommonFeatures.DisableCrons).GetAwaiter().GetResult(), Env);
            }

            app.UseSwagger();

            // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
            // specifying the Swagger JSON endpoint.
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Katana API V1"));

            // Request endpoints
            //
            var krudderDbConfiguration = app.ApplicationServices.GetRequiredService<IOptions<KrudderDbConfiguration>>().Value;
            var corsPolicy = Env.IsDevelopment() ? "DevelopmentCORS" : "ProductionCORS";
            app.UseEndpoints(endpoints =>
            {
                if (Env.IsTesting())
                {
                    endpoints.MapControllers().RequireAuthorization();
                }
                else
                {
                    endpoints.MapControllers().RequireCors(corsPolicy).RequireAuthorization();
                }

                // Any route not mapped to a controller or a static filepack
                // is considered part of the ClientApp (SPA) and is automatically sent
                // to it. The exception to this rule are routes begining with "/api".
                // These routes are forced to be handled by the backend, which
                // could return 404 if necessary. If we let invalid routes
                // starting with /api to also be sent to the SPA, we could have
                // routing loops where requests timeout only after an extremenly long
                // time (5+ mins) instead of failing fast with a 404 code and thus
                // providing quick feedback to the user in the client app
                endpoints.Map("/api/{**slug}", HandleApiFallback);

                if (!Env.IsTesting())
                {
                    endpoints.MapHub<ChatHub<User>>("/hubs/chat").RequireCors(corsPolicy).RequireAuthorization();
                }
            });

            // Serve SPA (front-end) app
            //

            app.UseStaticFiles();
            app.UseSpaStaticFiles();

            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = "ClientApp";

                if (Env.IsDevelopment())
                {
                    spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
                }
            });

            if (!Env.IsTesting())
            {
                app.ApplicationServices.StartServiceBusProcessors();
            }

        }

        private Task HandleApiFallback(HttpContext context)
        {
            context.Response.StatusCode = StatusCodes.Status404NotFound;
            return Task.CompletedTask;
        }

        private static bool IsUIRequest(HttpContext httpContext)
        {
            var requestPath = httpContext.Request.Path;
            return requestPath == "/" ||
                   !requestPath.StartsWithSegments($"/api", StringComparison.OrdinalIgnoreCase);
        }
    }

#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
}
