﻿using Aoshield.Core.DataAccess.Models;
using Katana.Core.Entities;
using Katana.Services.PatientPoolItems;
using Katana.Services.PatientPoolItems.Models;
using Katana.Services.Treatmentplans.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// PatientPoolItems Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PatientPoolItemsController : ControllerBase
    {
        private readonly IPatientPoolItemsService _patientPoolItemsService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="crudService">CRUD service</param>
        public PatientPoolItemsController(IPatientPoolItemsService crudService) => _patientPoolItemsService = crudService;

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<PatientPoolItemDto>> GetPatientPoolItems([FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientPoolItemsService.GetAsPagedResults(query, cancellation);

        /// <summary>
        /// Get Dto by id
        /// </summary>
        /// <param name="id">Id of the entity</param>
        /// <param name="cancellation"></param>
        /// <returns>Dto</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<PatientPoolItemDto>> GetPatientPoolItem(int id, CancellationToken cancellation)
        {
            var dto = await _patientPoolItemsService.GetById(id, cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientPoolItemsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientPoolItemsService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientPoolItemsService.DisableBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch([FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientPoolItemsService.EnableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Get all patients by Custodian Gp or Custodian NP
        /// </summary>
        /// <returns>List of patients</returns>
        /// <param name="query">Sieve model</param>
        /// <param name="cancellation">Cancellation token</param>
        [HttpGet("PatientPanelingByCustodianGP")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetPatientPanelingByCustodianGP(
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientPoolItemsService.GetPatientPanelingByCustodianGP(query, cancellation);

        /// <summary>
        /// HPC Finalize Paneling for Patient(s)
        /// </summary>
        /// <param name="patientPoolItemsDto"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("HPCFinalizePaneling")]
        public async Task<int> HPCFinalizePaneling(BaseUpdateDto[] patientPoolItemsDto, CancellationToken cancellation)
            => await _patientPoolItemsService.HPCFinalizePaneling(patientPoolItemsDto, cancellation);

        /// <summary>
        /// HPC Finalize Paneling for Patient(s)
        /// </summary>
        /// <param name="patientPoolItemsDto"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("CancelRequest")]
        public async Task<int> CancelRequest(ConfirmationNoteDto[] patientPoolItemsDto, CancellationToken cancellation)
            => await _patientPoolItemsService.CancelRequest(patientPoolItemsDto, PatientPoolItemActions.CancelRequest.Name, cancellationToken: cancellation);

        /// <summary>
        /// GP Send back the PatientPoolItems from InReview To Started
        /// </summary>
        /// <param name="patientPoolItemsDto"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("SendBackToHpc")]
        public async Task<int> SendBackToHpc(ConfirmationNoteDto[] patientPoolItemsDto, CancellationToken cancellation)
            => await _patientPoolItemsService.SendBackToHpc(patientPoolItemsDto, cancellation);

        /// <summary>
        /// GP Send back the PatientPoolItems from Completed To InReview
        /// </summary>
        /// <param name="patientPoolItemsDto"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("SendBackToGp")]
        public async Task<int> SendBackToGp(ConfirmationNoteDto[] patientPoolItemsDto, CancellationToken cancellation)
        {
            return await _patientPoolItemsService.SendBackToGp(patientPoolItemsDto,
                cancellation);
        }

        /// <summary>
        /// Get My Pool of Patients
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("AvailablePanelingList")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetMyPoolPatients([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetMyPoolPatients(query);

        /// <summary>
        /// Get My Pool of Patients
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("CompletedHpcPanelingList")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForHpc([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetCompletedPatientsForHpc(query);

        /// <summary>
        /// Get My Pool of Patients
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("CompletedMvcaPanelingList")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForMvca([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetCompletedPatientsForMvca(query);

        /// <summary>
        /// Get Upcoming Patients for HPC or MVCAs
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("UpcomingPatientsHpc")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetUpcomingPatientsForHpc([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetUpcomingPatientsForHpc(query);

        /// <summary>
        /// Get Upcoming Patients for HPC or MVCAs
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("UpcomingPatientsMvca")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetUpcomingPatientsForMvca([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetUpcomingPatientsForMvca(query);


        /// <summary>
        /// Get All completed Patients for GpItp Lead
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("AllCompletedPanelingList")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForHpcLead([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetCompletedPatientsForHpcLead(query);

        /// <summary>
        /// Get Upcoming Patients for HPC Lead
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("UpcomingPatientsHpcLead")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetUpcomingPatientsForHpcLead([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetUpcomingPatientsForHpcLead(query);

        /// <summary>
        /// Get Upcoming Patients for GP
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("UpcomingPatientsGp")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetUpcomingPatientsForGp([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetUpcomingPatientsForGp(query);

        /// <summary>
        /// Get Clinical Questions by Patient Pool Item
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("{id}/ClinicalQuestions")]
        public async Task<IPagedResults<ClinicalQuestionDto>> GetClinicalQuestionsByPatient(int id, [FromQuery] SieveModel query,
            CancellationToken cancellation = default) => await _patientPoolItemsService.GetClinicalQuestionsByPatientPoolItem(id, query, cancellation);

        /// <summary>
        /// Get Attachments by Patient Pool Item
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("{id}/attachments")]
        public async Task<IPagedResults<CQPatientPoolItemAttachmentDto>> GetAttachmentsByPatientPoolItem(int id, [FromQuery] SieveModel query, CancellationToken cancellation = default) =>
            await _patientPoolItemsService.GetAttachmentsByPatientPoolItem(id, query, cancellation);

        /// <summary>
        /// Get workflow statuses
        /// </summary>
        /// <param name="id">Request's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/WorkflowStatus")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<CQPatientPoolItemWorkflowStatus>>>
            GetWorkflowStatusLogs(int id, [FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetWorkflowStatusLogsAsPagedResults(id, query);

        /// <summary>
        /// Get Mine for HPC and MVCA
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet("MineHPCMVCA")]
        public async Task<IPagedResults<PatientPoolItemDto>> GetMineHPCMVCA([FromQuery] SieveModel query) =>
            await _patientPoolItemsService.GetMineHPCMVCA(query);
    }
}
