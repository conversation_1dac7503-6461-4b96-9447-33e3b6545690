﻿using Aoshield.Core.Services.Notification;
using Aoshield.Services.Notification.Core.Entities;
using Aoshield.Services.Notification.Core.Models;
using Katana.Core.Entities;
using Katana.Services.Careplans;

namespace Katana.Tests.Common.Utils.Seeders
{
    public partial class TestDataSeeder
    {
        /// <summary>
        /// Seed Notification Templates entities
        /// </summary>
        public void SeedNotificationTemplates()
        {
            var entities = new List<NotificationTemplate>()
            {
                new()
                {
                    Action = CareplansActions.AssignSp.Name,
                    Name = nameof(Careplan),
                    Template = nameof(Careplan),
                    EntityType = nameof(Careplan),
                    Recipients =
                    [
                        new()
                        {
                            NotificationType = NotificationType.Email,
                            Type = NotificationRecipientType.Property,
                            Value = "Sp.Email",
                        }
                    ]
                }
            };

            SeedEntity(() => entities);
        }
    }
}
