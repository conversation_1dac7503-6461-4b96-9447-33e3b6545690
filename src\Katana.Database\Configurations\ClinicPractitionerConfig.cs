using Katana.Core.Entities;
using Katana.Database.Configurations.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Katana.Database.Configurations
{
    /// <summary>
    /// Entity config
    /// </summary>
    public class ClinicPractitionerConfig : IEntityTypeConfiguration<ClinicPractitioner>
    {
        /// <summary>
        /// Configure method called by db context OnModelCreating()
        /// </summary>
        /// <param name="builder"></param>
        public void Configure(EntityTypeBuilder<ClinicPractitioner> builder)
        {
            builder.ConfigureBaseEntity();
            builder.HasIndex(m => m.ClinicId);
            builder.HasIndex(m => m.PractitionerId);
            builder.HasIndex(m => new { m.ClinicId, m.PractitionerId, m.EmrId }).HasFilter("[SoftDeleteLevel] = 0").IsUnique();
            builder.HasOne(c => c.Clinic)
                .WithMany(c => c.Practitioners)
                .HasForeignKey(c => c.ClinicId)
                .OnDelete(DeleteBehavior.Cascade);
            builder.HasOne(c => c.Practitioner)
                .WithMany(c => c.Clinics)
                .HasForeignKey(c => c.PractitionerId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
