using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using AutoMapper;
using Bogus;
using Katana.Core.Entities;
using Katana.Tests.Common.Utils;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sieve.Models;
using Xunit;

namespace Katana.Services.Tests.Common
{
    /// <summary>
    /// KatanaDbContext (the real one) tests.
    /// </summary>
    public class KatanaKrudderWithClaimsTests : ServicesUnitTestConfiguration
    {
        private readonly Krudder<User> _krudder;
        private readonly IClaimsPrincipalContext<User> _claimsPrincipalContext;
        private readonly IDataContext _dataContext;

        /// <summary>
        /// Public constructor
        /// </summary>
        public KatanaKrudderWithClaimsTests(ConfigurationFixture configurationFixture) : base(configurationFixture)
        {
            _claimsPrincipalContext = ServiceProvider.GetRequiredService<IClaimsPrincipalContext<User>>();
            _dataContext = ServiceProvider.GetService<IDataContext>();
            _krudder = new Krudder<User>(ServiceProvider.GetService<DataContextTransactionTracker>(), ServiceProvider.GetService<IServiceProvider>(),
                ServiceProvider.GetService<IServiceScopeFactory>(),
               ServiceProvider.GetService<IDataContext>(),
               ServiceProvider.GetService<ILogger<IKrudder<User>>>(),
               ServiceProvider.GetService<ISieveProcessor>(),
               ServiceProvider.GetService<IOptions<SieveOptions>>(),
               ServiceProvider.GetService<IMapper>(),
               ServiceProvider.GetService<IOptions<KrudderDbConfiguration>>(),
               ServiceProvider.GetService<IUniqueValidationController<User>>(),
               ServiceProvider.GetService<IClaimsPrincipalContext<User>>());
        }

        /// <summary>
        /// Test run to OnBeforeSaving method
        ///     Scenario: When Adding Country entity with a valid user mocked
        ///     Expected result: The audit field not empty and contain values mocked to user claims.
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task OnBeforeSaving_WhenAddedEntityWithClaimPrincipal_GeneratedSuccesFieldAudit()
        {
            // Arrange
            var country = new Faker<Country>()
                .RuleFor(c => c.Name, x => x.Random.Words(3))
                .Generate();

            // Act
            await _krudder.Add(country, null);

            // Assert
            Assert.NotNull(country?.CreatedAt);
            Assert.NotNull(country?.UpdatedAt);
            var currentUser = await _claimsPrincipalContext.GetCurrentUser(_dataContext);
            Assert.Equal(currentUser?.DisplayName, country.CreatedBy);
            Assert.Equal(currentUser?.DisplayName, country.UpdatedBy);
            Assert.Equal(currentUser?.ObjectId, country.CreatedById);
            Assert.Equal(currentUser?.ObjectId, country.UpdatedById);
        }
    }
}
