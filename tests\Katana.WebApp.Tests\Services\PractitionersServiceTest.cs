﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Validation;
using Aoshield.Services.Core.Search.Models;
using Katana.Core.Entities;
using Katana.Services.ClinicPractitioners.Models;
using Katana.Services.PractitionerAssignedPractitioners.Models;
using Katana.Services.PractitionerDiagnostics.Model;
using Katana.Services.Practitioners;
using Katana.Services.Practitioners.Models;
using Katana.Services.PractitionerSpecialties.Model;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Practitioners CRUD service
    /// </summary>
    public class PractitionersServiceTest : IPractitionersService
    {
        ///<inheritdoc/>
        public FluentValidation.IValidator<Practitioner> Validator =>
            throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPractitionerDto> Add(AddPractitionerDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPractitionerDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PractitionerListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<PractitionerListDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PractitionerDto>
            GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new PractitionerDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdatePractitionerDto> Update(UpdatePractitionerDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePractitionerDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<AddPractitionerSpecialtyDto[]> AddSpecialtiesToPractitioner(
            AddPractitionerSpecialtyDto[] practitionerSpecialtyDtos, CancellationToken cancellation) =>
            Task.Run(() =>
                practitionerSpecialtyDtos
                    .Select((p, i) => new AddPractitionerSpecialtyDto()).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableSpecialtiesBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableSpecialtiesBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DeleteSpecialtiesFromPractitioner(DeletePractitionerSpecialtyDto[] dtoArr, CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Select(x => x.Id).ToArray());

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddNoteDto> AddNote(AddNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddNoteDto());

        ///<inheritdoc/>
        public async Task<NoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new NoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<NoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<NoteDto>([], default, default, default, default,
                default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdateNoteDto> UpdateNote(UpdateNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Practitioner> ConfigureSet(IQueryable<Practitioner> _, Type _1, ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Practitioner _, IList<Practitioner> _1, string _2, Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Queries

        /// <summary>
        /// For retrieve all Practitioner that belongs to a Clinic
        /// </summary>
        /// <param name="clinicId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<ClinicPractitionerDto>> GetClinicPractitionersByClinicId(
            int clinicId,
            SieveModel query) =>
            await Task.Run(() => new PagedResults<ClinicPractitionerDto>(
                [],
                default, default, default, default, default, default, default, default));

        /// <summary>
        /// To retrieve all practitioners that are enabled to be added to a Clinic
        /// </summary>
        /// <param name="clinicId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<PractitionerDto>> GetAvailablePractitionersByClinicId(
            int clinicId, SieveModel query) =>
            await Task.Run(() => new PagedResults<PractitionerDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public Task<IPagedResults<PractitionerDto>> GetHPCAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        /// <inheritdoc />
        public Task<ConvertAliasDto[]> ConvertToAlias(ConvertAliasDto[] dtos,
            CancellationToken cancellation = default) => Task.FromResult(dtos);

        /// <inheritdoc />
        public Task<int[]> DeleteBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos, CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> RestoreBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            CancellationToken cancellation = default) => Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public async Task<IPagedResults<AliasDto>> GetAllAliasesByEntityId(SieveModel query, int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<AliasDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc />
        public Task<AddAliasDto[]> AddBatchAliases(AddAliasDto[] dtos,
            CancellationToken cancellation = default) => Task.FromResult(dtos);

        /// <inheritdoc />
        public Task<AddAliasDto>
            AddAlias(AddAliasDto dto, CancellationToken cancellation = default) =>
            Task.FromResult(dto);

        /// <inheritdoc />
        public Task<int[]> DisableBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        /// <inheritdoc />
        public Task<IEnumerable<Practitioner>> SearchPractitioner(string displayName, CancellationToken cancellation = default) =>
            Task.FromResult((IEnumerable<Practitioner>) [new() { DisplayName = displayName }]);

        /// <inheritdoc />
        public Task<UpdatePractitionerSettingsDto> UpdatePractitionerSettings(UpdatePractitionerSettingsDto dto, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IList<Alias>> GetAllAliasesByEntityId(int id, CancellationToken cancellation = default) =>
            Task.FromResult(new List<Alias>() as IList<Alias>);

        /// <inheritdoc />
        public Task<IPagedResults<AliasDto>> GetAllAliasesByEntity(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> AssignPractitioner(AddPractitionerAssignedPractitionerDto[] dto, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PractitionerDto>> GetAvailablePractitionersForPractitionerId(int practitionerId, int type, SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PractitionerAssignedPractitionerDto>> GetAssignedPractitionersByPractitionerId(int practitionerId, SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        #region Practitioner Diagnostics

        /// <inheritdoc />
        public Task<AddPractitionerDiagnosticDto[]> AddDiagnosticsToPractitioner(AddPractitionerDiagnosticDto[] practitionerDiagnosticDtos) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DeleteDiagnosticsFromPractitioner(ConfirmationNoteDto[] dtoArr) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DisableDiagnosticsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableDiagnosticsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<SpecialtyPractitionerDto>> GetPractitionersBySpecialtyId(int id, SieveModel query) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> DeletePractitionersFromSpecialty(ConfirmationNoteDto[] dtoArr) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PractitionerSettingsDto> GetPractitionerSettings(int practitionerId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        List<PractitionerDiagnostic> IPractitionersService.CreatePractitionerDiagnostics(List<PractitionerDiagnostic> allPractitionerDiagnostics, IEnumerable<int> practitionersIds, IEnumerable<int> diagnosticsIds, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> ToggleOptOutClinicalQuestionsInitialReview(BaseUpdateDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PractitionerListDto>> GetAvailablePractitionersByDiagnosticsId(int[] diagnosticIds, SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<Dictionary<DateTime, Dictionary<int, int>>> GetNumberOfAssignmentsByPractitioner(int[] practitionerIds, DateTime startDate, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PractitionerDto>> GetPractitionersByClinicIds(int[] clinicId, SieveModel query) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> SendPatientsToPrePaneling(BaseUpdateDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        #endregion
    }
}
