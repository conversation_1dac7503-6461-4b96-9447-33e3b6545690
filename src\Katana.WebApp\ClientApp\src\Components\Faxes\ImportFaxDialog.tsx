import { Grid } from "@mui/material";
import { KUseQueryAutocomplete } from "Components/Common/Autocomplete/KUseQueryAutocomplete";
import { KDialog } from "Components/Common/Dialogs/KDialog";
import { useClinicsAutocomplete } from "Components/Common/Hooks/useClinicsAutocomplete";
import {
    useKatanaSnackAndNotifyError,
    useKatanaSnackAndNotifySuccess,
} from "Components/Common/Hooks/useKatanaSnackNotify";
import { KDropzoneArea } from "Components/Common/KDropzoneArea/KDropzoneArea";
import { AlertType } from "Components/Common/KDropzoneArea/types";
import { useKatanaApiClient } from "Components/Common/Providers/KatanaApiClientProvider";
import { useKatanaSnackbar } from "Components/Common/Providers/KatanaSnackbarProvider";
import { KUploadDialogProps } from "Components/types";
import { ReactQueryKeys } from "lib/reactQuery/reactQueryKeys";
import { KATANA_EMPTY_FIELD, nameofFactory } from "lib/utensils";
import { useCallback, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { QueryClient, useMutation } from "@tanstack/react-query";
import { FaxCommonActionsDto } from "lib/models/Faxes/FaxCommonActionsDto";

/** ImportFaxDialogProps */
export interface ImportFaxDialogProps extends KUploadDialogProps {
    /** Query Client */
    queryClient: QueryClient;
}

/**
 * Katana Import Fax Dialog
 *
 * @param props Component props
 * @returns Component
 */
export const ImportFaxDialog: React.FC<ImportFaxDialogProps> = (props) => {
    //Translation
    const { t } = useTranslation();
    // Katana Api client
    const katanaApiClient = useKatanaApiClient();

    //Notify
    const snackAndNotifySuccess = useKatanaSnackAndNotifySuccess();
    const snackAndNotifyError = useKatanaSnackAndNotifyError();

    // Snackbar
    const katanaSnackbar = useKatanaSnackbar();

    // Props
    const { dialogProps, showDialog, queryClient } = props;

    const [selectedFiles, setSelectedFiles] = useState<File[] | undefined>(undefined);
    const [isProcessing, setIsProcessing] = useState(false);

    // Submit

    const uploadMutation = useMutation(
        async (data: any) => {
            setIsProcessing(true);
            const formData = new FormData();
            selectedFiles && selectedFiles.forEach((file) => formData.append("files", file));

            const faxName = !!data.clinic.faxNumber
                ? data.clinic.faxNumber
                : !!data.clinic.telephone
                ? data.clinic.telephone
                : data.clinic.name;

            await katanaApiClient.Faxes.ImportFaxes(faxName, formData, data.clinic.id);
        },
        {
            onSuccess: () => {
                snackAndNotifySuccess(
                    `${t("_pages:fax.actions.import.success.message", {
                        count: selectedFiles?.length,
                    })}`
                );
            },

            onSettled: () => {
                setIsProcessing(false);
                queryClient.invalidateQueries([ReactQueryKeys.Fax]);
                showDialog(false);
            },

            onError: (error: Error) => {
                return snackAndNotifyError(`${error.message}`);
            },
        }
    );

    const fileChange = useCallback((files: File[]) => {
        setSelectedFiles(
            files.filter((value, index, self) => index === self.findIndex((t) => t === value))
        );
    }, []);

    const addFaxFormHook = useForm();
    const nameOfFaxDto = nameofFactory<FaxCommonActionsDto>();
    const clinicsAutocompleteProps = useClinicsAutocomplete();

    //#region Main component
    return (
        <KDialog
            fullScreen
            {...dialogProps}
            title={t("_pages:fax.actions.import.dialogTitle")}
            dialogProps={{
                open: dialogProps.open,
            }}
            handleOk={addFaxFormHook.handleSubmit((data) => uploadMutation.mutate(data))}
            handleCancel={() => showDialog(false)}
            isProcessing={isProcessing}
        >
            <form
                encType="multipart/form-data"
                onSubmit={addFaxFormHook.handleSubmit(() => void 0)}
                noValidate
            >
                <Grid container direction="column">
                    <Grid item xs={12}>
                        {/* Clinic */}
                        <Controller
                            render={({ field: { value, onChange, ...childProps } }) => (
                                <KUseQueryAutocomplete
                                    {...childProps}
                                    {...clinicsAutocompleteProps}
                                    value={value}
                                    onChange={(_, data) => onChange(data)}
                                    textFieldProps={{
                                        label: `${t(`_entities:fax.${nameOfFaxDto("clinic")}`)}`,
                                        helperText: (
                                            <>{addFaxFormHook.formState.errors.clinic?.message}</>
                                        ), //if we return null here the component will show the built in helperText (based on 'searchFields' prop)
                                        error: !!addFaxFormHook.formState.errors[
                                            nameOfFaxDto("clinic")
                                        ],
                                        required: true,
                                    }}
                                    disabled={isProcessing}
                                />
                            )}
                            rules={{
                                required: `${`${t(`_entities:fax.${nameOfFaxDto("clinic")}`)}`} ${t(
                                    "_common:required"
                                )}`,
                            }}
                            name={nameOfFaxDto("clinic")}
                            control={addFaxFormHook.control}
                        />
                    </Grid>
                    {/* Files */}
                    <Grid item xs={12}>
                        <Controller
                            render={({ field: { onChange } }) => (
                                <KDropzoneArea
                                    disabled={isProcessing}
                                    multiple
                                    error={!!addFaxFormHook.formState.errors.files}
                                    helperText={
                                        (addFaxFormHook.formState.errors.files?.message as string) ||
                                        (selectedFiles?.length === 0
                                            ? `${t("_pages:fax.actions.import.dropzone.helperText")}`
                                            : KATANA_EMPTY_FIELD)
                                    }
                                    useChipsForPreview
                                    accept={{ "application/pdf": [".pdf"] }}
                                    dropzoneText={`${t("_pages:fax.actions.import.dropzone.text")}`}
                                    previewText={`${t(
                                        "_pages:fax.actions.import.dropzone.previewText"
                                    )}`}
                                    maxFiles={10}
                                    onChange={(files) => {
                                        fileChange(files);
                                        onChange(files);
                                    }}
                                    onAlert={(msg: string, variant: AlertType) => {
                                        // I'm not using snackAndNotify here to avoid polluting the notifications panel
                                        // with silly messages such as "File loaded" that are only relevant to the DropZone component
                                        katanaSnackbar.showWithMessage({ message: msg, type: variant });
                                    }}
                                />
                            )}
                            name="files"
                            control={addFaxFormHook.control}
                            rules={{
                                validate: (value: File[]) => {
                                    if (!value || value.length === 0) {
                                        return `${t("_common:areRequired")}`;
                                    }
                                    return true;
                                },
                            }}
                        />
                    </Grid>
                </Grid>
            </form>
        </KDialog>
    );
};
