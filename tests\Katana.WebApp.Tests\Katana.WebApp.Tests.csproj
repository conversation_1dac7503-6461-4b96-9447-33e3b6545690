﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <IsTestProject>true</IsTestProject>
    <AnalysisLevel>latest</AnalysisLevel>
    <UserSecretsId>8fe5b4a6-efc6-4eed-90ad-f838d9820ef4</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <Using Include="Katana.Core.Entities.User" Alias="User" />
    <Using Include="Aoshield.Services.Core.Search.Entities.Alias" Alias="Alias" />
    <Using Include="Sieve.Services" />
    <!--Sieve-->
    <Using Include="Sieve.Models.SieveModel" Alias="SieveModel" />
    <Using Include="Aoshield.Core.DataAccess.Sieve" />
    <!--Models-->
    <Using Include="Aoshield.Core.Entities.Models.BaseEntityDto" Alias="BaseEntityDto" />
    <Using Include="Aoshield.Core.Entities.Models.BaseAddDto" Alias="BaseAddDto" />
    <Using Include="Aoshield.Core.Entities.Models.BaseUpdateDto" Alias="BaseUpdateDto" />
    <Using Include="Aoshield.Core.Entities.Models.ConfirmationNoteDto" Alias="ConfirmationNoteDto" />
    <!--Actions-->
    <Using Include="Aoshield.Core.EntityActions.EntityAction" Alias="EntityAction" />
    <Using Include="Aoshield.Core.EntityActions.GlobalActions" Alias="GlobalActions" />
    <Using Include="Aoshield.Core.EntityActions.HttpMetadata" Alias="HttpMetadata" />
    <Using Include="Aoshield.Core.EntityActions.IActionable" Alias="IActionable" />
    <Using Include="Aoshield.Core.EntityActions.IActionableDto" Alias="IActionableDto" />
    <Using Include="Aoshield.Core.EntityActions.RolesBasedCanDelegate" Alias="RolesBasedCanDelegate" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Controllers\DeferredRequest\**" />
    <EmbeddedResource Remove="Controllers\DeferredRequest\**" />
    <None Remove="Controllers\DeferredRequest\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="WebMotions.Fake.Authentication.JwtBearer" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <ProjectReference Include="..\..\src\Katana.WebApp\Katana.WebApp.csproj" />
    <ProjectReference Include="..\Katana.Services.Tests\Katana.Services.Tests.csproj" />
    <ProjectReference Include="..\Katana.Tests.Common\Katana.Tests.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="xunit.runner.json" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>
</Project>