﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Katana.Core.Entities;
using Katana.Services.Clinics;
using Katana.Services.Clinics.Models;
using Katana.Services.Practitioners.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Clinic CRUD service
    /// </summary>
    public class ClinicsServiceTest : IClinicsService
    {
        ///<inheritdoc/>
        public FluentValidation.IValidator<Clinic> Validator => throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddClinicDto> Add(AddClinicDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddClinicDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<ClinicListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<ClinicListDto>([], default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<ClinicDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new ClinicDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateClinicDto> Update(UpdateClinicDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateClinicDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true, CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());


        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddNoteDto> AddNote(AddNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddNoteDto());

        ///<inheritdoc/>
        public async Task<NoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new NoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<NoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<NoteDto>([], default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdateNoteDto> UpdateNote(UpdateNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>([], default, default, default, default, default, default, default, default));

        #endregion

        #region Custom

        ///<inheritdoc/>
        public static async Task<IPagedResults<PractitionerDto>> GetPractitionersByClinicId() =>
            await Task.Run(() => new PagedResults<PractitionerDto>([], default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public static async Task<IPagedResults<PractitionerDto>> GetAvailablePractitioners() =>
            await Task.Run(() => new PagedResults<PractitionerDto>([], default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<int> UpdateCps(ClinicCpsDto[] clinics, CancellationToken _1 = default) =>
            await Task.Run(() => clinics.Length);

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int> MarkAsReadyForOnboarding(BaseUpdateDto[] dtos, CancellationToken cancellation = default) => Task.Run(() => dtos.Length);

        /// <inheritdoc />
        public Task<int> AssignIntegratorType(AssignClinicIntegratorTypeDto[] dtos, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion
    }
}
