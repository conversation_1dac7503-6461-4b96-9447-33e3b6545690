using Aoshield.Services.Import.Core.Interfaces;
using Aoshield.Services.Import.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Imports Controller - main API entry point for all Katana (Magento-related) data import.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ImportsController : ControllerBase
    {
        private readonly IImportService _importService;

        /// <summary>
        /// Main contructor
        /// </summary>
        /// <param name="importService"></param>
        public ImportsController(
            IImportService importService) => _importService = importService;

        /// <summary>
        ///  Get list
        /// </summary>
        /// <param name="query"></param>
        /// <returns>List of import events</returns>
        [HttpGet]
        public Task<IPagedResults<ImportEventDto>> GetImportEvents(
            [FromQuery] SieveModel query) =>
            _importService.GetAsPagedResults(query);

        /// <summary>
        ///  For retrieving import profiles. This can be uses to show in the front-end a selectable list
        /// </summary>
        /// <param name="query"></param>
        /// <returns>List of Import Profiles</returns>
        [HttpGet("Profiles")]
        public IPagedResults<ImportProfileDto> GetProfiles([FromQuery] SieveModel query) =>
            _importService.GetProfilesAsPagedResults(query);


        /// <summary>
        /// Import file
        /// </summary>
        /// <param name="importProfileTypeName">Import Profile</param>
        /// <param name="fileName">Name of the file to import</param>
        /// <param name="formFile">File to import</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> Import(string importProfileTypeName, string fileName,
            [FromForm] IFormFile formFile, CancellationToken cancellation)
        {
            var memoryStream = new MemoryStream();

            // Copy the file content to a memory buffer
            await formFile.CopyToAsync(memoryStream, cancellation);

            await _importService.Enqueue(memoryStream, importProfileTypeName, fileName,
                false, null, false, cancellation);

            // Let's send data back so that we can do proper asynchronous notification in the front end
            return Ok((FileName: fileName, ImportProfile: importProfileTypeName));
        }

        /// <summary>
        /// For retrieving import events.
        /// </summary>
        /// <param name="query">Sieve query</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of Import Events</returns>
        [HttpGet("ImportEvents")]
        public async Task<IPagedResults<ImportEventDto>> GetImportEvents(
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _importService.GetImportEventsAsPagedResults(query, cancellation);

        /// <summary>
        /// For retrieving ImportEvent details
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>ImportEvent</returns>
        [HttpGet("ImportEvents/{id}")]
        public async Task<ActionResult<ImportEventDto>> GetImportEvent(int id,
            CancellationToken cancellation) =>
            await _importService.GetImportEvent(id, cancellation);

        /// <summary>
        /// For retrying ImportEvent details
        /// </summary>
        /// <param name="dto">list of items to retry</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>ImportEvent</returns>
        [HttpPut("ImportEvents/retry")]
        public async Task<ActionResult<bool>> RetryImportEvents(BaseUpdateDto[] dto,
            CancellationToken cancellation) =>
            await _importService.RetryImportEvents(dto, cancellation);

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // DELETE: api/Faxes(3)
        [HttpDelete("ImportEvents/{id}")]
        public async Task<ActionResult<int>> DeleteImportEvent(int id,
            CancellationToken cancellation) =>
            await _importService.DeleteImportEvent(id, cancellation);

        /// <summary>
        /// For retrieving ImportItem that belongs to an ImportEvent
        /// </summary>
        /// <param name="id">Import event id</param>
        /// <param name="query"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List of Import Items</returns>
        [HttpGet("ImportEvents/{id}/ImportItems")]
        public async Task<IPagedResults<ImportItemDto>> GetImportItems(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _importService.GetItemsByImportEventId(id, query, cancellation);

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("ImportEvents/{id}/Download")]
        public Task<ActionResult> GetFileSasUri(int id, [FromQuery] string fileName)
        {
            var result = _importService.DownloadFile(id, fileName);
            return Task.FromResult<ActionResult>(Ok(new { Url = result.ToString() }));
        }

        /// <summary>
        /// For retrieving ImportItemDetail that belongs to an ImportItem
        /// </summary>
        /// <param name="id">ImportItem id</param>
        /// <param name="query">Query</param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("ImportItems/{id}/ImportItemDetails")]
        public async Task<IPagedResults<ImportItemDetailDto>> GetDetailsByImportItemId(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _importService.GetDetailsByImportItemId(id, query, cancellation);

        /// <summary>
        /// For retrieving ImportItemDetails data
        /// </summary>
        /// <param name="id">ImportItem id</param>
        /// <param name="cancellation"></param>
        /// <returns>ImportItem</returns>
        [HttpGet("ImportItemDetails/{id}")]
        public async Task<ActionResult<ImportItemDetailDto>> GetImportItemDetail(int id,
            CancellationToken cancellation) =>
            await _importService.GetImportItemDetail(id, cancellation);

        /// <summary>
        /// Resolve ImportItemDetail
        /// </summary>
        /// <param name="id">Id of the ImportItemDetail to resolve</param>
        /// <param name="dto">Resolution's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("ImportItemDetails/{id}")]
        public async Task<ActionResult<ImportItemDetailDto>> ResolveImportItemDetail(int id,
            ImportItemDetailDto dto, CancellationToken cancellation)
        {
            if (id != dto.Id)
            {
                BadRequest();
            }

            return await _importService.ResolveImportItemDetail(dto, cancellation);
        }

        /// <summary>
        /// For retrieving ImportItem
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>ImportItem</returns>
        [HttpGet("ImportItems/{id}")]
        public async Task<ActionResult<ImportItemDto>> GetImportItem(int id,
            CancellationToken cancellation) => await _importService.GetImportItem(id, cancellation);

        /// <summary>
        /// For retrying ImportItems
        /// </summary>
        /// <param name="dto">list of items to retry</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>ImportItem</returns>
        [HttpPut("ImportItems/retry")]
        public async Task<ActionResult<int>> RetryImportItems(BaseUpdateDto[] dto,
            CancellationToken cancellation) =>
            await _importService.RetryImportItems(dto, cancellation);
    }
}
