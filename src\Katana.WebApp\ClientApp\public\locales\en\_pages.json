{"home": {"title": "Home", "welcome": "Welcome {{userName}}!", "email": "Email: ", "groups": "App Groups: ", "roles": "App Roles: ", "db": "DataBase: ", "appSettings": "App Settings: "}, "accuro": {"title": "Accuro", "text": "Connection status:", "warning": "External system ACCURO is attempting to connect to your API", "status": {"connected": "Connected", "notConnected": "Not connected"}, "buttons": {"connect": "Connect", "accept": "Accept", "decline": "Decline"}}, "careplan": {"title": "eConsults", "label": "eConsult", "details": {"title": "Details", "tabs": {"detail": "Details", "hPURequest": "HPU Request", "patient": "Patient Information", "reason": "Reason for Consultation", "condition": "Conditions", "medication": "Medications", "allergy": "Allergies", "socialHistory": "Social Histories", "vitalSign": "Vital Signs", "cases": "Cases Notes", "recommendations": "Recommendations", "caseNotes_recommendations": "Case Notes & Recommendations", "requestAttachments": "eConsult Attachments", "attachment": "Attachments", "billing": "Billing", "hPUCareplan": "HPU eConsults", "hPUAttachment": "HPU Attachments", "note": "Notes", "workflowStatus": "eConsult Status Logs", "careplanMedications": "Consult Medications", "careplanConditions": "Consult Conditions", "investigations": "Investigations"}, "approvedSummary": {"quotaA": "Assignment Q<PERSON><PERSON>", "quotaR": "Requesting <PERSON><PERSON><PERSON>", "approved": "Assigned eConsults", "requested": "Requested eConsults", "weekly": "Weekly", "daily": "Daily"}}, "tooltips": {"daysToExpire": {"day_one": "One day left until the eConsult expires.", "day_other": "{{count}} days left until the eConsult expires.", "day_zero": "The eConsult expires today."}, "expired": {"day_one": "The eConsult expired one day ago.", "day_other": "The eConsult expired {{count}} days ago.", "day_zero": "The eConsult expires today.", "text": "Days"}}, "actions": {"sendBack": {"text": "Send Back", "tooltip": "Send Back", "dialogTitle": "Send Back", "labelTextField": "Reason", "successMessage": "eConsult(s) processed."}, "pickUp": {"text": "Pick Up", "tooltip": "Pick Up", "dialogTitle": "Pick Up", "confirmationMessage": "Are you sure you want to pick up selected record(s)?"}, "releaseCareplan": {"text": "Release", "tooltip": "Release", "dialogTitle": "Release", "confirmationMessage": "Are you sure you want to release selected record(s)?"}, "createDraft": {"text": "Create Draft", "tooltip": "Create Draft", "dialogTitle": "Continue", "confirmationMessage": "Are you sure you want to create Draft(s) for selected record(s)?"}, "continueToITP": {"text": "Continue to ITP", "tooltip": "Continue to ITP", "dialogTitle": "Continue", "confirmationMessage": "Are you sure you want to continue to ITP selected record(s)?"}, "approve": {"text": "Approve", "tooltip": "Approve", "dialogTitle": "Continue", "confirmationMessage": "Are you sure to want to approve selected record(s)?"}, "assignToITP": {"text": "Manual ITP Assignment", "tooltip": "Manual ITP Assignment"}, "assignToSP": {"text": "Manual SP Assignment", "tooltip": "Manual SP Assignment"}, "autoAssignSp": {"text": "Automatic SP Assignment", "tooltip": "Automatic SP Assignment", "dialogTitle": "Automatic SP Assignment", "confirmationMessage": "Are you sure to want to auto assign SP(s) to selected record(s)?"}, "assignToGPSI": {"text": "Manual GPSI Assignment", "tooltip": "Manual GPSI Assignment"}, "assignToGP": {"text": "Manual GP Assignment", "tooltip": "Manual GP Assignment", "dialogTitle": "Manual GP Assignment", "confirmationMessage": "The eConsults will be reassigned to the requesting physician. Are you sure?"}, "assignToGPSIITP": {"text": "Manual GPSI ITP Assignment", "tooltip": "Manual GPSI ITP Assignment"}, "assignToSPITP": {"text": "Manual SP ITP Assignment", "tooltip": "Manual SP ITP Assignment"}, "changeCustodianGP": {"text": "Change <PERSON><PERSON><PERSON>ian <PERSON>", "tooltip": "Change <PERSON><PERSON><PERSON>ian <PERSON>"}, "reloop": {"text": "Re<PERSON>", "tooltip": "Re<PERSON>", "successMessage": "eConsult(s) re-looped."}, "pdfVersion": {"text": "PDF Version", "tooltip": "PDF Version"}, "doBillOCode": {"text": "Include in O Code billing", "tooltip": "Include in O Code billing", "dialogTitle": "Include in O Code billing", "confirmationMessage": "Are you sure you want to include selected record(s) in O Code billing?"}, "doNotBillOCode": {"text": "Exclude from O Code billing", "tooltip": "Exclude from O Code billing", "dialogTitle": "Exclude from O Code billing", "successMessage": "eConsult(s) excluded from O Code billing."}, "doBillRCode": {"text": "Include in R Code billing", "tooltip": "Include in R Code billing", "dialogTitle": "Include in R Code billing", "confirmationMessage": "Are you sure you want to include selected record(s) in R Code billing?"}, "doNotBillRCode": {"text": "Exclude from R Code billing", "tooltip": "Exclude from R Code billing", "dialogTitle": "Exclude from R Code billing", "successMessage": "eConsult(s) excluded from R Code billing."}, "share": {"text": "Share", "tooltip": "Share"}, "backInPool": {"text": "Back in pool", "tooltip": "Back in pool", "dialogTitle": "Back in pool eConsult(s)", "confirmationMessage": "Are you sure you want to send back in pool selected record(s)?"}, "associateClinicGp": {"text": "Associate Clinic(s) and Gp(s)", "tooltip": "Associate Clinic(s) and Gp(s)", "dialogTitle": "Associate Clinic(s) and Gp(s)", "confirmationMessage": "Are you sure you want to associate the Clinic and the Gp for selected record(s)?", "successMessage": "Clinic(s) and Gp(s) were associated successfully."}, "addClinicToCPS": {"text": "Add Clinic(s) to CPS program", "tooltip": "Add Clinic(s) to CPS program", "dialogTitle": "Add Clinic(s) to CPS program", "confirmationMessage": "Are you sure you want to add the Clinic to CPS program for selected record(s)?"}, "appendTemplate": {"text": "Append Template", "tooltip": "Append Response Template", "dialogTitle": "Copy Response Template"}, "adjustRequestDate": {"text": "Adjust Request Date", "tooltip": "Adjust Request Date", "dialogTitle": "Adjust Request Date", "confirmationMessage": "Are you sure you want to adjust the request Date for selected record(s)?"}, "assignSpecialty": {"text": "Assign <PERSON>ty", "tooltip": "Assign <PERSON>ty", "dialogTitle": "Assign <PERSON>ty"}, "cancel": {"text": "Cancel", "tooltip": "Cancel", "dialogTitle": "Cancel eConsult", "labelTextField": "Cancel Reason", "confirmationMessage": "Are you sure you want to cancel selected record(s)?", "successMessage": "eConsult(s) cancelled"}, "rejectByGP": {"text": "Send Back to GPSI ITP", "tooltip": "Send Back to GPSI ITP", "dialogTitle": "Send Back to GPSI ITP", "labelTextField": "Reason", "successMessage": "eConsult(s) processed"}, "reviewSpResponseByGp": {"text": "<PERSON> Reviewed", "tooltip": "<PERSON> Reviewed", "dialogTitle": "<PERSON> Reviewed", "confirmationMessage": "Are you sure you want to mark the selected record(s) as reviewed response(s)?", "successMessage": "Response(s) reviewed"}, "assignDiagnostic": {"text": "Assign Diagnosis", "toolTip": "Assign Diagnosis", "dialogTitle": "Assign Diagnosis", "successMessage": "eConsult(s) processed"}, "sendBackDispatch": {"text": "Send Back To Dispatch", "tooltip": "Send Back To Dispatch", "dialogTitle": "Send Back To Dispatch", "confirmationMessage": "Are you sure you want to send back to dispatch the selected record(s)?", "successMessage": "eConsult(s) processed"}, "sendBackToItp": {"text": "Send Back To Itp", "tooltip": "Send Back To Itp", "dialogTitle": "Send Back To Itp", "confirmationMessage": "Are you sure you want to send back to <PERSON><PERSON> the selected record?", "successMessage": "eConsult processed"}, "sendBackToSp": {"text": "Send Back To Sp", "tooltip": "Send Back To Sp", "dialogTitle": "Send Back To Sp", "confirmationMessage": "Are you sure you want to send back to Sp the selected record(s)?", "successMessage": "eConsult(s) processed"}, "useCaseNotes": {"text": "Use SP ITP Case Notes", "tooltip": "Use SP ITP Case Notes"}, "useAIgeneratedCaseNotes": {"text": "Use AI generated Case Notes", "tooltip": "Use AI generated Case Notes"}, "changeSpItpByDiagnostic": {"text": "Change SP ITP", "tooltip": "Change SP ITP", "dialogTitle": "Change SP ITP", "confirmationMessage": "Are you sure you want to change the SP ITP for selected record?", "successMessage": "eConsult processed"}, "changeSpByDiagnostic": {"text": "Change SP", "tooltip": "Change SP", "dialogTitle": "Change SP", "confirmationMessage": "Are you sure you want to change the SP for selected record?", "successMessage": "Service Provider has been changed for the eConsult"}, "setConfirmedReview": {"text": "Confirm Diagnostic", "tooltip": "Confirm Diagnostic", "dialogTitle": "Confirm Diagnostic", "confirmationMessage": "Are you sure you want to confirm the diagnostic of the selected record(s)?", "successMessage": "Confirmed Diagnostic(s) Successfully."}, "patientAiSummary": {"text": "Patient AI Summary", "tooltip": "Patient AI Summary"}}, "dialogs": {"note": "eConsult #{{careplanId}}: Notes", "workflowStatus": "eConsult Workflow Status Logs", "status": "eConsult Status Logs", "edit": "Edit eConsult", "add": "Add eConsult", "editNote": "Edit Case Notes", "editReason": "Edit Reason For Consultation", "editRecommendation": "Edit Recommendations", "reloop": "Reloop eConsult", "openEConsult": "Open eConsult"}, "forms": {"reloopLabel": "Reloop action", "reloopReasonMaxLength": "Can not have more than 450 characters", "notesMaxLength": "Can not have more than 450 characters", "ReasonMaxLength": "Can not have more than 450 characters", "notesNewCareplanLabel": "Add notes to the new eConsult", "excludeOCodeBillingLabel": "Exclude new eConsult from O Code billing", "excludeRCodeBillingLabel": "Exclude new eConsult from R Code billing", "notesNewType": {"label": "Type", "required": "Note's type"}, "notesNew": {"label": "Notes", "placeholder": "Notes", "required": "Note", "maxLength": "Notes can not have more than 450 characters"}, "diagnostic1Label": "Diagnosis 1", "reviewSpResponseByGp": {"uploadResponseToEMRLabel": "Upload Response To EMR"}, "sendBackToItp": {"assignSameSpLabel": "Send back to same Itp", "resetAssignedSpLabel": "Auto assign to Different Itp", "selectSpLabel": "Select different Itp", "rCodeWarning": "Some of the eConsults selected to be sent to the SP ITP have already been billed. Do you want to bill them again?"}, "sendBackToSp": {"assignSameSpLabel": "Send back to same SP", "resetAssignedSpLabel": "Auto assign to Different SP", "selectSpLabel": "Select different SP"}, "confirmDiagnostic": {"sendBackDispatch": "Send Back To Dispatch"}, "assignDiagnostic": {"sendBackDispatch": "Send Back To Dispatch"}}, "tabs": {"gp": {"available": "Available", "upcoming": "Upcoming", "spResponses": "SP Responses", "unanswered": "Unanswered", "mine": "Mine"}, "sp": {"available": "Available", "upcoming": "Upcoming", "mine": "Mine"}, "itp": {"all": "All", "attention": "Attention", "cps": "CPS", "available": "Available", "mine": "Mine"}}, "summarizationSuffix": "(Summarization)", "gpReviewSuffix": "(Gp Review)"}, "hpu": {"title": "HPUs", "label": "HPU", "details": {"title": "Details", "tabs": {"detail": "Details", "patient": "Patient Information", "condition": "Conditions", "medication": "Medications", "allergy": "Allergies", "socialHistory": "Social Histories", "vitalSign": "Vital Signs", "attachment": "Attachments", "hPUCareplan": "HPU eConsults", "note": "Notes", "workflowStatus": "Workflow Status Logs"}}, "tooltips": {"lock": "Locked by", "unLock": "Unlocked"}, "dialogs": {"note": "HPU Notes", "workflowStatus": "HPU Workflow Status Logs", "status": "HPU Status Logs", "edit": "Edit HPU", "add": "Add HPU"}}, "clinicalQuestion": {"title": "eConsult", "dialogs": {"edit": "Edit eConsult", "add": "Create eConsult", "note": "eConsults Notes", "reAsk": "Re-ask eConsult"}, "forms": {"questionsList": "Question", "addNewQuestion": "Add New Question", "other": "Other", "conditionFreeText": {"label": "Health Focus Free Text", "placeholder": "Health Focus Free Text", "maxLength": "Health Focus can not have more than 500 characters"}}, "actions": {"add": {"confirmation": {"title": "Confirm action", "messages": {"hpcMvca": "This eConsult Request will be sent the patient's Custodian GP for review.", "gp": "eConsult(s) successfully created. eConsult(s) will be sent to the Specialist for recommendations. Once you click 'OK' you are no longer able to edit the eConsult(s)."}}}, "reAskClinicalQuestion": {"text": "Re-ask eConsult", "tooltip": "Re-ask eConsult", "dialogTitle": "Re-ask eConsult", "successMessage": "Re-asked successfully"}, "closeClinicalQuestion": {"text": "Close eConsult", "tooltip": "Close eConsult", "labelTextField": "Close Reason", "dialogTitle": "Close eConsult", "successMessage": "Closed successfully"}, "addClinicalQuestion": {"text": "Add eConsult", "tooltip": "Add eConsult", "success": "eConsult Added"}, "approveClinicalQuestion": {"text": "Approve eConsult", "tooltip": {"message_one": "Approve eConsult", "message_other": "Approve Selected eConsults"}, "title": "Approve eConsult", "confirmationMessage": "Are you sure you want to approve selected eConsult(s)?"}, "declineClinicalQuestion": {"text": "Decline eConsult", "tooltip": {"message_one": "Decline eConsult", "message_other": "Decline Selected eConsults"}, "dialogTitle": "Decline eConsult", "confirmationMessage": "Are you sure you want to decline selected record(s)?", "successMessage": "eConsult(s) processed."}, "dispatchClinicalQuestion": {"text": "Dispatch eConsult", "tooltip": "Dispatch eConsult", "title": "Dispatch eConsult", "confirmationMessage": "Are you sure you want to dispatch selected record(s)?", "successMessage": "Dispatch Question(s) processed."}}}, "inquiryFormStep": {"title": "Inquiry Form Steps", "label": "Inquiry Form Step", "details": {"title": "Details", "tabs": {"detail": "Details", "question": "Questions"}}, "buttons": {"add": "Add Inquiry Form Step"}, "actions": {"uploadImage": {"dropzoneText": "Drag and drop image(s) here or click."}, "reassign": {"text": "Reassign to Inquiry form", "tooltip": "Reassign to Inquiry form", "dialogTitle": "Reassign to Inquiry form", "successMessage": "item(s) reassigned."}}, "dialogs": {"edit": "Edit Inquiry Form Step", "add": "Add Inquiry Form Step"}, "forms": {"uploadImage": "Upload Image"}}, "fax": {"title": "Faxes", "tabs": {"outgoing": "Outgoing", "incoming": "Incoming"}, "actions": {"generateUploadFaxPdf": {"text": "PDF version", "tooltip": "PDF version"}, "changeAssignedQty": {"text": "Actions", "tooltip": "Change assigned Quantity", "dialogTitle": "Change Assigned Qty", "successMessage": "Faxes Auto Assignment Plan has been successfully updated."}, "process": {"text": "Automatic Processing", "tooltip": "Automatic Processing", "dialogTitle": "Automatic Processing", "confirmationMessage": "Are you sure you want to send to automatic processing selected record(s)?"}, "import": {"text": "Generate eConsult", "tooltip": "Generate eConsult", "dialogTitle": "Add Fax", "confirmationMessage": "Are you sure you want to generate eConsult(s) from selected record(s)?", "success": {"message_one": "Fax successfully added.", "message_other": "Faxes successfully added."}, "error": {"message_one": "Error adding fax:", "message_other": "Error adding faxes:"}, "dropzone": {"text": "Drag and drop fax(es) file(s) here or click.", "addedMessage": "Is ready to be added.", "previewText": "Files:", "helperText": "Files are required"}}, "generate": {"text": "Generate eConsult", "tooltip": "Generate eConsult", "dialogTitle": "Generate eConsult(s)", "confirmationMessage": "Are you sure you want to generate eConsult(s) from selected record(s)?", "successMessage": "eConsult(s) generated."}, "markAsProcessed": {"text": "<PERSON> as Processed", "tooltip": "<PERSON> as Processed", "dialogTitle": "<PERSON>ed", "confirmationMessage": "Are you sure you want to mark as processed selected record(s)?"}, "sendToEMR": {"text": "Send To EMR", "tooltip": "Send To EMR", "dialogTitle": "Send To EMR", "confirmationMessage": "Are you sure you want to send selected record(s)?", "noProcessMessage": "Any Fax could be sent to EMR."}, "sendBack": {"text": "Send Back", "tooltip": "Send Back", "dialogTitle": "Send Back", "labelTextField": "Reason", "successMessage": "Fax(es) processed."}, "executePlan": {"dialogTitle": "Execute Plan", "confirmationMessage": "Are you sure you want to execute the plan?", "successMessage": "Faxes Auto Assignment Plan has been successfully executed."}, "cancelPlan": {"dialogTitle": "Cancel Plan", "confirmationMessage": "Are you sure you want to cancel the plan?", "successMessage": "Faxes Auto Assignment Plan has been successfully cancelled."}, "assignTranscriber": {"text": "Assign to Transcriber", "tooltip": "Assign to Transcriber", "dialogTitle": "Assign to Transcriber", "success": {"message_one": "Item processed.", "message_other": "Items processed."}}, "assignGroup": {"text": "Assign to Group", "tooltip": "Assign to Group", "dialogTitle": "Assign to Transcribers Group"}, "cancel": {"text": "Cancel", "tooltip": "Cancel", "dialogTitle": "Cancel Fax", "labelTextField": "Cancel Reason", "confirmationMessage": "Are you sure you want to cancel selected record(s)?", "successMessage": "Fax(s) cancelled"}}, "tooltips": {"responseBack": "Yes", "katanaProcessed": "Yes"}, "details": {"title": "Details", "tabs": {"detail": "Details", "attachment": "Attachments", "note": "Notes", "workflowStatus": "Workflow Status Logs"}}, "dialogs": {"status": "Fax Status Logs", "note": "Fax notes", "workflowStatus": "Fax Workflow Status Logs", "edit": "Edit Fax", "add": "Add Fax"}, "forms": {"autoAssign": {"label": "Assigned Qty", "placeholder": "Assigned Qty", "required": "Assigned quantity"}}, "buttons": {"executePlan": "Execute Plan", "cancelPlan": "Cancel Plan", "assign": "Assign", "imports": "Imports"}, "autoAssign": {"titles": {"detail": "Details", "group": "Groups Summary", "assignment": "Assignment Plan"}}, "errors": "Invalid use of faxes screen"}, "invoiceItem": {"title": "Invoice Items", "details": "Details", "actions": {"resubmitInvoiceItem": {"title": "Resubmit Invoice Item", "text": "Resubmit", "tooltip": "Resubmit", "dialogTitle": "Update claim number", "successMessage": "Invoice Item has been updated successfully."}, "include": {"text": "Include", "tooltip": "Include in current billing", "dialogTitle": "Include in current billing", "successMessage": "Invoice item(s) have been included in current billing."}, "exclude": {"text": "Exclude", "tooltip": "Exclude from current billing", "dialogTitle": "Exclude from current billing", "successMessage": "Invoice item(s) have been excluded from current billing."}, "check": {"text": "Check Errors", "tooltip": "Check Errors", "dialogTitle": "Errors", "confirmationMessage": "Are you sure you want to check errors from selected record(s)?"}, "ignore": {"text": "Ignore Errors", "tooltip": "Ignore Errors", "dialogTitle": "Errors", "confirmationMessage": "Are you sure you want to ignore errors from selected record(s)?"}, "adjustRequestDate": {"text": "Adjust Request Date", "tooltip": "Adjust Request Date", "dialogTitle": "Adjust Request Date for Billing", "confirmationMessage": "Are you sure you want to adjust eConsult request date?", "successMessage": "Records(s) Processed."}, "adjustResponseDate": {"text": "Adjust Response Date", "tooltip": "Adjust Response Date", "dialogTitle": "Adjust Response Date for Billing", "confirmationMessage": "", "successMessage": "Records(s) Processed."}, "externalBilling": {"text": "External Billing", "tooltip": "External Billing", "dialogTitle": "External Billing", "confirmationMessage": "Are you sure you want to set as external billing selected record(s)?", "successMessage": "Item(s) Updated."}, "clinicAidBilling": {"text": "Clinic Aid billing", "tooltip": "Clinic Aid billing", "dialogTitle": "Clinic Aid billing", "confirmationMessage": "Are you sure you want to set as Clinic Aid billing selected record(s)?", "successMessage": "Item(s) Updated."}, "associateBillingSetting": {"text": "Associate Billing <PERSON>ting", "tooltip": "Associate Billing <PERSON>ting", "dialogTitle": "Associate Billing <PERSON>ting", "confirmationMessage": "Are you sure you want to associate <PERSON><PERSON> selected record(s)?", "successMessage": "Item(s) Updated."}}, "dialogs": {"note": "Invoice Item Notes", "edit": "Edit Invoice Item"}, "buttons": {"export": "Export"}, "column": {"phn": {"filterLabel": "or Address"}, "expired": {"text": "{{value}} ({{expiredCount}} day(s))", "toolTip": "The eConsult has {{expiredCount}} day(s) of expiration."}, "ignoreErrors": {"toolTip": "Yes"}, "check": {"text": "Check Errors", "tooltip": "Check Errors", "dialogTitle": "Errors", "confirmationMessage": "Are you sure you want to check errors from selected record(s)?"}, "ignore": {"text": "Ignore Errors", "tooltip": "Ignore Errors", "dialogTitle": "Errors", "confirmationMessage": "Are you sure you want to ignore errors from selected record(s)?"}, "adjustRequestDate": {"text": "Adjust Request Date", "tooltip": "Adjust Request Date", "dialogTitle": "Adjust Request Date", "confirmationMessage": "Are you sure you want to adjust eConsult request date?"}}, "placeholderNote": "eConsult note"}, "transcriber": {"title": "Transcribers", "dialogs": {"status": "Transcriber Status Logs", "edit": "Edit Transcriber", "add": "Add Transcriber", "assign": "Assign Transcriber"}, "buttons": {"add": "Add Transcriber"}}, "transcriberManager": {"title": "Transcribers Managers", "dialogs": {"status": "Transcriber Managers Status Logs", "edit": "Edit Transcriber Manager", "add": "Add Transcriber Manager"}, "buttons": {"add": "Add Transcriber Manager"}}, "transcriberGroup": {"title": "Transcriber Groups", "actions": {"configure": {"text": "Configure Group", "tooltip": "Configure Group", "dialogTitle": "Pick Up", "confirmationMessage": "Are you sure you want to pick up selected record(s)?"}}, "dialogs": {"status": "Transcriber Group Status Logs", "edit": "Edit Transcribers Group", "add": "Add Transcribers Group"}, "forms": {"autocompleteError": "Enter name. Min search characters length"}, "buttons": {"add": "Add Group"}}, "transcribersGroupConfig": {"title": "Configurations", "details": {"title": "Details"}, "dialogs": {"edit": "Edit Group Configuration", "add": "Add Group Configuration"}, "buttons": {"add": "Add Configuration"}}, "province": {"title": "Provinces", "dialogs": {"edit": "Edit Province", "add": "Add Province"}, "buttons": {"add": "Add Province"}, "forms": {"countryError": "Select Country first"}}, "inquiryForm": {"title": "Inquiry Forms", "detailsTitle": "Inquiry Form", "actions": {"new": "New Form", "edit": "Edit Form"}, "dialogs": {"edit": "Edit Inquiry Form", "add": "Add Inquiry Form"}, "buttons": {"add": "Add Inquiry Form"}}, "inquiryFormQuestion": {"title": "Inquiry Form Questions", "buttons": {"new": "Add Inquiry Form Question"}, "actions": {"reassign": {"text": "Reassign to Step", "tooltip": "Reassign to Step", "dialogTitle": "Reassign to Step", "successMessage": "Item(s) Reassign."}}, "tooltips": {}, "dialogs": {"edit": "Edit Inquiry Form Question", "add": "Add Inquiry Form Question"}, "forms": {"editInquiryFormQuestionForm": {"questionValuesInfo": "Question values ​​must be separated by commas."}}}, "deadLetterMessages": {"title": "Dead Letter Messages", "actions": {"edit": "Edit Message", "requeue": {"text": "Re Queue", "tooltip": "Re queue message", "dialogTitle": "Re queue messages", "confirmationMessage": "Are you sure you want to re queue selected record(s)?", "successMessage": "Dead Message(s) have been queued."}}, "dialogs": {"edit": "Edit Message"}}, "userInquiryForm": {"actions": {"acceptInquiryForm": {"text": "Accept", "tooltip": "Accept", "dialogTitle": "Accept Inquiry Form", "confirmationMessage": "Are you sure you want to accept selected record(s)?"}, "acceptInquiryFormAnswer": {"text": "Accept", "tooltip": "Accept", "dialogTitle": "Accept Inquiry Form Answer", "confirmationMessage": "Are you sure you want to accept selected record(s)?"}}}, "educationalCareplan": {"title": "Educational Care Plans", "details": {"title": "Details", "tabs": {"detail": "Details", "attachment": "Attachments"}}, "dialogs": {"edit": "Edit Educational eConsult", "add": "Add Educational Careplan"}, "buttons": {"add": "Add Educational eConsult"}}, "diagnosis": {"title": "Diagnosis", "details": {"title": "Details", "tabs": {"detail": "Details", "practitioners": "Practitioners", "educationalCareplan": "Educational Careplan", "alias": "<PERSON><PERSON>"}}, "actions": {"configureAlias": {"text": "Configure <PERSON>", "tooltip": "Configure <PERSON>"}, "convertToAlias": {"text": "Convert to <PERSON><PERSON>", "tooltip": "Convert to <PERSON><PERSON>", "dialogTitle": "Convert Diagnosis to <PERSON><PERSON>", "success": {"message_one": "Diagnosis converted.", "message_other": "Diagnosis's converted."}}, "assignEducationalCareplan": {"text": "Assign Educational eConsult", "tooltip": "Assign Educational eConsult", "dialogTitle": "Assign Educational eConsult", "success": {"message_one": "Item assigned.", "message_other": "Items assigned."}}, "setDispatchReview": {"text": "Set Dispatch Review", "tooltip": "Set Dispatch Review", "dialogTitle": "Set Dispatch Review", "confirmationMessage": "Are you sure you want to set the 'Dispatch Review' to the selected record(s)?", "successMessage": "'Dispatch Review' set Successfully."}, "removeDispatchReview": {"text": "Unset Dispatch Review", "tooltip": "Unset Dispatch Review", "dialogTitle": "Unset Dispatch Review", "confirmationMessage": "Are you sure you want to unset the 'Dispatch Review' from the selected record(s)?", "successMessage": "'Dispatch Review' unset Successfully"}}, "dialogs": {"edit": "Edit Diagnosis", "add": "Add Diagnosis", "status": "Diagnosis Status Logs"}, "buttons": {"add": "Add Diagnosis"}, "autocomplete": {"educationalCareplan": "<PERSON><PERSON><PERSON>"}}, "billingSetting": {"title": "Billing <PERSON>s", "dialogs": {"edit": "Edit Billing <PERSON>ting", "add": "Add Billing Setting", "associate": {"text": "Associate Billing <PERSON>ting", "success": {"message_one": "Billing Setting associated.", "message_other": "Billing Setting associated."}}}, "buttons": {"add": "Add Billing Setting"}}, "report": {"title": "Reporting", "link": {"referrals": "Referrals Reports", "npMetrics": "NP Metrics", "eConsultMetrics": "E-Consult Metrics", "vaMetrics": "Virtual Assistants Metrics", "cqMetrics": "Clinical Questions Metrics"}}, "user": {"title": "Users", "actions": {"manageGroup": {"text": "Manage Groups", "tooltip": "Manage Groups"}}, "dialogs": {"status": "User Status Logs", "edit": "Edit User", "add": "Add User"}, "buttons": {"add": "Add User"}}, "groupMember": {"title": {"members": "Members", "groups": "Groups"}, "details": "<PERSON><PERSON>", "dialogs": {"edit": "Edit Group Member", "add": "Add Group Member"}, "actions": {"delete": {"text": "Delete", "tooltip": "Delete Member from Group"}, "restore": {"text": "Rest<PERSON>", "tooltip": "Restore Member to Group"}}}, "groupRole": {"title": {"roles": "Roles", "groups": "Groups"}, "dialogs": {"edit": "Edit Group Role", "add": "Add Group Role"}, "actions": {"delete": {"text": "Delete", "tooltip": "Delete Role from Group"}, "restore": {"text": "Rest<PERSON>", "tooltip": "Restore Role to Group"}}}, "group": {"title": "User Groups", "actions": {"view": {"text": "View", "tooltip": "Group details page"}}, "dialogs": {"edit": "Edit Group", "add": "Add Group"}, "buttons": {"add": "Add Group"}, "details": {"title": "Details", "tabs": {"members": "Members", "roles": "Roles"}}}, "role": {"title": "Roles", "dialogs": {"edit": "Edit Role", "add": "Add Role"}, "buttons": {"add": "Add Role"}}, "notificationTemplate": {"title": "Notification Templates", "dialogs": {"edit": "Edit Notification Template", "add": "Add Notification Template"}, "actionError": "Select Profile first", "tag": {"text": "Click following tags to use them in the template", "tooltip": "Tag", "startMessage": "Tag", "endMessage": "Has been copied to clipboard, now you can paste it in Template input."}, "attachmentTag": {"text": "Click following attachment tags to use them in the template", "tooltip": "Attachment tag"}, "buttons": {"add": "Add Notification Template"}, "forms": {"notificationActionError": "Select notification profile first", "notificationRecipientsError": "Select notification action first", "notificationNameError": "Notification name is required"}}, "practitioner": {"title": "Practitioners", "assignedPractitioners": "Assigned Practitioners", "details": {"title": "Details", "tabs": {"detail": "Details", "specialties": "Specialties", "diagnostics": "Billing Codes", "alias": "<PERSON><PERSON>", "assignedPractitioners": "Assigned Practitioners"}}, "action": {"assignPractitioner": {"text": "Assign Practitioners", "tooltip": "Assign Practitioners"}, "assignSpecialties": {"text": "Manage Specialties", "tooltip": "Manage Specialties"}, "assignPractitioners": {"text": "Manage Practitioners", "tooltip": "Manage Practitioners"}, "assignDiagnosticCodes": {"text": "Assign Diagnostic Codes", "tooltip": "Assign Diagnostic Codes"}, "toggleOptOutInitialReview": {"text": {"toggle_on": "Opt Out Initial Review", "toggle_off": "Opt In Initial Review"}, "tooltip": {"toggle_on": "Opt Out Initial Review", "toggle_off": "Opt In Initial Review"}, "dialogTitle": " Opt Out/In Initial Review", "confirmationMessage": "Are you sure you want to proceed?"}, "sendPatientsToPrePaneling": {"text": "Send Panel to Pre Paneling", "tooltip": "Send Panel to Pre Paneling", "confirmationMessage": "Are you sure you want to proceed?", "dialogTitle": "Send Panel to Pre Paneling", "processingMessage": "Send Panel to Pre Paneling successfully queued."}}, "dialogs": {"note": "Practitioner Notes", "status": "Practitioner Status Logs", "edit": "<PERSON>", "add": "Add Practitioner", "assign": {"text": "Assign <PERSON>er", "success": {"message_one": "Practitioner assigned.", "message_other": "Practitioners assigned."}}, "addPractitionerAssignedPractitioner": "Add Practitioner Assignment", "editPractitionerAssignedPractitioner": "Edit Practitioner Assignment", "assignPractitioner": {"text": "Assign <PERSON>er", "success": {"message_one": "Practitioner assigned.", "message_other": "Practitioners assigned."}}, "changeSettings": {"title": "Practitioner Settings", "success": "Practitioner Updated"}, "deleteSpecialties": {"title": "Delete", "note": "Note", "removeCodes": "Remove Associated ICD-9 Codes from Practitioner's \"Billing Codes\""}}, "buttons": {"add": "Add Practitioner"}}, "clinic": {"title": "Clinics", "actions": {"assignPractitioner": {"text": "Manage Practitioners", "tooltip": "Manage Practitioners"}, "toggleReadyToOnboarding": {"text": {"toggleOn": "Enable Synchronization", "toggleOff": "Disable Synchronization"}, "tooltip": {"toggleOn": "Enable Synchronization", "toggleOff": "Disable Synchronization"}, "dialogTitle": "Enable/Disable Synchronization", "confirmationMessage": "Are you sure you want to proceed?"}, "selectIntegratorType": {"text": "Change Integration Type", "tooltip": "Change Integration Type"}}, "dialogs": {"note": "Clinic Notes", "status": "Clinic Status Logs", "edit": "Edit Clinic", "add": "Add Clinic", "addClinicPractitioner": "Assign <PERSON>er", "editClinicPractitioner": "Edit Practitioner Assignment", "selectIntegratorType": "Select Integration Type"}, "buttons": {"add": "Add Clinic"}, "form": {"cps": "Participates in the CPS Program"}, "details": {"title": "Details", "tabs": {"details": "Details", "practitioners": "Practitioners"}}}, "importEvents": {"title": "Imported Data", "tableTitle": "Imported Data", "tableTitleFax": "Faxes Import", "tableTitleInvoiceItem": "Invoice Items Import", "tableTitleCareplan": "eConsults Import", "sourceName": "File Name", "updatedBy": "Updated by", "importDate": "Import Date", "entityDisplayName": "Entity", "profileName": "Profile", "totalItems": "Total", "unchangedItems": "Unchanged", "addedItems": "Added", "updatedItems": "Updated", "missedItems": "Missed", "failedItems": "Failed", "download": "Download imported file", "actions": {"retry": {"text": "Retry", "tooltip": "Retry", "dialogTitle": "Retry", "confirmationMessage": "Are you sure you want to retry this import?", "success": {"message_one": "Import re-tried.", "message_other": "{{count}} import(s) re-tried."}}, "fixConflict": {"text": "Retry", "tooltip": "Retry", "dialogTitle": "Retry", "successMessage": " Conflict resolved successfully."}, "import": {"text": "Retry", "tooltip": "Retry", "dialogTitle": "Import File", "successMessage": "{{fileName}} successfully imported", "error": {"missing": "Missing file. Please add the file to import", "duplicated": "'{{fileName}}' has been previously imported. Use the 'override' checkbox in the Import Dialog if this is intended.", "import": "Error importing '{{selectedFileName}}': {{message}}"}}}, "form": {"profile": "Profile", "dropZone": {"label": "Drag and drop an Excel file here or click", "added": "{{fileName}} is ready to be imported", "preview": "Files", "overrideExisting": "Override previously imported data"}}}, "deferredMerge": {"title": "<PERSON><PERSON><PERSON>", "action": {"revert": {"text": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON>", "dialogTitle": "Confirm revert", "successMessage": "<PERSON><PERSON> has been queued successfully", "confirmationMessage": "Are you sure you want to revert selected operation?"}}}, "bulkValidation": {"title": "Bulk Validations Requests", "buttons": {"add": "Add Request Bulk Validation"}, "dialogs": {"add": "Request Bulk Validation", "edit": "Edit Bulk Validation Request"}}, "deferredUserInvites": {"title": "Invite Users"}, "export": {"title": "Export Data"}, "import": {"title": "Import Data"}, "importEventDetail": {"title": "Events", "details": "Details", "waitingStatus": "Waiting for Resolution", "action": {"label": "Actions", "errorResolution": {"text": "Fix", "tooltip": "Fix", "dialogTitle": "Error Resolution", "successMessage": "Conflict resolved successfully."}, "retry": {"text": "Retry import", "successMessage": "Item imported successfully."}}}, "deferredRequest": {"title": "Other Tasks", "tabs": {"crons": "Crons"}}, "clinicPractitioner": {"title": "Practitioners", "details": "Details", "actions": {"emrPatients": {"text": "EMR Patients", "tooltip": "EMR Patients"}, "uploadPatientsPanelFile": {"text": "Import Patients", "tooltip": "Import Patients from Panel File"}, "toggleResponseBackCp": {"dialogTitle": "Modify Response Back", "text": "Modify Response Back", "tooltip": "Modify Response Back", "successMessage": "Response Back has been set successfully", "confirmationMessage": "Enabling this setting will automatically send all newly completed eConsults to the clinic."}}, "forms": {"toggleResponseBackCp": {"sendEConsultsToEMR": "Do you like to send all historical eConsults ({{countSendBack}}) to the EMR?"}}}, "practitionerSpecialty": {"title": "Specialties", "details": "Details"}, "practitionerDiagnostics": {"title": "Diagnostics", "details": "Details"}, "specialtyPractitioners": {"title": "Practitioners", "details": "Details"}, "specialtyDiagnostics": {"title": "Diagnostics", "details": "Details"}, "specialty": {"title": "Specialties", "details": {"title": "Details", "tabs": {"practitioners": "Practitioners", "diagnostics": "Billing Codes"}}, "dialogs": {"status": "Specialty Status Logs", "edit": "Edit Specialty", "add": "Add Specialty"}, "action": {"assignDiagnosticCodes": {"text": "Assign Diagnostic Codes", "tooltip": "Assign Diagnostic Codes"}}, "buttons": {"add": "Add Specialty"}}, "country": {"title": "Countries", "dialogs": {"edit": "Edit Country", "add": "Add Country"}, "buttons": {"add": "Add Country"}}, "authorizedUser": {"title": "Manage Authorized Users", "details": "{{impersonator}} will be able to login as {{impersonation}} until the Expire At is reached or the permission is revoked.", "dialogs": {"edit": "Edit Authorized Users", "add": "Add Authorized Users"}, "buttons": {"add": "Add Authorized User"}, "form": {"columns": {"impersonation": "On Behalf Of", "impersonator": "Authorized User"}, "alerts": {"addEditAccount": "{{impersonator}} will be able to login as {{impersonation}} until the Expire At is reached or the permission is revoked.", "accountAccess": "{{impersonator}} will be able to login as {{impersonation}} until the Expire At is reached or the permission is revoked.", "delegateAccount": "{{impersonator}} will be able to login as all selected users until the Expire At is reached or the permission is revoked.", "grantAccess": "All selected users will be able to login as {{impersonation}} until the Expire At is reached or the permission is revoked."}}, "actions": {"grant": {"text": "Grant Access", "tooltip": "Grant Access", "dialogTitle": "Grant Access"}, "delegate": {"text": "Add Authorized User", "tooltip": "Add Authorized User", "dialogTitle": "Add Authorized User"}}}, "referral": {"title": "Referrals", "details": {"title": "Details", "tabs": {"detail": "Details", "patient": "Patient Information", "condition": "Conditions", "medication": "Medications", "allergy": "Allergies", "socialHistory": "Social Histories", "vitalSign": "Vital Signs", "attachment": "Attachments", "note": "Notes", "workflowStatus": "Workflow Status Logs"}}, "dialogs": {"note": "Referral Notes", "edit": "Edit Referral", "add": "Add Referral"}, "buttons": {"add": "Add Referral"}}, "specialist": {"title": "Referral Specialists", "details": "Details", "dialogs": {"note": "Specialist Notes", "edit": "Edit Referral Specialist", "add": "Add Referral Specialist"}, "buttons": {"add": "Add Specialist"}}, "npDailyMetric": {"title": "NP Daily Metrics", "dialogs": {"edit": "Edit NP Daily Metric", "add": "Add NP Daily Metric"}, "buttons": {"add": "Add NP Daily Metric"}}, "eccDailyProcessedMetric": {"title": "ECC Daily Metrics", "dialogs": {"edit": "Edit ECC Daily Processed Metric", "add": "Add ECC Daily Processed Metric"}, "buttons": {"add": "Add ECC Daily Metric"}}, "patientPaneling": {"dialogs": {"startPaneling": {"dialogTitle": "Start Paneling", "confirmationMessage": "You are about to start paneling, are you sure to continue?"}}, "actions": {"startPaneling": {"text": "Get Next Patient", "tooltip": "Get Next Patient", "successMessage": "Patient fetched successful"}}, "title": "Patients", "tabs": {"all": "All", "locked": "Locked", "available": "Available", "completed": "Completed", "allCompleted": "All Completed", "upcomingHPCPatients": "Upcoming", "upcomingMVCAPatients": "Upcoming", "upcomingHPCLeadPatients": "Upcoming"}}, "patientPoolItem": {"details": {"title": "eConsults Request", "titleWithName": "eConsults Request for {{name}}", "tabs": {"details": "Demographics", "clinicalQuestions": "eConsults", "attachments": "Attachments", "notes": "Notes", "workflowStatus": "Request Status Logs"}}, "workflowStatus": {"title": "Request Status", "InPool": "Available for Review", "Started": "In Quality Review", "PendingAIQuestions": "Generating eConsults", "PendingOCR": "Scanning Documents", "PendingPool": "Request Queued", "InReview": "In Review", "Completed": "Completed", "Cancelled": "Cancelled", "Failed": "Failed", "PatientUpdated": "PatientUpdated"}, "actions": {"sendBackToGp": {"text": "Send Back Gp", "tooltip": "Send Back Gp", "title": "Send Back Gp", "reason": "Reason", "labelTextField": "Send Back Reason", "dialogTitle": "Send Back to Gp", "successMessage": "Request(s) sent back successfully"}, "sendBackToHpc": {"text": "Send Back CH Nurse", "tooltip": "Send Back CH Nurse", "title": "Send Back CH Nurse", "reason": "Reason", "labelTextField": "Send Back Reason", "dialogTitle": "Send Back to CH Nurse", "successMessage": "Request(s) sent back successfully"}, "moveToOcrPending": {"text": "Move to Ocr Pending", "tooltip": "Move to Ocr Pending", "title": "Move to Ocr Pending", "dialogTitle": "Move request to Ocr Pending", "successMessage": "Request moved to Ocr Pending successfully", "confirmationMessage": "Are you sure you want to move to Ocr Pending?"}, "ocrDocuments": {"text": "Ocr Documents", "tooltip": "Ocr Documents", "title": "Ocr Documents", "dialogTitle": "Ocr Documents", "successMessage": "Ocr process finished successfully", "confirmationMessage": "Are you sure you want to Ocr Documents?"}, "generateAIQuestions": {"text": "Generate Questions", "tooltip": "Generate Questions", "title": "Generate Questions", "dialogTitle": "Generate Questions", "successMessage": "Questions were generated successfully", "confirmationMessage": "Are you sure you want to Generate Questions?"}, "getNextPanelingPatients": {"text": "Get next patient", "tooltip": "Get next patient", "title": "Get next patient", "dialogTitle": "Get next patient", "successMessage": "Patients were taken successfully", "confirmationMessage": "Are you sure you want get patients?"}}}, "patient": {"title": "Patients", "patientPanelingTab": {"title": "Pending Approval"}, "UpcomingGP": {"title": "Upcoming"}, "observation": "Observation", "noObservations": "No observations", "daysInPending": "Days Pending", "daysInReview": "Days In Review", "details": {"title": "Demographics", "generatingEConsults": "Generating eConsults", "tabs": {"details": "Demographics", "medications": "Medications", "conditions": "Conditions", "socialHistories": "Social Histories", "vitalSigns": "Vital Signs", "allergies": "Allergies", "clinicalQuestions": "eConsults", "attachments": "Attachments", "emrDirectory": "Lab Attachments", "generatedLetters": "Generated Letters", "goals": "Goals", "notes": "Notes", "flags": "Chart Flags", "inquiryForms": "Inquiry Forms", "aliases": "Aliases", "familyMembers": "Family Members"}}, "detailSummary": {"title": "AI Summary", "aiSummaryDate": "Summary Date"}, "dialogs": {"pharmacyDialog": "Patient Pharmacy Contact", "editPatient": "<PERSON>", "addPatient": "Add Patient", "assignSocialHistory": "Assign Social History", "editSocialHistory": "Edit Patient Social History", "AssignVitalSign": "Assign Vital Sign", "editPatientAllergy": "Edit <PERSON>ient Allergy", "assignPatientAllergy": "Assign <PERSON><PERSON>", "editMedications": "Edit Patient Medications", "editDiagnoses": "<PERSON> Patient Diagnoses", "assignDiagnostic": "Assign Diagnosis", "addPatientGoal": "Add Patient Goals", "editPatientGoal": "<PERSON>", "assignHousehold": "Assign Member to Household", "editHousehold": "Edit Household's member"}, "actions": {"invite": {"text": "Invite", "tooltip": "Invite", "title": "<PERSON><PERSON><PERSON>", "confirmationMessage": "Are you sure you want to invite selected record(s)?"}, "assignGP": {"text": "Assign <PERSON><PERSON><PERSON><PERSON>", "tooltip": "Assign <PERSON><PERSON><PERSON><PERSON>"}, "finalizePaneling": {"text": "Finalize Paneling", "tooltip": "Finalize Paneling", "title": "Finalize Paneling", "gpConfirmationMessage": "Are you sure you want to finish the paneling with the questions in their current state?", "gpConfirmationMessageWithGpPending": "This patient still has questions that require your review and approval. Questions left in “Pending” status will not be dispatched. Would you like to proceed?"}, "sendAll": {"text": "Send All", "tooltip": "Send All", "title": "Send All", "hpcConfirmationMessage": "You are about to send all eConsults in 'Proposed' status to the patient's GP for review.", "successMessage": "Finish the patient paneling successfully"}, "cancelRequest": {"text": "Cancel Request", "tooltip": "Cancel Request", "title": "Cancel Request", "confirmationMessage": "This eConsult Request(s) will be archived and will not be retrievable.", "successMessage": "eConsult Request(s) cancelled successfully"}, "acceptPrepanelingPatient": {"text": "Chart Updated", "tooltip": "Chart Updated", "title": "Chart Updated", "confirmationMessage": "Are you sure want to complete the chart of the selected patient(s)?"}, "requestChartUpdate": {"text": "Request Chart Update", "tooltip": "Request Chart Update", "title": "Request Chart Update", "confirmationMessage": "Are you sure want to request the chart update for the selected patient(s)?"}, "sendCalendar": {"text": "Send Calendar", "tooltip": "Send Calendar", "dialogTitle": "Send Calendar", "confirmationMessage": "Are you sure you want to send calendar to selected patient(s)?"}, "markAsIneligible": {"text": "<PERSON> As Ineligible", "tooltip": "<PERSON> As Ineligible", "title": "<PERSON> As Ineligible", "reason": "Reason", "labelTextField": "Ineligible Reason", "dialogTitle": "<PERSON> As Ineligible", "confirmationMessage": "Are you sure you want to mark as ineligible selected record(s)?", "success": {"message_one": "<PERSON><PERSON> marked as ineligible successfully", "message_other": "{{count}} Patients marked as ineligible successfully"}}, "toggleEmailCommunication": {"enable": {"text": "Enable Email Communication", "tooltip": "Enable Email Communication", "successMessage": "Enable email notification processed"}, "disable": {"text": "Disable Email Communication", "tooltip": "Disable Email Communication", "successMessage": "Disable email notification processed"}, "dialogTitle": "Enable/Disable Email Communication", "confirmationMessage": "Are you sure you want to adjust the selected records?"}, "openAiSummary": {"title": "Open Summary"}, "askQuestion": {"text": "Request eConsults", "dialog": {"title": "Request eConsults", "subtitle": "Please select at least one document for this request for eConsults. The selected document(s) will be considered by the AI when generating eConsults."}, "tooltip": "Request eConsults", "successMessage": "The attachments were successfully selected and sent"}, "addAttachments": {"text": "Add Attachments", "tooltip": "Add Attachments", "dialog": {"invalidDate": "Invalid date. Please make sure the date is after 2020.", "tableTitle": "Existing Attachments", "fileName": "File Name", "addDocument": "+Add Document"}}, "changeLastContacted": {"text": "Change \"Last Contacted\" Date", "tooltip": "Change \"Last Contacted\" Date", "dialogTitle": "Change \"Last Contacted\" Date", "successMessage": "Patient(s) processed"}}, "buttons": {"add": "Add Patient"}, "notes": "Patient Notes", "notesCounter_one": "{{count}} note", "notesCounter_other": "{{count}} notes", "dateOfBirth": "Date of Birth: {{format}}", "info": {"title": "Patient"}, "errors": "Invalid use of patients screen", "tooltips": {"daysInPaneling": "{{days}} days in Paneling", "patientsThatCanBelongHousehold": "Patients that can belong to Household"}}, "patientDocumentFolder": {"download": "File", "subtype": "Sub Type", "showAsTable": "Show result as table", "showAsTree": "Show as explorer tree"}, "patientGeneratedLetter": {"title": "Generated Letters"}, "patientHistoryItem": {"dialogs": {"edit": "Edit Condition", "add": "Add Condition", "regularItemDescription": "Description"}}, "attachmentDialog": {"edit": "Edit Attachment", "add": "Add Attachment"}, "patientAttachment": {"title": "Patient Attachments"}, "allergy": {"title": "Allergies", "dialogs": {"edit": "Edit Allergy", "add": "Add Allergy"}, "buttons": {"add": "Add Allergy"}}, "folder": {"title": "Folder", "dialogs": {"edit": "Edit <PERSON>", "add": "Add Folder"}, "buttons": {"add": "Add Folder"}}, "labTest": {"title": "Lab Test", "dialogs": {"edit": "Edit Lab Test", "add": "Add Lab Test"}, "buttons": {"add": "Add Lab Test"}}, "vitalSigns": {"title": "Vital Signs", "dialogs": {"edit": "Edit Vital Sign", "add": "Add Vital Sign"}, "buttons": {"add": "Add Vital Sign"}}, "socialHistory": {"title": "Social Histories", "dialogs": {"edit": "Edit Social History", "add": "Add Social History"}, "buttons": {"add": "Add Social History"}}, "unitOfMeasurement": {"title": "Units Of Measurement", "dialogs": {"edit": "Edit Unit Of Measurement", "add": "Add Unit Of Measurement"}, "buttons": {"add": "Add Unit Of Measurement"}}, "importItemDetail": {"buttons": {"group": "Take", "label": "Value to use for resolution"}}, "invoice": {"title": "Invoices", "actions": {"markAsInvoiced": {"text": "<PERSON> as Invoiced", "tooltip": "<PERSON> as Invoiced", "dialogTitle": "<PERSON> as Invoiced", "successMessage": "Task has been queued successfully.", "confirmationMessage": "Are you sure you want to mark as invoiced selected record(s)?"}, "markAsReadyToInvoice": {"text": "<PERSON> as Ready to Invoice", "tooltip": "<PERSON> as Ready to Invoice", "dialogTitle": "<PERSON> as Ready to Invoice", "successMessage": "Records updated.", "confirmationMessage": "Are you sure you want to mark as ready to invoice selected record(s)?"}, "sendBackToDraft": {"text": "Send Back To Draft", "tooltip": "Send Back To Draft", "dialogTitle": "Send Back To Draft", "successMessage": "Records updated.", "confirmationMessage": "Are you sure you want to send back to draft selected record(s)?"}}, "dialogs": {"add": "Add Invoice", "note": "Invoice Notes", "workflowStatus": "Invoice Workflow Status Logs"}, "buttons": {"add": "Add Invoice", "import": "Imports"}}, "careplanResponseTemplate": {"title": "eConsult Response Templates", "dialogs": {"edit": "Edit Response Template", "add": "Add Response Template"}, "buttons": {"add": "Add Response Template"}, "errorMessage": "Invalid use of eConsult Response Template screen."}, "diagnosisAlias": {"title": "Diagnosis Aliases", "dialogs": {"edit": "<PERSON>ag<PERSON><PERSON>", "add": "Add Diagnostic Alias"}, "buttons": {"add": "Add Diagnosis Alias"}}, "medication": {"title": "Medications", "dialogs": {"edit": "Edit Medications", "add": "Add Medications"}, "buttons": {"add": "Add Medication"}}, "kUseQueryAutocomplete": {"new": "New", "newEntity": "New Entity", "valueField": "Search {{valueField}}. Minimum {{minQueryLen}} characters. Press Enter key to search.", "searchField": "Search {{searchField}}. Minimum {{minQueryLen}} characters. Press Enter key to search.", "moreSearchField": "Search {{searchFields}} or {{last}}. Minimum {{minQueryLen}} characters. Press Enter key to search."}, "kExportDialog": {"title": "Export", "label": "Profile", "successMessage": "Export successfully queued.", "rule": "Export profile"}, "kUploadFileDialog": {"title": "Upload File", "label": "Drag and drop file(s) here or click.", "added": "{{fileName}} is ready to be upload.", "preview": "Files:", "overrideExisting": "Override previously upload data", "success": {"message_one": "Fax successfully upload.", "message_other": "{{count}} faxes successfully upload."}, "error": {"message_one": "Error upload file:", "message_other": "Error upload files:"}}, "kTransitionAlert": {"reOpen": "Re-open"}, "kNotificationDialog": {"title": "Notification Details"}, "kUserCard": {"settingsLabel": "Settings", "signOutLabel": "Sign Out"}, "katanaGlobalErrorFallback": {"alertButton": "Try Again", "alertMessage": "Unexpected error. Please, contact AOShield Solutions Inc:", "title": {"detail": "Details", "error": "Error"}, "listItem": {"name": "Name", "message": "Message", "stack": "<PERSON><PERSON>"}}, "kFeedback": {"button": "Send Feedback", "checkbox": "Add Screenshot", "dialogs": {"add": "Thanks for help us improve!"}, "maxLength": "Can not have more than 100 characters"}, "note": {"edited": "(Edited)", "dialogs": {"edit": "Edit Note", "add": "Add Note"}}, "dashboard": {"gpCard": {"labels": {"clinicalQuestion": "Clinical Questions", "spResponses": "Sp Responses"}, "warning": "ACTION REQUIRED: YOU HAVE OUTSTANDING APPROVALS! Please navigate to the <patientsLink>Available</patientsLink> tab to review your outstanding clinical questions. View the <helpLink>HELP GUIDE</helpLink> or <videoLink>TRAINING VIDEO</videoLink> for more information."}, "careplanCard": {"titleCard": "eConsults", "subtitleCard": "Summarization of eConsults", "labels": {"attention": "Attention", "assigned": "Assigned"}}, "cpsCareplanByWorkflowStatus": {"titleCard": "eConsult Status Tracker", "cascadeChart": {"generalPractitioner": "General Practitioner (GP)", "pending": "Pending", "approved": "Approved", "pendingReview": "Pending Review (Dispatch)", "itpPending": "ITP Pending", "specialist": "Specialist (SP)", "upcoming": "Upcoming", "available": "Available", "unanswered": "Unanswered", "total": "Total Consults in System", "draft": "Draft", "proposed": "Proposed"}, "tooltip": {"gPPending": "These eConsults are waiting in the GPs' inboxes for review and approval. eConsults move into this status after an initial Quality Assurance review.", "gPApproved": "These eConsults have been approved by the GP and have not yet been dispatched.", "pendingReview": "These eConsults have been approved by the GP and were identified as requiring review (e.g., due to a predefined Diagnostic Code) or were sent back for additional review by a Specialist. The pending eConsults are being reviewed by the administrative team. After review, the administrative team may send the consult forward for dispatch or cancel it if applicable.", "iTPPending": "All eConsults within international Specialists'  (ITP) inboxes that require actioning. Once case notes and recommendations are written by the international Specialist these eConsults move on to the Specialist.", "iTPApproved": "These eConsults appear within the Specialists' \"Upcoming\" tab but cannot be approved in this status to ensure compliance with Alberta Health billing requirements. eConsults move into this status after an international Specialist (ITP) completes the case notes and recommendations for an eConsult.", "sPPending": "These eConsults can be approved immediately by Specialists (SPs). eConsults move into this status once they are eligible for approval to ensure a Specialist only actions one eConsult per patient within a 24-hour period. The system moves eligible eConsults from \"SP Upcoming\" to \"SP Approved\" every hour and upon every new Specialist approval.", "unanswered": "These eConsults did not receive a Specialist response within 30 days from the request date and cannot be billed as an O-Code.", "sPApproved": "These eConsults have been approved by the Specialist and can be included for O-Code billing.", "total": "Sum of all eConsults at any stage of the workflow this week. This does not consider eConsults that have been approved in previous weeks.", "proposed": "Proposed includes all eConsult Questions that have been generated by AI but have not been reviewed by Quality Assurance. Once reviewed by Quality Assurance they will move to GP Pending."}}, "eccCareplanByWorkflowStatus": {"titleCard": "ECC eConsults by Status"}, "quotas": {"title": {"request": "Requested <PERSON><PERSON><PERSON>", "approve": "Approved Quotas"}, "subtitleCard": "Summarization of Quotas", "labels": {"approved": "Approved eConsults", "assigned": "Assigned eConsults", "weekly": "Weekly", "daily": "Daily", "total": "Total"}}, "manageFaxCard": {"titleCard": "Fax Summary", "subtitleCard": "Summarization of fax assigned by groups", "labels": {"notAssigned": "Not Assigned"}}, "referralCountCard": {"titleCard": "Referral Count", "subtitleCard": "Count by each active status"}, "manageGroupCard": {"titleCard": "Assigned Fax summary", "subtitleCard": "Assigned faxes group summarization", "labels": {"notAssigned": "Not Assigned"}}, "transcriberManagerCard": {"titleCard": "Transcriber Fax Summary", "subtitleCard": "Summarization of fax assigned and processed by transcribers", "transcribe": "Transcribe", "upload": "Upload"}, "billingSummaryCard": {"titleCard": "eConsult Billing Overview", "subtitleCard": "Billing Cycle", "lastWeek": "Last Week", "thisWeek": "This Week", "rCode": {"title": "R-Codes", "subtitle": "eConsult Questions", "currentlyBillable": {"title": "Currently Billable", "tooltip": "These are eConsult Questions that have been approved by a practitioner (GP) and successfully dispatched to an international Specialist (ITP) for review. The workflow status is \"Pending Review\", \"ITP Pending\", \"SP Pending\", or \"SP Approved\"."}, "gpPending": {"title": "GP Pending", "tooltip": "This is the number of eConsults currently available for General Practitioners to approve. For the \"Last Week\" column this reflects the status 7 days ago."}, "estimatedTotalRCode": {"title": "Estimated Total", "subtitle": "If GPs approve everything currently Available to them", "tooltip": "The sum of \"Currently Billable\" and \"GP Pending\". For the \"Last Week\" column this reflects the number we calculated 7 days ago and not the actual total that was achieved."}, "finalTotal": {"title": "Final Total", "tooltip": "The number of actual R-Codes that were billed last cycle. This might be higher or lower than the \"Estimated Total\" for the \"Last Week\" column as the number of dispatched eConsults and excluded bills varies."}}, "oCode": {"title": "O-Codes", "subtitle": "eConsult Recommendations", "currentlyBillable": {"title": "Currently Billable", "tooltip": "These are eConsults that have been approved by a Specialist (SP Approved)."}, "spAvailable": {"title": "SP Available", "tooltip": "This is the number of eConsults currently available for Specialists to approve. For the \"Last Week\" column this reflects the status 7 days ago."}, "estimatedTotalOCode": {"title": "Estimated Total", "subtitle": "If Specialists approve everything currently Available to them", "tooltip": "The sum of \"Currently Billable\" and  all eConsults available for Specialists to approve (SP Available) as of today. For the \"Last Week\" column this reflects the number calculated 7 days ago and not the actual total that was achieved by the end of the cycle."}, "finalTotal": {"title": "Final Total", "tooltip": "The number of actual O-Codes that were billed last cycle. This might be higher or lower than the \"Estimated Total\" as Specialists might approve more or less eConsults than currently available to them."}}, "na": "NA"}, "patientPanelingMetricsCard": {"titleCard": "Patient Paneling Metrics", "subtitleCard": "", "labels": {"ineligible": "Ineligible", "blocked": "Blocked", "completed": "Completed"}}, "completedPatientsByHpcCard": {"titleCard": "Completed Patients By CH Nurse", "subtitleCard": "Completed Patients in the last week (Top 10)"}, "hpcMvcaMetricsCard": {"titleCard": "Summary", "subtitleCard": "Data from a 7-day period:", "labels": {"patientsCompleted": "Patients Completed", "questionsGenerated": "Questions Generated"}}, "questionsCompletedPerHpc": {"titleCard": "Questions Completed By CH Nurse", "subtitleCard": "Questions Completed in the last week (Top 10)", "labels": {"proposalPractitioner": "Gp ITP", "questionsCompleted": "Questions Completed"}}, "questionsUnansweredPerSP": {"titleCard": "SP with more Unanswered Questions", "subtitleCard": "Unanswered Questions (Top 10)", "labels": {"sp": "SP", "questionsUnanswered": "Unanswered Questions"}}, "questionsUnansweredPerDiagnostic": {"titleCard": "Diagnostics with more Unanswered Questions", "subtitleCard": "Unanswered Questions (Top 10)", "labels": {"sp": "Diagnostic", "questionsUnanswered": "Unanswered Questions"}}, "patientPoolOccupancy": {"titleCard": "Patient Pools Status", "subtitleCard": "(Top 10)", "labels": {"patientOccupancyLevel": "Occupancy Level", "poolSize": "Pool Size"}}}, "patientHistoryType": {"title": "History Types", "actions": {"assignParticipateInGenerateQuestion": {"text": "Include in Clinical Question", "tooltip": "Include in Clinical Question", "dialogTitle": "Include in Clinical Question", "labelTextField": "Reason", "successMessage": "History Type(s) processed.", "confirmationMessage": "Are you sure you want to include the selected records in Clinical Question generation?"}, "unassignParticipateInGenerateQuestion": {"text": "Exclude from Clinical Question", "tooltip": "Exclude from Clinical Question", "dialogTitle": "Exclude from Clinical Question", "labelTextField": "Reason", "successMessage": "History Type(s) processed.", "confirmationMessage": "Are you sure you want to exclude the selected records from Clinical Question generation?"}, "setGroup": {"text": "Set Group", "toolTip": "Set Group", "success": {"message_one": "Group assigned.", "message_other": "Group assigned."}}, "setShowOrder": {"text": "Set Show Order", "toolTip": "Set Show Order", "success": {"message_one": "Show Order assigned.", "message_other": "Show Order assigned."}}}, "dialogs": {"add": "Add History Type", "edit": "Edit History Type", "setGroup": {"text": "Set Group", "toolTip": "Set Group", "success": {"message_one": "Group assigned.", "message_other": "Group assigned."}}, "setShowOrder": {"text": "Set Show Order", "toolTip": "Set Show Order", "success": {"message_one": "Show Order assigned.", "message_other": "Show Order assigned."}}}, "buttons": {"add": "Add History Type"}}, "groupLabTests": {"title": "Lab Test Groups", "dialogs": {"edit": "Edit Test Lab Group", "add": "Add Test Lab Group"}, "buttons": {"add": "Add Lab Test Group"}}, "groupFolders": {"title": "Folder Groups", "dialogs": {"edit": "Edit Folder Group", "add": "Add Folder Group"}, "buttons": {"add": "Add Folder Group"}}, "groupItems": {"title": "History Type Groups", "dialogs": {"edit": "Edit History Type Group", "add": "Add History Type Group", "setOrder": {"text": "Set Group Order", "toolTip": "Set Group Order", "success": {"message_one": "Group Order assigned.", "message_other": "Group Orders assigned."}}}, "actions": {"setOrder": {"text": "Set Group Order", "toolTip": "Set Group Order", "success": {"message_one": "Group Order assigned.", "message_other": "Group Orders assigned."}}}, "buttons": {"add": "Add History Type Group"}}, "bulkSearchIndeBuild": {"title": "Bulk Search Index Build Requests", "buttons": {"add": "Add Request Bulk Search Index Build"}, "dialogs": {"add": "Request Bulk Search Index Build", "edit": "Edit Bulk Search Index Build"}}, "patientStatus": {"title": "Patient Statuses", "actions": {"ActivateStatus": {"text": "Set As Active Status", "tooltip": "Set As Active Status", "dialogTitle": "Set As Active Status", "labelTextField": "Reason", "successMessage": "Patient Status processed", "confirmationMessage": "Are you sure you want set as Active Status the selected records?"}, "DeactivateStatus": {"text": "Set As Inactive Status", "tooltip": "Set As Inactive Status", "dialogTitle": "Set As Inactive Status", "labelTextField": "Reason", "successMessage": "Patient Status processed", "confirmationMessage": "Are you sure you want set as Inactive Status the selected records?"}}}, "patientEmrSyncRecord": {"title": "EMR Patients"}, "flag": {"title": "Chart Flags", "actions": {"enableGrantConsent": {"text": "Enable Grant Consent", "tooltip": "Enable Grant Consent", "dialogTitle": "Enable/Disable Grant Consent", "successMessage": "Grant consent(s) processed", "confirmationMessage": "Are you sure you want to adjust the selected records?"}, "disableGrantConsent": {"text": "Disable Grant Consent", "tooltip": "Disable Grant Consent"}, "enableDeclineConsent": {"text": "Enable Decline Consent", "tooltip": "Enable Decline Consent", "dialogTitle": "Enable/Disable Decline Consent", "successMessage": "Decline consent(s) processed", "confirmationMessage": "Are you sure you want to adjust the selected records?"}, "disableDeclineConsent": {"text": "Disable Decline Consent", "tooltip": "Disable Decline Consent"}, "editFlagType": {"text": "Edit Flag Type", "tooltip": "Edit Flag Type", "dialogTitle": "Edit Flag Type"}}}, "goalItem": {"title": "Goal Items", "dialogs": {"edit": "Edit Goal Item", "add": "Add Goal Item"}, "buttons": {"add": "Add Goal Item"}}, "patientPool": {"title": "Patient Pool Size", "dialogs": {"edit": "Edit <PERSON>ient <PERSON> Si<PERSON>", "add": "Add Patient Pool Size"}, "buttons": {"add": "Add Patient Pool Size"}, "details": {"title": "Details", "patientAvailable": "Patients Available", "patientStarted": "Patients Started", "patientInComing": "Patients Incoming"}, "filters": {"assignedPractitioners": "Assigned Practitioners"}}, "reminder": {"title": "Reminders", "dialogs": {"add": "<PERSON><PERSON>", "edit": "<PERSON>minder"}}, "kCitationFiles": {"title": "Citation Files"}, "reports": {"title": "Reports", "dialogs": {"add": "Add Report", "edit": "Edit Report"}, "buttons": {"add": "Add Report"}}, "declineReason": {"title": "Decline Reason", "dialogs": {"add": "Add Decline Reason", "edit": "Edit Decline Reason"}, "buttons": {"add": "Add Decline Reason"}}, "aiPromptDefinition": {"title": "AI Prompts", "buttons": {"add": "Add AI Prompt"}, "dialogs": {"add": "Add AI Prompt", "edit": "Edit AI Prompt"}}, "hpcPractitioners": {"title": "CH Nurse Practitioners"}, "KSnackbar": {"seeMore": "See More"}, "qualityReviewHPC": {"title": "Quality Review CH Nurse", "label": "Quality Review CH Nurse", "tabs": {"locked": "Locked", "available": "Available", "upcoming": "Upcoming", "mine": "Mine", "unanswered": "Unanswered"}}, "clinicalQuestions": {"tabs": {"clinicalQuestions": "Clinical Questions", "allUpcoming": "All Upcoming"}}, "qualityReviewMVCA": {"title": "Quality Review MVCA", "label": "Quality Review MVCA", "tabs": {"locked": "Locked", "available": "Available", "upcoming": "Upcoming", "mine": "Mine", "unanswered": "Unanswered"}}, "expandableClinicalQuestionTable": {"cqDxCode": "DX", "cqDxCodeName": "Description"}, "entityChangeLog": {"buttons": {"applyFilters": "Apply Filters"}, "startDate": "Start date", "endDate": "End date"}, "timeLine": {"snapShot": "Current State"}}