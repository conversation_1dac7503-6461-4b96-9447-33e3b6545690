﻿using Aoshield.Core.Entities.Models;
using Katana.Services.UnitsOfMeasurement;
using Katana.Services.UnitsOfMeasurement.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// UnitOfMeasurement CRUD service
    /// </summary>
    public class UnitsOfMeasurementServiceTest : IUnitsOfMeasurementService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddUnitOfMeasurementDto> Add(AddUnitOfMeasurementDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddUnitOfMeasurementDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<UnitOfMeasurementDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<UnitOfMeasurementDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<UnitOfMeasurementDto> GetById(int id,
            CancellationToken cancellation = default)
            => await Task.Run(() => id <= 0 ? null : new UnitOfMeasurementDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateUnitOfMeasurementDto> Update(UpdateUnitOfMeasurementDto dto,
            CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateUnitOfMeasurementDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
