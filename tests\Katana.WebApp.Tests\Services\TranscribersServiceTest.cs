﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Katana.Services.Common.Models;
using Katana.Services.Transcribers;
using Katana.Services.Transcribers.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Transcriber CRUD service
    /// </summary>
    public class TranscribersServiceTest : ITranscribersService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddTranscriberDto> Add(AddTranscriberDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddTranscriberDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscriberDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<TranscriberDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<TranscriberDto>
            GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new TranscriberDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateTranscriberDto> Update(UpdateTranscriberDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateTranscriberDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(
            () => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(
            () => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscriberDto>> GetTranscribersByGroupId(int groupId,
            SieveModel query) =>
            await Task.Run(() => new PagedResults<TranscriberDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc />
        public Task<int> AssignToTranscriberGroup(AssignTranscribersGroupDto[] dto,
            CancellationToken cancellationToken = default) => Task.FromResult(dto.Length);

        #endregion
    }
}
