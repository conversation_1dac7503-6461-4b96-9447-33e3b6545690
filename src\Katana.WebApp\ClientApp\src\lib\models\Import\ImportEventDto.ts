import { BaseEntityDto } from "../BaseEntityDto";
import { DeferredRequestCommonDto } from "../DeferredRequests/DeferredRequestDto";

export interface ImportEventDto extends BaseEntityDto {
    importDate: string;
    sourceName: string;
    entityType: string;
    entityDisplayName: string;
    profileName: string;
    totalItems: number;
    addedItems: number;
    updatedItems: number;
    unchangedItems: number;
    missedItems: number;
    failedItems: number;
    deferredRequest: DeferredRequestCommonDto | null;
    allowRetryImport: boolean;
    noRetry: boolean;
}
