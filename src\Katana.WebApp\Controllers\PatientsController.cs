using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Exceptions;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Core.Services.Audit;
using Aoshield.Core.Services.Audit.Models;
using Aoshield.Core.Services.Reminder.Models;
using Aoshield.Services.Core.Search.Models;
using Katana.Core.Entities;
using Katana.Services.Attachment.Models;
using Katana.Services.Careplans.Models;
using Katana.Services.Common.Models;
using Katana.Services.PatientAllergies;
using Katana.Services.PatientAllergies.Models;
using Katana.Services.PatientDiagnostics;
using Katana.Services.PatientDiagnostics.Models;
using Katana.Services.PatientDocumentFolders.Models;
using Katana.Services.PatientFlags;
using Katana.Services.PatientFlags.Models;
using Katana.Services.PatientGeneratedLetters;
using Katana.Services.PatientGeneratedLetters.Models;
using Katana.Services.PatientGoals;
using Katana.Services.PatientGoals.Models;
using Katana.Services.PatientHouseholds;
using Katana.Services.PatientHouseholds.Models;
using Katana.Services.PatientMedications;
using Katana.Services.PatientMedications.Models;
using Katana.Services.Patients;
using Katana.Services.Patients.Models;
using Katana.Services.PatientSocialHistories;
using Katana.Services.PatientSocialHistories.Models;
using Katana.Services.PatientVitalSigns;
using Katana.Services.PatientVitalSigns.Models;
using Katana.WebApp.FilterAttributes;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Patients Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PatientsController : ControllerBase
    {
        private readonly IPatientsService _patientsService;
        private readonly IPatientSocialHistoriesService _patientSocialHistoriesService;
        private readonly IPatientDiagnosticsService _patientDiagnosticsService;
        private readonly IPatientMedicationsService _patientMedicationsService;
        private readonly IPatientVitalSignsService _patientVitalSignsService;
        private readonly IPatientAllergiesService _patientAllergiesService;
        private readonly IPatientGeneratedLettersService _patientGeneratedLettersService;
        private readonly IPatientGoalsService _patientGoalsService;
        private readonly IPatientFlagsService _patientFlagsService;
        private readonly IPatientHouseholdService _patientHouseholdService;
        private readonly IAuditStorageProvider _auditStorageProvider;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="crudService">CRUD service</param>
        /// <param name="patientSocialHistoriesService">CRUD service</param>
        /// <param name="patientDiagnosticsService">CRUD service</param>
        /// <param name="patientMedicationCrudService">CRUD service</param>
        /// <param name="patientVitalSignsService">CRUD service</param>
        /// <param name="patientAllergiesService">CRUD service</param>
        /// <param name="patientGeneratedLettersService">CRUD service</param>
        /// <param name="patientGoalsService">CRUD service</param>
        /// <param name="patientFlagsService">CRUD service</param>
        /// <param name="patientHouseholdService"></param>
        /// <param name="auditStorageProvider">Entity Auditing Services</param>
        public PatientsController(
            IPatientsService crudService,
            IPatientSocialHistoriesService patientSocialHistoriesService,
            IPatientDiagnosticsService patientDiagnosticsService,
            IPatientMedicationsService patientMedicationCrudService,
            IPatientVitalSignsService patientVitalSignsService,
            IPatientAllergiesService patientAllergiesService,
            IPatientGeneratedLettersService patientGeneratedLettersService,
            IPatientGoalsService patientGoalsService,
            IPatientFlagsService patientFlagsService,
            IPatientHouseholdService patientHouseholdService,
            IAuditStorageProvider auditStorageProvider
        )
        {
            _patientsService = crudService;
            _patientSocialHistoriesService = patientSocialHistoriesService;
            _patientDiagnosticsService = patientDiagnosticsService;
            _patientMedicationsService = patientMedicationCrudService;
            _patientVitalSignsService = patientVitalSignsService;
            _patientAllergiesService = patientAllergiesService;
            _patientGeneratedLettersService = patientGeneratedLettersService;
            _patientGoalsService = patientGoalsService;
            _patientFlagsService = patientFlagsService;
            _patientHouseholdService = patientHouseholdService;
            _auditStorageProvider = auditStorageProvider;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("MigrateAttachments")]
        public async Task<bool> PopulatePatientAttachmentsData([FromQuery] int? take = null, [FromQuery] string practitionerIds = null, CancellationToken cancellation = default)
        {
            await _patientsService.MigrateAttachments(take, practitionerIds, cancellation: cancellation);
            return true;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("GenerateCRFirstRound")]
        public async Task<bool> PopulateFirstCQRoundData(CancellationToken cancellation)
        {
            await _patientsService.PopulateFirstCQRoundData(cancellation: cancellation);
            return true;
        }

        //[HttpGet("MigratePatientWS")]
        //public async Task<bool> MoveWorkflowStatusesTOPatientPoolItems(CancellationToken cancellation)
        //{
        //    await _patientsService.MoveWorkflowStatusesTOPatientPoolItems(cancellation);
        //    return true;
        //}

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<PatientDto>> GetPatients([FromQuery] SieveModel query) => await _patientsService.GetAsPagedResults(query);

        /// <summary>
        /// Get dto by id
        /// </summary>
        /// <param name="id">Id of the entity</param>
        /// <param name="cancellation"></param>
        /// <returns>Dto with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<PatientDto>> GetPatient(int id, CancellationToken cancellation)
        {
            var dto = await _patientsService.GetById(id, cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdatePatientDto>> PutPatient(int id, UpdatePatientDto dto,
            CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.Update(dto, cancellation: cancellation);

        /// <summary>
        /// Update Patient Intake
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Intake/{id}")]
        public async Task<ActionResult<UpdatePatientIntakeDto>> PutPatientIntake(int id, UpdatePatientIntakeDto dto,
            CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.UpdatePatientIntake(dto, cancellation: cancellation);

        /// <summary>
        /// Add entity
        /// </summary>
        /// <param name="addDto">Add dto's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost]
        public async Task<ActionResult<AddPatientDto>> PostPatient(AddPatientDto addDto,
            CancellationToken cancellation)
        {
            var dto = await _patientsService.Add(addDto, cancellation: cancellation);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Delete entity
        /// </summary>
        /// <param name="id">Id of the entity to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeletePatient(int id,
            CancellationToken cancellation = default) =>
            await _patientsService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestorePatient(int id) =>
            await _patientsService.Restore(id, notify: false);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Invite Batch
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPost("InviteBatch")]
        public async Task<int[]> InviteBatch(
            [FromBody] int[] ids, CancellationToken cancellation) =>
            await _patientsService.InviteBatch(ids,
                cancellationToken: cancellation);

        /// <summary>
        /// Update property Medications
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Patient dto</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/Medications")]
        public async Task<ActionResult<UpdatePatientMedicationsDto>> PutPatientMedications(int id,
            UpdatePatientMedicationsDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.UpdatePatientMedications(dto, cancellation: cancellation);

        /// <summary>
        /// Update property Diagnoses
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Patient dto</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/Diagnoses")]
        public async Task<ActionResult<UpdatePatientDiagnosesDto>> PutPatientDiagnoses(int id,
            UpdatePatientDiagnosesDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.UpdatePatientDiagnoses(dto, cancellation: cancellation);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLog(int id,
            [FromQuery] SieveModel query) =>
            await _patientsService.GetStatusLogsAsPagedResults(id, query);

        /// <summary>
        /// Add note to Patient
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Notes")]
        public async Task<ActionResult<AddPatientNoteDto>> PostNote(AddPatientNoteDto addDto)
        {
            var dto = await _patientsService.AddNote(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Note by Id
        /// </summary>
        /// <param name="id">Note id</param>
        /// <returns></returns>
        [HttpGet("Notes/{id}")]
        public async Task<ActionResult<PatientNoteDto>> GetNote(int id)
        {
            var dto = await _patientsService.GetNoteById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Notes")]
        public async Task<IPagedResults<PatientNoteDto>> GetNotes(int id, [FromQuery] SieveModel query) =>
            await _patientsService.GetNotesAsPagedResults(id, query);

        /// <summary>
        /// Update note
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Notes/{id}")]
        public async Task<ActionResult<UpdatePatientNoteDto>> PutNote(int id, UpdatePatientNoteDto dto) =>
            id != dto.Id ? BadRequest() : await _patientsService.UpdateNote(dto);

        /// <summary>
        /// Delete Note
        /// </summary>
        /// <param name="id">Id of the note to delete</param>
        /// <returns></returns>
        [HttpDelete("Notes/{id}")]
        public async Task<ActionResult<int?>> DeleteNote(int id) =>
            await _patientsService.DeleteNote(id);

        /// <summary>
        /// Delete Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to delete</param>
        /// <returns></returns>
        [HttpDelete("DeleteNoteBatch")]
        public async Task<int[]> DeleteNoteBatch(int[] ids) =>
            await _patientsService.DeleteNoteBatch(ids);

        /// <summary>
        /// Restore Note batch
        /// </summary>
        /// <param name="ids">Id of the notes to restore</param>
        /// <returns></returns>
        [HttpDelete("RestoreNoteBatch")]
        public async Task<int[]> RestoreNoteBatch(int[] ids) =>
            await _patientsService.RestoreNoteBatch(ids);

        /// <summary>
        /// Get workflow statuses
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/CQWorkflowstatus")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<PatientWorkflowStatus>>>
            GetWorkflowStatusLogs(int id, [FromQuery] SieveModel query) =>
            await _patientsService.GetWorkflowStatusLogsAsPagedResults(id, query);

        #region Patient Medication

        /// <summary>
        /// Get Medications
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Medications")]
        public async Task<IPagedResults<PatientMedicationDto>> GetMedications(int id, [FromQuery] SieveModel query)
            => await _patientMedicationsService.GetMedicationsByPatientId(id, query);

        /// <summary>
        /// Get Medications by Patient Id
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>MedicationPatient Dto</returns>
        [HttpGet("Medications/{id}")]
        public async Task<ActionResult<MedicationPatientDto>> GetMedication(int id, CancellationToken cancellation)
        {
            var dto = await _patientsService.GetMedicationByPatientId(id, cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        #endregion

        #region Patient Diagnostic

        /// <summary>
        /// Add Diagnostic to Patient
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Diagnostics")]
        public async Task<ActionResult<AddPatientDiagnosticDto>> PostDiagnostic(
            AddPatientDiagnosticDto addDto)
        {
            var dto = await _patientDiagnosticsService.Add(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Diagnostic by Id
        /// </summary>
        /// <param name="id">Patient Diagnostic id</param>
        /// <returns></returns>
        [HttpGet("Diagnostics/{id}")]
        public async Task<ActionResult<PatientDiagnosticDto>> GetDiagnostic(int id)
        {
            var dto = await _patientDiagnosticsService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get Diagnostics
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Diagnostics")]
        public async Task<IPagedResults<PatientDiagnosticDto>> GetDiagnostics(int id,
            [FromQuery] SieveModel query)
            => await _patientDiagnosticsService.GetDiagnosticsByPatientId(id, query);

        /// <summary>
        /// Update Diagnostic
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Diagnostics/{id}")]
        public async Task<ActionResult<UpdatePatientDiagnosticDto>> PutDiagnostic(int id,
            UpdatePatientDiagnosticDto dto) =>
            id != dto.Id ? BadRequest() : await _patientDiagnosticsService.Update(dto);

        /// <summary>
        /// Delete Diagnostic
        /// </summary>
        /// <param name="id">Id of the Diagnostic to delete</param>
        /// <returns></returns>
        [HttpDelete("Diagnostics/{id}")]
        public async Task<ActionResult<int?>> DeleteDiagnostic(int id) =>
            await _patientDiagnosticsService.Delete(id);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Patient Comorbidity Ids to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("Diagnostics/DeleteBatch")]
        public async Task<int[]> DeleteBatchDiagnostic(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _patientDiagnosticsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Patient Comorbidity Ids to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("Diagnostics/RestoreBatch")]
        public async Task<int[]> RestoreBatchDiagnostic(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _patientDiagnosticsService.RestoreBatch(dtoArr, cancellation: cancellation);

        #endregion

        #region Patient SocialHistory

        /// <summary>
        /// Add SocialHistory to Patient
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("SocialHistories")]
        public async Task<ActionResult<AddPatientSocialHistoryDto>> PostPatientSocial(
                    AddPatientSocialHistoryDto addDto)
        {
            var dto = await _patientSocialHistoriesService.Add(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get SocialHistory by Id
        /// </summary>
        /// <param name="id">SocialHistory id</param>
        /// <returns></returns>
        [HttpGet("SocialHistories/{id}")]
        public async Task<ActionResult<PatientSocialHistoryDto>> GetPatientSocial(int id)
        {
            var dto = await _patientSocialHistoriesService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get SocialHistories
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/SocialHistories")]
        public async Task<IPagedResults<PatientSocialHistoryDto>>
            GetSocialHistoriesByParentId(int id, [FromQuery] SieveModel query) =>
            await _patientSocialHistoriesService.GetSocialHistoriesByPatientId(id, query);

        /// <summary>
        /// Update SocialHistory
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("SocialHistories/{id}")]
        public async Task<ActionResult<UpdatePatientSocialHistoryDto>> PutPatientSocial(int id,
            UpdatePatientSocialHistoryDto dto) =>
            id != dto.Id ? BadRequest() : await _patientSocialHistoriesService.Update(dto);

        /// <summary>
        /// Delete SocialHistory
        /// </summary>
        /// <param name="id">Id of the medication to delete</param>
        /// <returns></returns>
        [HttpDelete("SocialHistories/{id}")]
        public async Task<ActionResult<int?>> DeletePatientSocial(int id) =>
            await _patientSocialHistoriesService.Delete(id);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">patientSocialHistory's Id to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("SocialHistories/DeleteBatch")]
        public async Task<int[]> DeleteBatchSocialHistories(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation)
            => await _patientSocialHistoriesService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">patientSocialHistory's Id to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("SocialHistories/RestoreBatch")]
        public async Task<int[]> RestoreBatchSocialHistories(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation)
            => await _patientSocialHistoriesService.RestoreBatch(dtoArr, cancellation: cancellation);

        #endregion

        #region Patient VitalSign

        /// <summary>
        /// Add VitalSign to Patient
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("VitalSigns")]
        public async Task<ActionResult<AddPatientVitalSignDto>> PostVitalSign(
            AddPatientVitalSignDto addDto)
        {
            var dto = await _patientVitalSignsService.Add(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get VitalSign by Id
        /// </summary>
        /// <param name="id">VitalSign id</param>
        /// <returns></returns>
        [HttpGet("VitalSigns/{id}")]
        public async Task<ActionResult<PatientVitalSignDto>> GetVitalSign(int id)
        {
            var dto = await _patientVitalSignsService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get VitalSigns
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/VitalSigns")]
        public async Task<IPagedResults<PatientVitalSignDto>> GetVitalSignsByParentId(int id,
            [FromQuery] SieveModel query) =>
            await _patientVitalSignsService.GetVitalSignsByPatientId(id, query);

        /// <summary>
        /// Update VitalSign
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("VitalSigns/{id}")]
        public async Task<ActionResult<UpdatePatientVitalSignDto>> PutVitalSign(int id,
            UpdatePatientVitalSignDto dto) =>
            id != dto.Id ? BadRequest() : await _patientVitalSignsService.Update(dto);

        /// <summary>
        /// Delete VitalSign
        /// </summary>
        /// <param name="id">Id of the medication to delete</param>
        /// <returns></returns>
        [HttpDelete("VitalSigns/{id}")]
        public async Task<ActionResult<int?>> DeleteVitalSign(int id) =>
            await _patientVitalSignsService.Delete(id);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">patientVitalSign's Id to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("VitalSigns/DeleteBatch")]
        public async Task<int[]> DeleteBatchVitalSigns(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _patientVitalSignsService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">patientVitalSign's Id to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("VitalSigns/RestoreBatch")]
        public async Task<int[]> RestoreBatchVitalSigns(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
            => await _patientVitalSignsService.RestoreBatch(dtoArr, cancellation: cancellation);

        #endregion

        #region Patient Allergy

        /// <summary>
        /// Add Allergy to Patient
        /// </summary>
        /// <param name="addDto">Dto's data</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPost("Allergies")]
        public async Task<ActionResult<AddPatientAllergyDto>> PostPatientAllergy(
            AddPatientAllergyDto addDto)
        {
            var dto = await _patientAllergiesService.Add(addDto);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Get Allergy by Id
        /// </summary>
        /// <param name="id">Allergy id</param>
        /// <returns></returns>
        [HttpGet("Allergies/{id}")]
        public async Task<ActionResult<PatientAllergyDto>> GetPatientAllergy(int id)
        {
            var dto = await _patientAllergiesService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get Allergies
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Allergies")]
        public async Task<IPagedResults<PatientAllergyDto>> GetAllergiesByParentId(int id,
            [FromQuery] SieveModel query)
            => await _patientAllergiesService.GetAllergiesByPatientId(id, query);

        /// <summary>
        /// Update Allergy
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data update</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Allergies/{id}")]
        public async Task<ActionResult<UpdatePatientAllergyDto>> PutPatientAllergy(int id,
            UpdatePatientAllergyDto dto) =>
            id != dto.Id ? BadRequest() : await _patientAllergiesService.Update(dto);

        /// <summary>
        /// Delete Allergy
        /// </summary>
        /// <param name="id">Id of the medication to delete</param>
        /// <returns></returns>
        [HttpDelete("Allergies/{id}")]
        public async Task<ActionResult<int?>> DeletePatientAllergy(int id) =>
            await _patientAllergiesService.Delete(id);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">patientAllergy's Id to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("Allergies/DeleteBatch")]
        public async Task<int[]> DeleteBatchPatientAllergy(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation)
            => await _patientAllergiesService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="patientIds">patientAllergy's Id to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("Allergies/RestoreBatch")]
        public async Task<int[]> RestoreBatchPatientAllergy(ConfirmationNoteDto[] patientIds,
            CancellationToken cancellation)
            => await _patientAllergiesService.RestoreBatch(patientIds, cancellation: cancellation);

        #endregion

        #region Patient Attachment

        /// <summary>
        /// Get list of attachments
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Attachments")]
        public async Task<IPagedResults<PatientAttachmentDto>> GetListFiles(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientsService.ListAttachments(id, query, cancellation);

        /// <summary>
        /// Upload attachment
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="files">List of files to upload</param>
        /// <param name="overrideExisting">Override file if existing</param>
        /// <param name="dto">The dto that holds the patient attachment data, like the document type</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>
        [HttpPost("{id}/Attachments")]
        [DisableRequestSizeLimit]
        [CustomFileSizeLimit]
        public async Task<ActionResult> UploadFiles(int id, [FromForm] List<IFormFile> files, [FromForm] AddPatientAttachmentFormDto dto,
            bool overrideExisting, CancellationToken cancellation)
        {
            var addAttachmentDtoList = new List<AddPatientAttachmentDto>();

            if (dto.Data is null || dto.Data.Length == 0)
            {
                throw new ReadableException($"Missing files. Please add the file(s) to upload.");
            }

            for (var i = 0; i < dto.Data.Length; i++)
            {
                addAttachmentDtoList.Add(new AddPatientAttachmentDto
                {
                    File = files[i],
                    Name = files[i].FileName,
                    DocumentType = dto.Data[i].DocumentType,
                    Date = dto.Data[i].Date
                });
            }

            await _patientsService.UploadFiles(id, addAttachmentDtoList, overrideExisting, cancellation);

            return Ok((FileName: string.Join(", ", files.Select(f => f.Name)), Result: "Ok"));
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("Attachments/{id}/uri")]
        public async Task<ActionResult> GetFileSasUri(int id, CancellationToken cancellation)
        {
            var uri = await _patientsService.GeneratePublicUri(id, cancellation: cancellation);
            return Ok(new { Url = uri.ToString() });
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileName"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("{id}/Attachments/{fileName}/uri")]
        public async Task<ActionResult> GetFileSasUri(int id, string fileName, CancellationToken cancellation)
        {
            var uri = await _patientsService.GeneratePublicUri(id, fileName, cancellation: cancellation);
            return Ok(new { Url = uri.ToString() });
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        [HttpGet("AttachmentsByBlobName/{blobName}/uri")]
        public Task<ActionResult> GetFileSasUri(string blobName)
        {
            var decodedBlobName = Uri.UnescapeDataString(blobName);
            var uri = _patientsService.GeneratePublicUri(decodedBlobName);
            return Task.FromResult(Ok(new { Url = uri.ToString() }) as ActionResult);
        }

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        [HttpPut("AttachmentsByBlobName/uri")]
        public Task<ActionResult> GenerateFileSasUriFromBlobName(BlobItemDto blobName)
        {
            var decodedBlobName = Uri.UnescapeDataString(blobName.Name);
            var uri = _patientsService.GeneratePublicUri(decodedBlobName);
            return Task.FromResult(Ok(new { Url = uri.ToString() }) as ActionResult);
        }

        /// <summary>
        /// Delete attachment
        /// </summary>
        /// <param name="dtoArr"> DeleteStorageFileDto []</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>BlobItemDto</returns>
        [HttpDelete("Attachments/deleteBatch")]
        public async Task<ActionResult<BlobItemDto[]>> DeleteFiles(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation = default) =>
            Ok(await _patientsService.DeleteFiles(dtoArr, cancellation));

        /// <summary>
        /// Restore PatientHousehold batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("Attachments/restoreBatch")]
        public async Task<IList<int>> RestoreAttachmentsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.RestoreFiles(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable Attachments batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("Attachments/enableBatch")]
        public async Task<IList<int>> EnableAttachmentsBatch([FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.EnableFiles(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Disable Attachments batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpPut("Attachments/disableBatch")]
        public async Task<IList<int>> DisableAttachmentsBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.DisableFiles(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Get the Attachment given its Id
        /// </summary>
        /// <param name="id">Patient Attachment Id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpGet("Attachments/{id}")]
        public async Task<ActionResult<PatientAttachmentDto>> GetAttachmentById(int id, CancellationToken cancellation)
        {
            var dto = await _patientsService.GetAttachmentById(id, cancellation: cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get the Attachment given its Id
        /// </summary>
        /// <param name="id">Patient Attachment Id</param>
        /// <param name="dto">UpdatePatientAttachment Dto</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpPut("Attachments/{id}")]
        public async Task<ActionResult<int>> UpdateAttachment(int id,
            UpdatePatientAttachmentDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.UpdatePatientAttachment(dto, cancellation);

        #endregion

        #region Patient Flag
        /// <summary>
        /// Get all flags by patient id
        /// </summary>
        /// <param name="id">Patient id</param>
        /// <param name="query">Sieve model</param>
        /// <returns></returns>
        [HttpGet("{id}/Flags")]
        public Task<IPagedResults<PatientFlagDto>> GetFlagsByPatientId(int id, [FromQuery] SieveModel query) =>
            _patientFlagsService.GetFlagsByPatientId(id, query);
        #endregion

        #region Operations

        /// <summary>
        /// Assign Custodian Gp to multiples patients
        /// </summary>
        /// <param name="dtoArr">Contains id of practitioner and id(s) of Patient(s) to assign or unassign </param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignGp")]
        public async Task<int> AssignCustodianGp(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.AssignCustodianGp(dtoArr, cancellation: cancellation);

        #endregion

        #region Queries

        /// <summary>
        /// Get all Diagnostics with Educational Careplan link
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("{id}/EducationalCPs")]
        public async Task<PatientEducationalCareplanDto[]> GetPatientEducationalCareplans(
            int id, CancellationToken cancellation) =>
            await _patientsService.GetPatientEducationalCareplans(id, cancellation);

        #endregion

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.DisableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.EnableBatch(dtoArr,
                cancellation: cancellation);

        /// <summary>
        /// Get all patients by Custodian Gp or Custodian NP
        /// </summary>
        /// <returns>List of patients</returns>
        /// <param name="query">Sieve model</param>
        /// <param name="cancellation">Cancellation token</param>
        [HttpGet("PatientsByCustodianGpOrCustodianNP")]
        public async Task<IPagedResults<PatientDto>> GetPatientsByCustodianGpOrCustodianNP(
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientsService.GetPatientsByCustodianGp(query, cancellation);

        /// <summary>
        /// Get all patients by Custodian Gp or Custodian NP
        /// </summary>
        /// <returns>List of patients</returns>
        /// <param name="assignmentType">Assignment type</param>
        /// <param name="query">Sieve model</param>
        /// <param name="cancellation">Cancellation token</param>
        [HttpGet("GetPatientsFromAssignedPractitioner/{assignmentType}")]
        public async Task<IPagedResults<PatientDto>> GetPatientsFromAssignedPractitioner(
            int assignmentType,
            [FromQuery] SieveModel query,
            CancellationToken cancellation) =>
            await _patientsService.GetPatientsFromAssignedPractitioner(assignmentType, query, cancellation);

        /// <summary>
        /// Retrieve all generated letters that belongs to a patient
        /// </summary>
        /// <param name="id">Patient's ID</param>
        /// <param name="query">Sieve parameters</param>
        /// <returns>Paged list of PatientGeneratedLetterListDto</returns>
        [HttpGet("{id}/GeneratedLetters")]
        public async Task<IPagedResults<PatientGeneratedLetterListDto>> GetGeneratedLetters(int id,
            [FromQuery] SieveModel query) =>
            await _patientGeneratedLettersService.GetGeneratedLettersByPatientId(id, query);

        /// <summary>
        /// Get Patient Emr GeneratedLetter content by Patient Id
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="generatedLetterId">GeneratedLetter Id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>FileContentResult</returns>
        [HttpGet("{id}/EmrGeneratedLetter/{generatedLetterId}")]
        public async Task<FileContentResult> GetPatientEmrGeneratedLetter(int id, int generatedLetterId, CancellationToken cancellation)
            => await _patientsService.GetPatientEmrGeneratedLetterContent(id, generatedLetterId, cancellation);

        /// <summary>
        /// Get Patient Emr Directory by Patient Id
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="docId">Document Id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>PatientDirectoryDto</returns>
        [HttpGet("{id}/EmrDocument/{DocId}")]
        public async Task<FileContentResult> GetPatientEmrDocumentContent(int id, int docId, CancellationToken cancellation)
            => await _patientsService.GetPatientEmrDocumentContent(id, docId, cancellation);

        /// <summary>
        /// Retrieve all emr document that belongs to a patient
        /// </summary>
        /// <param name="id">Patient's ID</param>
        /// <param name="groupFolderId">Group Folder's Id</param>
        /// <param name="query">Sieve parameters</param>
        /// <returns>Paged list of PatientDocumentFolderDto</returns>
        [HttpGet("{id}/PatientDocumentByGroupFolderList/{groupFolderId}")]
        public async Task<IPagedResults<PatientDocumentFolderListDto>> GetPatientEmrDocumentsByGroupFolder(int id, int groupFolderId,
            [FromQuery] SieveModel query) =>
            await _patientsService.GetPatientEmrDocumentsByGroupFolder(id, groupFolderId, query);

        /// <summary>
        /// Retrieve all emr document that belongs to a patient
        /// </summary>
        /// <param name="id">Patient's ID</param>
        /// <param name="folderId">Folder's Id</param>
        /// <param name="query">Sieve parameters</param>
        /// <returns>Paged list of PatientDocumentFolderDto</returns>
        [HttpGet("{id}/PatientDocumentByFolderList/{folderId}")]
        public async Task<IPagedResults<PatientDocumentFolderListDto>> GetPatientEmrDocumentsByFolder(int id, int folderId,
            [FromQuery] SieveModel query) =>
            await _patientsService.GetPatientEmrDocumentsByFolder(id, folderId, query);

        /// <summary>
        /// Synchronize Patient information with accuro
        /// </summary>
        /// <param name="patientIds"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("SynchronizePatient")]
        public async Task<DeferredRequestResponse> SynchronizePatient(int[] patientIds, CancellationToken cancellation)
            => await _patientsService.SynchronizePatients(patientIds, cancellation: cancellation);

        #region Patient Paneling

        /// <summary>
        /// Accept External Patient(s)
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("AcceptPrepanelingPatient")]
        public async Task<int> AcceptExternalPatients(BaseUpdateDto[] dtoArr, CancellationToken cancellation)
            => await _patientsService.AcceptPrepanelingPatients(dtoArr, cancellation);

        /// <summary>
        /// Request Chart Updated
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("RequestChartUpdate")]
        public async Task<int> RequestChartUpdate(BaseUpdateDto[] dtoArr, CancellationToken cancellation)
            => await _patientsService.RequestChartUpdate(dtoArr, cancellation);

        /// <summary>
        /// Get ineligible patients based on the provided query.
        /// </summary>
        /// <param name="query">The SieveModel query.</param>
        /// <returns>The paged results of ineligible patients.</returns>
        [HttpGet("IneligiblePatients")]
        public async Task<IPagedResults<PatientDto>> GetIneligiblePatients([FromQuery] SieveModel query)
            => await _patientsService.GetIneligiblePatients(query);

        /// <summary>
        /// Get ineligible patients based on the provided query.
        /// </summary>
        /// <param name="query">The SieveModel query.</param>
        /// <returns>The paged results of ineligible patients.</returns>
        [HttpGet("PrePanelingPatients")]
        public async Task<IPagedResults<PrepanelingPatientDto>> GetPrePanelingPatients([FromQuery] SieveModel query)
            => await _patientsService.GetPrePanelingPatients(query);

        #endregion

        /// <summary>
        /// Check if patient has questions when finalizing paneling
        /// </summary>
        /// <returns></returns>
        [HttpGet("{id}/HasDraftClinicalQuestion")]
        public async Task<ActionResult<PatientClinicalQuestionCountDto>> HasDraftQuestions(int id, CancellationToken cancellation) =>
            await _patientsService.HasDraftQuestions(id, cancellation);

        /// <summary>
        /// Get Patient Goals
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Goals")]
        public async Task<IPagedResults<PatientGoalDto>> GetGoalsByPatientId(int id, [FromQuery] SieveModel query,
            CancellationToken cancellation) => await _patientGoalsService.GetGoalsByPatientId(id, query, cancellation);

        /// <summary>
        /// Update Patient ChartNotes
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("{id}/ChartNotes")]
        public async Task<ActionResult<UpdatePatientChartNotesDto>> PutPatientChartNotes(
            int id,
            UpdatePatientChartNotesDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientsService.UpdatePatientChartNotes(dto,
                    cancellation: cancellation);

        /// <summary>
        /// Get Patient ChartNotes by id
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("{id}/ChartNotes")]
        public async Task<PatientChartNotesDto> GetPatientChartNotes(int id, CancellationToken cancellation)
            => await _patientsService.GetPatientChartNotes(id, cancellation);

        /// <summary>
        /// Get Patient Reminders
        /// </summary>
        /// <param name="id">Entity id</param>
        /// <param name="query"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>
        [HttpGet("{id}/Reminders")]
        public async Task<IPagedResults<EntityReminderWithActionsDto>> GetReminders(int id, [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientsService.GetReminders(id, query, cancellation);

        /// <summary>
        /// Add Reminder
        /// </summary>
        /// <param name="id">Entity id</param>
        /// <param name="addDto">Add dto</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Response</returns>
        [HttpPost("{id}/Reminders")]
        public async Task<AddKatanaEntityReminderWithActionsDto> AddReminder(int id, AddKatanaEntityReminderWithActionsDto addDto, CancellationToken cancellation) =>
            await _patientsService.AddReminder(id, addDto, cancellation);

        /// <summary>
        /// Send Calendar
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the TaxCreditLetter(s) to submit it with the status</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Treatmentplan dtos</returns>
        [HttpPut("SendCalendar")]
        public async Task<int> SendCalendar(BaseUpdateDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.SendCalendar(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Mark patients as ineligible
        /// </summary>
        /// <param name="patients">Array of patients to mark as ineligible</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Number of patients marked as ineligible</returns>
        [HttpPut("MarkAsIneligible")]
        public async Task<int> MarkAsIneligible(ConfirmationNoteDto[] patients, CancellationToken cancellation)
        => await _patientsService.MarkAsIneligible(patients, cancellation);

        /// <summary>
        /// Set AllowEmailCommunication property for the specified DTO array.
        /// </summary>
        /// <param name="dtoArr">Array of UpdateFlagDto</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Number of affected records</returns>
        [HttpPut("SetEmailCommunication")]
        public async Task<int> SetEmailCommunication(BaseUpdateDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.ToggleEmailCommunication(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Retrieves a summary for a specific patient.
        /// </summary>
        /// <param name="id">Unique identifier of the patient.</param>
        /// <param name="cancellation">Token to monitor for cancellation requests.</param>
        /// <returns></returns>
        [HttpGet("{id}/PatientAiSummary")]
        public async Task<ActionResult<PatientSummaryDto>> PatientSummary(int id,
            CancellationToken cancellation)
        {
            var dto = await _patientsService.GetPatientSummary(id, cancellation: cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        #region Household

        /// <summary>
        /// Get all PatientsHousehold of specific Patient
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query"></param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("{id}/PatientHouseholds")]
        public async Task<IPagedResults<PatientHouseholdDto>> GetPatientHouseholds(int id, [FromQuery] SieveModel query, CancellationToken cancellation)
            => await _patientsService.GetPatientHouseholdsFromPatient(id, query, cancellation);

        /// <summary>
        /// Get available Patients To Patient's Household
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query"></param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("{id}/PatientsToHousehold")]
        public async Task<IPagedResults<PatientCommonDto>> GetPatientsToHousehold(int id, [FromQuery] SieveModel query, CancellationToken cancellation)
            => await _patientsService.GetPatientsToHousehold(id, query, cancellation);

        /// <summary>
        /// Add patients(family members) to Patient's Household
        /// </summary>
        /// <param name="id"></param>
        /// <param name="addDtos"></param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpPost("{id}/PatientsHouseholds")]
        public async Task<ActionResult<AddPatientHouseholdDto[]>> AddPatientsToHousehold(int id, AddPatientHouseholdDto[] addDtos, CancellationToken cancellation)
            => await _patientsService.AddPatientsToHousehold(id, addDtos, cancellation);

        /// <summary>
        /// Get Patients that can belong to Household
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query"></param>
        /// <param name="cancellation"></param>
        /// <returns>List</returns>
        [HttpGet("{id}/PatientsThatCanBelongToHousehold")]
        public async Task<IPagedResults<PatientCommonDto>> GetPatientsThatCanBelongToHousehold(int id, [FromQuery] SieveModel query, CancellationToken cancellation)
            => await _patientsService.GetPatientsWithSameAddress(id, query, cancellation);

        /// <summary>
        /// Get PatientHouseholdDto by id
        /// </summary>
        /// <param name="id">Id of the entity</param>
        /// <param name="cancellation"></param>
        /// <returns>Dto</returns>
        [HttpGet("PatientHouseholds/{id}")]
        public async Task<ActionResult<PatientHouseholdDto>> GetPatientHousehold(int id, CancellationToken cancellation)
        {
            var dto = await _patientHouseholdService.GetById(id, cancellation);
            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="dto">Dto's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// /// <returns>Dto</returns>
        [HttpPut("PatientHouseholds/{id}")]
        public async Task<ActionResult<UpdatePatientHouseholdDto>> UpdatePatientHousehold(int id, UpdatePatientHouseholdDto dto, CancellationToken cancellation) =>
            id != dto.Id
                ? BadRequest()
                : await _patientHouseholdService.UpdatePatientHousehold(dto, cancellation: cancellation);

        /// <summary>
        /// Delete PatientHousehold batch entities
        /// </summary>
        /// <param name="id">Patient Id</param>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("{id}/PatientHouseholds/deleteBatch")]
        public async Task<int[]> DeletePatientHouseholdBatch(int id, ConfirmationNoteDto[] dtoArr, CancellationToken cancellation)
        {
            await _patientsService.RemovePatientsFromPatientHousehold(id, dtoArr, cancellation);
            return await _patientHouseholdService.DeleteBatch(dtoArr, cancellation: cancellation);
        }

        /// <summary>
        /// Restore PatientHousehold batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("PatientHouseholds/restoreBatch")]
        public async Task<int[]> RestorePatientHouseholdBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientHouseholdService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable PatientHousehold batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("PatientHouseholds/enableBatch")]
        public async Task<int[]> EnablePatientHouseholdBatch([FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientHouseholdService.EnableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Disable PatientHousehold batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpPut("PatientHouseholds/disableBatch")]
        public async Task<int[]> DisablePatientHouseholdBatch(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientHouseholdService.DisableBatch(dtoArr, cancellation: cancellation);

        #endregion Alias

        #region Alias

        /// <summary>
        /// Get Aliases
        /// </summary>
        /// <param name="id">Patients's ID</param>
        /// <param name="query">Sieve parameters</param>
        /// <returns>Paged list of PatientsDto</returns>
        [HttpGet("{id}/aliases")]
        public async Task<IPagedResults<AliasDto>>
            GetAliases(int id, [FromQuery] SieveModel query) =>
            await _patientsService.GetAllAliasesByEntityId(query, id);

        /// <summary>
        /// Add Alias
        /// </summary>
        /// <param name="addAliasDto">Add Alias Dto</param>
        /// <returns></returns>
        [HttpPost("alias")]
        public async Task<AddAliasDto>
            AddAlias([FromBody] AddAliasDto addAliasDto) =>
            await _patientsService.AddAlias(addAliasDto);


        /// <summary>
        /// Convert To Alias
        /// </summary>
        /// <param name="convertAliasDtos">Convert Alias Dto</param>
        /// <returns></returns>
        [HttpPut("convertToAlias")]
        public async Task<ConvertAliasDto[]> ConvertToAlias(
            [FromBody] ConvertAliasDto[] convertAliasDtos) =>
            await _patientsService.ConvertToAlias(convertAliasDtos);


        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("deleteBatchAliases")]
        public async Task<int[]> DeleteAliases([FromBody] ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.DeleteBatchAliases(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to retsore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("restoreBatchAliases")]
        public async Task<int[]> RestoreAliases([FromBody] ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.RestoreBatchAliases(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Disable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>

        [HttpPut("DisableBatchAliases")]
        public async Task<int[]> DisableBatchAliases(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.DisableBatchAliases(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatchAliases")]
        public async Task<int[]> EnableBatchAliases(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _patientsService.EnableBatchAliases(dtoArr,
                cancellation: cancellation);

        #endregion

        #region Patient Clinical Question Round

        /// <summary>
        /// Get list of attachments for the Ask Question Action
        /// </summary>
        /// <param name="id">Patient's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/AttachmentsForAskQuestion")]
        public async Task<IPagedResults<PatientAttachmentCQRoundDto>> GetAttachmentsForAskQuestion(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _patientsService.GetAttachmentsForAskQuestion(id, query, cancellation);

        /// <summary>
        /// Start a new round for the selected patient using the corresponding attachments
        /// </summary>
        /// <param name="id">Patient Id</param>
        /// <param name="dtoArr">Patient Attachments Ids to be used</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns></returns>
        [HttpPost("{id}/AskQuestion")]
        public async Task<ActionResult<BaseUpdateDto>> AskQuestion(int id,
            BaseUpdateDto[] dtoArr, CancellationToken cancellationToken = default)
        {
            await _patientsService.AskQuestion(id, dtoArr.Select(x => x.Id)
                .ToList(), cancellationToken);
            return StatusCode(StatusCodes.Status201Created, dtoArr);
        }

        #endregion

        /// <summary>
        /// Moves patient to OCR Pending
        /// </summary>
        /// <param name="patientPoolItemsDtos">Patient's id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpPut("MovePatientToOCRPendingForTests")]
        public async Task<int> MovePatientToOCRPendingForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) =>
            await _patientsService.MovePatientToOCRPendingForTests(patientPoolItemsDtos, cancellation);

        /// <summary>
        /// OCR Documents
        /// </summary>
        /// <param name="patientPoolItemsDtos">Patient's id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpPut("OCRPatientDocumentsForTests")]
        public async Task<int> OCRPatientDocumentsForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) =>
            await _patientsService.OCRPatientDocumentsForTests(patientPoolItemsDtos, cancellation);

        /// <summary>
        /// Generates AI Questions for the patient
        /// </summary>
        /// <param name="patientPoolItemsDtos">Patient's id</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpPut("GeneratePatientsAIQuestionsForTests")]
        public async Task<int> GeneratePatientsAIQuestionsForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) =>
            await _patientsService.GeneratePatientsAIQuestionsForTests(patientPoolItemsDtos, cancellation);

        /// <summary>
        /// Pickup patient
        /// </summary>
        /// <param name="dtoArr">Contains the identification of the careplan(s) assigned by the use to review it</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("PickUp")]
        public async Task<List<int>> PickUpCareplanByUser(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.PickUp(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Realese patient
        /// </summary>
        /// <param name="dtoArr">Release the identification of the careplan(s) assigned by the use to review it</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Careplan dtos</returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("Release")]
        public async Task<List<int>> ReleasePatient(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation) =>
            await _patientsService.ReleasePatient(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Update Last Contacted Date
        /// </summary>
        /// <param name="dtoArr"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpPut("UpdateLastContacted")]
        public async Task<int> UpdateLastContactedDate(UpdatePatientLastContactedDto[] dtoArr, CancellationToken cancellation)
            => await _patientsService.UpdateLastContactedDate(dtoArr, cancellation);

        /// <summary>
        /// GetPagedEntityChanges
        /// </summary>
        /// <param name="id"></param>
        /// <param name="filters"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("Audit/{id?}")]
        public async Task<IPagedResults<ListEntityChangeLogDto>> GetPagedEntityChanges(int? id,
            [FromQuery] ChangeLogQuery filters, CancellationToken cancellation) =>
            await _auditStorageProvider.GetPagedEntityChanges<Patient>(id, filters, cancellation);

        /// <summary>
        /// GetPagedEntityChanges
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="endDate"></param>
        /// <param name="cancellation"></param>
        /// <param name="startDate"></param>
        /// <returns></returns>
        [HttpGet("TimeLine/{entityId}")]
        public async Task<EntityChangesDto> GetPagedEntityChanges(int entityId,
            DateTime? startDate = null, DateTime? endDate = null,
            CancellationToken cancellation = default) =>
            await _auditStorageProvider.GetEntityChanges<Patient>(entityId,
            startDate, endDate, cancellation);

        /// <summary>
        /// GetSnapShot
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        [HttpGet("SnapShot/{entityId}")]
        public async Task<EntitySnapshotDto> GetSnapShot(int entityId,
            CancellationToken cancellation = default)
        {
            var entity = new Patient { Id = entityId };
            var date = DateTime.UtcNow;
            return await _auditStorageProvider.GetEntitySnapshot(entity, date, cancellation);
        }
    }
}