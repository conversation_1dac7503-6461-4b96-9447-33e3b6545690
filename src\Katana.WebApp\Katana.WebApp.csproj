﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <TypeScriptToolsVersion>Latest</TypeScriptToolsVersion>
    <TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
    <IsPackable>false</IsPackable>
    <SpaRoot>ClientApp\</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>
    <!--<ApplicationInsightsResourceId>/subscriptions/97ad3d4f-d17b-40a0-af19-b03e36c72203/resourceGroups/katana-rg-01/providers/microsoft.insights/components/satori-katana</ApplicationInsightsResourceId>-->
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <ActiveDebugProfile>Katana.WebApp</ActiveDebugProfile>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <AnalysisLevel>6.0-recommended</AnalysisLevel>
  </PropertyGroup>
  <ItemGroup>
    <Using Include="Katana.Core.Entities.User" Alias="User" />
    <Using Include="Aoshield.Services.Core.Search.Entities.Alias" Alias="Alias" />
    <!--Data Access-->
    <Using Include="Aoshield.Core.DataAccess" />
    <!--Sieve-->
    <Using Include="Sieve.Models.SieveModel" Alias="SieveModel" />
    <Using Include="Aoshield.Core.DataAccess.Sieve" />
    <!--Models-->
    <Using Include="Aoshield.Core.Entities.Models" />
    <!--Actions-->
    <Using Include="Aoshield.Core.EntityActions" />
    <!--Merge-->
    <Using Include="Aoshield.Core.Services.Merge" />
  </ItemGroup>
  <ItemGroup>
    <!-- Don't publish the SPA source files, but do show them in the project files list -->
    <Compile Remove="Automapping\**" />
    <Compile Remove="Common\**" />
    <Compile Remove="Permission\**" />
    <Content Remove="Automapping\**" />
    <Content Remove="Common\**" />
    <Content Remove="Permission\**" />
    <EmbeddedResource Remove="Automapping\**" />
    <EmbeddedResource Remove="Common\**" />
    <EmbeddedResource Remove="Permission\**" />
    <None Remove="$(SpaRoot)**" />
    <None Remove="Automapping\**" />
    <None Remove="Common\**" />
    <None Remove="Permission\**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
    <Content Remove="ClientApp\public\locales\en\_entities.json" />
    <None Include="microservices.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libcrypto.1.0.0.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Core.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5DBus.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Gui.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Multimedia.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5MultimediaWidgets.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Network.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5OpenGL.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Positioning.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5PrintSupport.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Qml.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Quick.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Sensors.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Sql.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Svg.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5WebChannel.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5WebKit.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5WebKitWidgets.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libQt5Widgets.5.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\libssl.1.0.0.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\Syncfusion.WebKitWrapper">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\platforms\libqcocoa.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\platforms\libqminimal.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\platforms\libqoffscreen.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqdds.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqgif.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqicns.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqico.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqjp2.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqjpeg.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqmng.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqsvg.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqtga.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqtiff.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqwbmp.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\QtBinariesMac\imageformats\libqwebp.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\wkhtmltopdf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\wkhtmltopdf.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\wkhtmltox.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML.Extensions.WebApi" Version="0.5.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.8.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Katana.Services\Katana.Services.csproj" />
  </ItemGroup>
  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <!-- As part of publishing, ensure the JS resources are freshly built in production mode -->
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />
    <!-- Include the newly-built files in the publish output -->
    <ItemGroup>
      <DistFiles Include="$(SpaRoot)build\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>%(DistFiles.Identity)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>
</Project>
