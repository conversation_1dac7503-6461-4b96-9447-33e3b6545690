import { ProtectedResource } from "Components/Common/Hooks/ResourceProtected/types";

// Modules types
//

/**
 * ModulesId
 */
export enum ModulesId {
    DEFAULT = "default",
    STAFF = "staff",
}

/**
 * Define Module type
 */
export type ModuleType = {
    id: number;
    name: string;
    sitemap: UrlData[];
    menuMap: MenuItem[];
    urlsMap: UrlMap;
    roleBasedProtected?: ProtectedResource[];
    excludedRoles?: ProtectedResource[];
    // disable module access during impersonation session
    disableOnImpersonation?: boolean;
    // determine if the module is the default module
    isDefaultModule?: boolean;
};

// Site map types
/**
 * Describe page protection props
 */
export type PageProtection = {
    roleBasedProtected?: ProtectedResource[];
    excludedRoles?: ProtectedResource[];
    operator?: OperatorType;
};

/**
 * Describe page props
 */
export type PageData = {
    // friendly name
    name?: string;
    // slug: reserved for tab slug text
    slug?: string;
    // slug: reserved for page description text
    description?: string;
    // list of roles base needed to access current page
    roleBasedProtected?: ProtectedResource[];
    // list of roles exclude to access current page
    excludedRoles?: ProtectedResource[];
};

/**
 * Describe page url info for routes propose
 */
export type UrlData = {
    // unique page id
    id: string;
    // page url
    href?: string;
    // page data
    pageData?: PageData;
    // component
    component?: JSX.Element;
    //child
    childRoutes?: Omit<UrlData, "childRoutes">[];
};

export type UrlMap = {
    [id: string]: UrlData;
};

export type OperatorType = "OR" | "AND";

/**
 * Pages ids
 */
export enum pagesId {
    HOME = "Home",
    API = "Api",
    ACCURO = "Accuro",
    ACCURO_CALLBACK = "Accuro-callback",
    FAXES = "Faxes",
    FAXES_DETAILS = "Faxes-details",
    FAXES_ASSIGNMENT_PLAN = "Faxes-assignment-plan",
    TRANSCRIBER = "Transcriber",
    TRANSCRIBER_MANAGERS = "Transcriber-managers",
    TRANSCRIBER_GROUPS = "Transcriber-groups",
    TRANSCRIBER_GROUPS_CONFIG = "Transcriber-groups-config",
    HPUS = "hpus",
    HPU_DETAILS = "hpus-details",
    HPU_ASSIGNED = "hpus-assigned",
    HPU_OPERATION_ASSIGNED = "hpus-operation-assigned",
    CARE_PLANS = "eConsults",
    CARE_PLANS_DETAILS = "eConsults-details",
    ECONSULTS_OPERATION_DETAILS = "eConsults-operation-details",
    ECONSULTS_SP_DETAILS = "eConsults-sp-details",
    ECONSULTS_GP_DETAILS = "eConsults-gp-details",
    ECONSULTS_ITP_DETAILS = "eConsults-itp-details",
    CARE_PLAN_RESPONSE_TEMPLATE = "eConsult-response-templates",
    EDUCATIONAL_CARE_PLANS = "Educational-care-plans",
    EDUCATIONAL_CARE_PLANS_DETAILS = "Educational-care-plans-details",
    DIAGNOSES = "Diagnoses",
    DIAGNOSES_DETAILS = "Diagnoses-details",
    DIAGNOSES_ALIASES = "Diagnoses-aliases",
    REFERRALS = "Referrals",
    REFERRALS_DETAILS = "referrals-details",
    REFERRALS_SPECIALISTS = "Referral-specialists",
    PATIENTS = "Patients",
    PATIENTS_DETAILS = "patients-details",
    PATIENTS_REQUEST_DETAILS = "request-details",
    ECONSULTS_GP_REQUEST_DETAILS = "gp-request-details",
    ECONSULTS_SP_REQUEST_DETAILS = "sp-request-details",
    ECONSULTS_ITP_REQUEST_DETAILS = "itp-request-details",
    PATIENT_PANELING = "Patient-paneling",
    PATIENT_PANELING_DETAILS = "patient-paneling-details",
    PATIENTS_PANELING_REQUEST_DETAILS = "patients-request-details",
    PATIENTS_DETAILS_SUMMARY = "patients-details-summary",
    PATIENTS_HISTORY_TYPE = "patients-history-type",
    UNITS_OF_MEASUREMENT = "Units-of-measurement",
    SOCIAL_HISTORIES = "Social-histories",
    VITAL_SIGNS = "Vital-signs",
    ALLERGIES = "Allergies",
    NOTIFICATIONS_TEMPLATES = "Notification-templates",
    ECC_DAILY_METRICS = "Ecc-daily-metrics",
    INVOICES = "Invoices",
    INVOICE_ITEMS = "Invoices-items",
    INVOICE_ALL_ITEMS = "Invoices-all-items",
    BULK_VALIDATIONS = "Bulk-validations",
    DEFERRED_REQUEST = "Deferred-requests",
    DEFERRED_MERGES = "Deferred-merges",
    DEFERRED_All_MERGES = "Deferred-all-merges",
    USER_INVITES = "Invite-users",
    IMPORT_EVENTS = "Imported-data",
    IMPORT_EVENT_DETAILS = "Imported-data-details",
    IMPORT_EVENT_ENTITY_DISPLAY_NAME = "Imported-data-entity-display-name",
    REPORTS = "Reports",
    USERS = "Users",
    CLINICS = "Clinics",
    CLINICS_DETAILS = "clinics-details",
    CLINIC_PRACTITIONERS = "clinic-practitioners",
    PRACTITIONERS = "Practitioners",
    PRACTITIONER_DETAILS = "practitioner-details",
    PRACTITIONER_SPECIALTIES = "practitioner-specialties",
    PRACTITIONER_DIAGNOSTICS = "practitioner-diagnostics",
    PRACTITIONER_ALIASES = "practitioner-aliases",
    SPECIALTIES = "Specialties",
    SPECIALTIES_DETAILS = "Specialties-details",
    MEDICATIONS = "Medications",
    COUNTRIES = "Countries",
    PROVINCES = "Provinces",
    EXPORTS = "Exported-data",
    AUTHORIZED_USERS = "Authorized-users",
    GROUPS = "Group",
    GROUP_DETAILS = "group-details",
    GROUPS_MEMBER = "Group-members",
    USER_MEMBERSHIP = "User-membership",
    INQUIRY_FORMS = "Inquiry-forms",
    INQUIRY_FORMS_DETAILS = "Inquiry-forms-details",
    INQUIRY_FORM_STEPS = "Inquiry-form-steps",
    INQUIRY_FORM_STEPS_DETAILS = "Inquiry-form-steps-details",
    INQUIRY_FORM_QUESTIONS = "Inquiry-Form-Questions",
    DEAD_LETTER_MESSAGES = "Dead-Letter_Messages",
    BILLING_SETTINGS = "Billing-Settings",
    BULK_SEARCH_INDEX_BUILDS = "Bulk-search-index-builds",
    GROUP_LAB_TESTS = "Group-lab-tests",
    LAB_TESTS = "Lab-tests",
    GROUP_ITEMS = "Group-items",
    GROUP_FOLDERS = "Group-folders",
    FOLDERS = "Folders",
    PATIENT_STATUSES = "Patient-statuses",
    FLAGS = "Flags",
    CLINICAL_QUESTIONS = "Clinical-questions",
    GOAL_ITEMS = "Goal-items",
    PATIENT_POOLS = "Patient-pools",
    REMINDERS = "Reminders",
    ROLES = "Roles",
    REPORTS_SETTINGS = "Reports-management",
    PATIENT_EMR_SYNC_RECORDS = "Patient-EMR-Sync-Records",
    DECLINE_REASON = "DECLINE_REASON",
    AI_PROMPT_DEFINITIONS = "ai-prompts",
    HPCS = "hpcs",
    HPCS_DETAILS = "hpcs-details",
    QUALITY_REVIEW_HPC = "eConsults-quality-review-hpc",
    QUALITY_REVIEW_HPC_PATIENT_DETAILS = "eConsults-quality-review-hpc-patient-details",
    QUALITY_REVIEW_HPC_CAREPLAN_DETAILS = "eConsults-quality-review-hpc-econsult-details",
    QUALITY_REVIEW_HPC_REQUEST_DETAILS = "eConsults-quality-review-hpc-request-details",
    QUALITY_REVIEW_MVCA = "eConsults-quality-review-mvca",
    QUALITY_REVIEW_MVCA_PATIENT_DETAILS = "eConsults-quality-review-mvca-patient-details",
    QUALITY_REVIEW_MVCA_CAREPLAN_DETAILS = "eConsults-quality-review-mvca-econsult-details",
    QUALITY_REVIEW_MVCA_REQUEST_DETAILS = "eConsults-quality-review-mvca-request-details",
    ECONSULTS_GP = "eConsults-gp",
    ECONSULTS_SP = "eConsults-sp",
    ECONSULTS_ITP = "eConsults-itp",
    ECONSULTS_OPERATION = "eConsults-operation",
    ENTITY_CHANGE_LOGS = "entity-change-logs",
}

// Menu map types
//
/**
 * Describe submenu item
 */
export type SubmenuItem = {
    // submenu item unique key
    key: string;
    // submenu item label
    text: string;
    // submenu item tooltip
    tooltip?: string;
    // submenu item href to navigate if clicked
    href: string;
    // roles based authorization to be accomplished to view the submenu item
    roleBasedProtected?: ProtectedResource[];
    // roles based authorization to be excluded to view the submenu item
    excludedRoles?: ProtectedResource[];
    // menu item's child
    subItems?: SubmenuItem[];
};

/**
 * Describe menu item
 */
export type MenuItem = {
    // menu item unique key
    key: string;
    // menu item label
    text: string;
    // menu item tooltip
    tooltip?: string;
    // menu item icon
    icon: JSX.Element;
    // menu item href to navigate if clicked
    href?: string;
    // roles based authorization to be accomplished to view the menu item
    roleBasedProtected?: ProtectedResource[];
    // roles based authorization to be excluded to view the submenu item
    excludedRoles?: ProtectedResource[];
    // menu item's child
    subItems?: SubmenuItem[];
    // operator AND or OR for checkAbilities
    operator?: OperatorType;
};

export type MenuMap = {
    [id: string]: MenuItem;
};

/**
 * Unique menu keys. by convention start with '_' character
 */
export enum menuKeys {
    HOME = "_home",
    FAXES = "_faxes",
    ECONSULTS = "_econsults",
    REFERRALS = "_referrals",
    PATIENTS = "_patients",
    PATIENTS_MANAGE = "_patients-manage",
    PATIENTS_PANELING = "_patients-paneling",
    NP_METRICS = "_np_metrics",
    BILLING = "_billing",
    IMPORTS_EXPORTS = "_imports-exports",
    REPORTING = "_reporting",
    SETTINGS = "_settings",
    NOTIFICATIONS_TEMPLATES = "_notification-templates",
    HEALTH_CARE = "_health-care",
    INQUIRY_FORM = "_inquiry_form",
    REMINDERS = "_reminders",
    ADMIN = "_admin",
}

/**
 * Unique submenu keys.
 */
export enum SubMenuKeys {
    ACCURO_SUBMENU = "accuro",
    FAXES_SUBMENU = "faxes",
    TRANSCRIBER_SUBMENU = "transcribers",
    TRANSCRIBER_MANAGERS_SUBMENU = "transcriber-managers",
    TRANSCRIBER_GROUPS_SUBMENU = "transcriber-groups",
    HPUs_SUBMENU = "hpus",
    CARE_PLANS_SUBMENU = "econsults-submenu",
    CARE_PLANS = "econsults",
    EDUCATIONAL_CARE_PLANS_SUBMENU = "educational-care-plans",
    CARE_PLAN_RESPONSE_TEMPLATE_SUBMENU = "econsult-response-templates",
    DIAGNOSES_SUBMENU = "diagnoses",
    DIAGNOSES_ALIASES_SUBMENU = "diagnoses-aliases",
    REFERRALS_SUBMENU = "referrals",
    REFERRALS_SPECIALISTS_SUBMENU = "referral-specialists",
    PATIENTS_SUBMENU = "patients",
    UNITS_OF_MEASUREMENT_SUBMENU = "units-of-measurement",
    SOCIAL_HISTORIES = "social-histories",
    VITAL_SIGNS_SUBMENU = "vital-signs",
    ALLERGIES_SUBMENU = "allergies",
    ECC_DAILY_METRICS_SUBMENU = "ecc-daily-metrics",
    INVOICES_SUBMENU = "invoices",
    INVOICE_ITEMS_SUBMENU = "invoice-items",
    DEFERRED_REQUEST_SUBMENU = "deferred-requests",
    DEFERRED_MERGES_SUBMENU = "deferred-merges",
    DEFERRED_All_MERGES_SUBMENU = "deferred-all-merges",
    DEFERRED_USERINVITES_SUBMENU = "invite-users",
    BULK_VALIDATIONS_SUBMENU = "bulk-validations",
    BULK_VALIDATIONS = "Bulk-validations",
    IMPORT_EVENTS_SUBMENU = "import-events",
    EXPORTS_SUBMENU = "exports",
    REPORTS_SUBMENU = "reports",
    USERS_SUBMENU = "users",
    CLINICS_SUBMENU = "clinics",
    PRACTITIONERS_SUBMENU = "practitioners",
    SPECIALTIES_SUBMENU = "specialties",
    MEDICATIONS_SUBMENU = "medications",
    COUNTRIES_SUBMENU = "countries",
    PROVINCES_SUBMENU = "provinces",
    NOTIFICATIONS_TEMPLATES_SUBMENU = "notification-templates",
    AUTHORIZED_USERS = "authorized-users",
    HEALTH_CARES_HPU = "health-cares-hpu",
    GROUPS_SUBMENU = "groups",
    INQUIRY_FORMS_SUBMENU = "inquiry-forms",
    DEAD_LETTER_MESSAGES_SUBMENU = "dead-letter-messages",
    BILLING_SETTINGS = "billing-settings",
    PROFILE = "profile",
    BULK_SEARCH_INDEX_BUILD_SUBMENU = "bulk-search-index-builds",
    BULK_SEARCH_INDEX_BUILD = "bulk-search-index-builds",
    GROUP_ITEMS = "group-items",
    GROUP_LAB_TESTS = "group-lab-tests",
    LAB_TESTS_SUBMENU = "lab-tests",
    PATIENT_HISTORY_TYPES_SUBMENU = "patient-history-types",
    PATIENT_PANELING = "patient-paneling",
    GROUP_FOLDERS = "group-folders",
    FOLDERS_SUBMENU = "folders",
    PATIENT_STATUSES_SUBMENU = "patient-statuses",
    FLAGS_SUBMENU = "flags",
    CLINICAL_QUESTIONS_SUBMENU = "clinical-questions",
    GOAL_ITEMS_SUBMENU = "goal-items",
    PATIENT_POOLS_SUBMENU = "patient-pools",
    REMINDERS_SUBMENU = "reminders",
    ROLES_SUBMENU = "roles",
    DECLINE_REASON_SUBMENU = "decline-reason",
    MANAGE_USERS_SUBMENU = "manage-users",
    DIAGNOSES_SETTINGS = "diagnoses-settings",
    ECONSULT_SETTINGS = "econsult-settingss",
    PATIENT_CHART_SETTINGS = "patient-chart-settings",
    LOCATIONS = "locations",
    TEMPLATES = "templates",
    INTEGRATIONS = "integrations",
    MERGE_DATA = "merge-data",
    DEAD_LETTER_MESSAGES = "dead_letter_messages",
    HPU = "_hpu",
    AI_PROMPT_DEFINITIONS_SUBMENU = "ai-prompts",
    HPCs_SUBMENU = "HPCs",
    QUALITY_REVIEW_HPC_SUBMENU = "quality-review-hpc",
    QUALITY_REVIEW_MVCA_SUBMENU = "quality-review-mvca",
    ECONSULTS_GP_SUBMENU = "eConsults-gp",
    ECONSULTS_SP_SUBMENU = "eConsults-sp",
    ECONSULTS_ITP_SUBMENU = "eConsults-itp",
    ECONSULTS_OPERATION_SUBMENU = "eConsults-operation",
    CHANGELOGS_SUBMENU = "changelogs",
}
