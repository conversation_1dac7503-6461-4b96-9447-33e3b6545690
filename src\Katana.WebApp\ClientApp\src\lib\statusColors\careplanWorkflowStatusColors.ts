import {
    CareplanFilterableStatuses,
    CareplanWorkflowStatus,
} from "../models/Careplans/CareplanWorkflowStatus";
import {
    BlueStatus,
    CyanStatus,
    DeepOrange,
    GreenStatus,
    RedStatus,
    YellowStatus,
    GreyStatus,
    OrangeStatus,
} from "./allStatusColors";

export const CareplanWorkflowStatusColors = new Map<CareplanWorkflowStatus, string>();
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.BeingCreated, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Draft, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.PendingReview, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.ITPPending, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.SPPending, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.GpPending, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.SPApproved, GreenStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Closed, GreyStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Billed, GreenStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Unanswered, RedStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Proposed, YellowStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.Declined, RedStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.GpApproved, GreenStatus);
CareplanWorkflowStatusColors.set(CareplanWorkflowStatus.ITPApproved, CyanStatus);

export const CPSWorkflowStatusColorMap = new Map<(typeof CareplanFilterableStatuses)[number], string>([
    ["BeingCreated", RedStatus],
    ["Draft", BlueStatus],
    ["ITPPending", DeepOrange],
    ["GpPending", CyanStatus],
    ["SPPending", BlueStatus],
    ["SPApproved", GreenStatus],
    ["PendingReview", YellowStatus],
    ["Unanswered", RedStatus],
    ["ITPApproved", CyanStatus],
    ["Proposed", YellowStatus],
    ["Declined", RedStatus],
    ["GpApproved", GreenStatus],
    ["ITPApproved", YellowStatus],
]);

export const ECCWorkflowStatusColorMap = new Map<(typeof CareplanFilterableStatuses)[number], string>([
    ["BeingCreated", YellowStatus],
    ["ITPPending", DeepOrange],
    ["GpPending", OrangeStatus],
    ["SPPending", YellowStatus],
    ["PendingReview", CyanStatus],
    ["SPApproved", GreenStatus],
    ["Unanswered", RedStatus],
    ["ITPApproved", CyanStatus],
]);
