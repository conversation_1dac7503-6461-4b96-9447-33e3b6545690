﻿using Aoshield.Core.Entities.Models;
using Katana.Services.PatientAllergies;
using Katana.Services.PatientAllergies.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// PatientAllergy CRUD service
    /// </summary>
    public class PatientAllergiesServiceTest : IPatientAllergiesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientAllergyDto> Add(AddPatientAllergyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientAllergyDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientAllergyDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<PatientAllergyDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientAllergyDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new PatientAllergyDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdatePatientAllergyDto> Update(UpdatePatientAllergyDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientAllergyDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Custom

        /// <summary>
        /// For retrieve all Allergies that belongs to an Patient
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<IPagedResults<PatientAllergyDto>> GetAllergiesByPatientId(int patientId,
            SieveModel query) =>
            await Task.Run(() => new PagedResults<PatientAllergyDto>([],
                default, default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public Task SynchronizePatientAllergies(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        #endregion
    }
}
