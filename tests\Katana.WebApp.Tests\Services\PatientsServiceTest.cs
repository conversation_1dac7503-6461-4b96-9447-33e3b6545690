﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Core.Services.Audit;
using Aoshield.Core.Services.Reminder.Entities;
using Aoshield.Core.Services.Reminder.Models;
using Aoshield.Core.Services.Reminder.Notification;
using Aoshield.Core.Validation;
using Aoshield.Services.Core.Search.Models;
using Katana.Core.Entities;
using Katana.Services.Attachment.Models;
using Katana.Services.Careplans.Models;
using Katana.Services.Common.Models;
using Katana.Services.PatientDocumentFolders.Models;
using Katana.Services.PatientHouseholds.Models;
using Katana.Services.PatientPoolItems.Models;
using Katana.Services.Patients;
using Katana.Services.Patients.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Patient CRUD service
    /// </summary>
    public class PatientsServiceTest : IPatientsService
    {
        /// <inheritdoc />
        public IAuditStorageProvider AuditStorageProvider { get; }

        /// <inheritdoc />
        public IKrudder<User> Krudder { get; }

        ///<inheritdoc/>
        public FluentValidation.IValidator<Patient> Validator =>
            throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientDto> Add(AddPatientDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<PatientDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new PatientDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdatePatientDto> Update(UpdatePatientDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientDto() { Id = dto.Id });

        /// <inheritdoc/>
        public async Task<UpdatePatientIntakeDto> UpdatePatientIntake(UpdatePatientIntakeDto dto,
            CancellationToken cancellation = default)
            => await Task.Run(() => new UpdatePatientIntakeDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<UpdatePatientMedicationsDto> UpdatePatientMedications(
            UpdatePatientMedicationsDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientMedicationsDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<UpdatePatientDiagnosesDto> UpdatePatientDiagnoses(
            UpdatePatientDiagnosesDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientDiagnosesDto() { Id = dto.Id });

        ///<inheritdoc/>
        public Task<List<int>> PickUp(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<List<int>> ReleasePatient(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddPatientNoteDto> AddNote(AddPatientNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientNoteDto());

        ///<inheritdoc/>
        public async Task<PatientNoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new PatientNoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientNoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<PatientNoteDto>([], default, default, default, default,
                default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdatePatientNoteDto> UpdateNote(UpdatePatientNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Patient> ConfigureSet(IQueryable<Patient> _, Type _1, ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Patient _, IList<Patient> _1, string _2, Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Attachments

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientAttachmentDto>>
            ListAttachments(int Id, SieveModel query, CancellationToken cancellation) => await Task.Run(
            () => new PagedResults<PatientAttachmentDto>([], default, default, default,
                default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<BlobItemDto> DownloadFile(int id, CancellationToken cancellation) =>
            await Task.Run(() => new BlobItemDto()
            {
                Id = id,
                Content = new MemoryStream(),
                ContentType = "application/octet-stream"
            });

        /// <inheritdoc />
        public async Task<IList<int>> DeleteFiles(ConfirmationNoteDto[] confirmationNotes,
            CancellationToken cancellation = default) => await Task.Run(() => confirmationNotes.Select(dto => dto.Id).ToList());

        /// <inheritdoc />
        public Task<IList<int>> RestoreFiles(ConfirmationNoteDto[] confirmationNotes,
            CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IList<int>> EnableFiles(ConfirmationNoteDto[] confirmationNotes, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IList<int>> DisableFiles(ConfirmationNoteDto[] confirmationNotes,
            CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<Uri> GeneratePublicUri(int id, DateTimeOffset? expiresOn = null, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Uri GeneratePublicUri(string blobName, DateTimeOffset? expiresOn = null) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<List<PatientAttachmentDto>> UploadFiles(int id, List<IFormFile> files,
            bool overrideExisting, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        #endregion

        #region Operations

        ///<inheritdoc/>
        public Task<int> AssignCustodianGp(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> InviteBatch(int[] patientIds,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        #endregion

        #region Queries

        /// <inheritdoc />
        public Task<PatientEducationalCareplanDto[]> GetPatientEducationalCareplans(int patientId, CancellationToken cancellation = default) => Task.Run(() =>
        {
            return new List<PatientEducationalCareplanDto>().ToArray();
        });

        /// <inheritdoc />
        public Task<MedicationPatientDto> GetMedicationByPatientId(int id, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        /// <inheritdoc />
        public Task<IPagedResults<PatientDto>> GetPatientsByCustodianGp(SieveModel query,
                CancellationToken cancellation = default) => throw new NotImplementedException();

        #region  Accuro

        /// <inheritdoc />
        public Task<FileContentResult> GetPatientEmrDocumentContent(int patientId, int documentId, CancellationToken cancellation = default) => throw new NotImplementedException();
        /// <inheritdoc />
        public Task<DirectoryDto> GetPatientEmrDirectoryByGroupFolder(int patientId, int groupFolderId, CancellationToken cancellation = default) => throw new NotImplementedException();
        /// <inheritdoc />
        public Task<DirectoryDto> GetPatientEmrDirectoryByFolder(int patientId, int folderId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientDocumentFolderListDto>> GetPatientEmrDocumentsByGroupFolder(int patietnId, int groupFolderId, SieveModel query, CancellationToken cancellationToken = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientDocumentFolderListDto>> GetPatientEmrDocumentsByFolder(int patietnId, int folderId, SieveModel query, CancellationToken cancellationToken = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> SynchronizePatients(int[] patientIds, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> SynchronizeEMRNomenclators(CancellationToken cancellation = default) =>
            Task.Run(() => new DeferredRequestResponse() { Sections = [new() { Name = "Onboarding EMR Nomenclator", Details = [] }] });

        /// <inheritdoc />
        public Task<DeferredRequestResponse> OnboardingPractitioners(CancellationToken cancellation = default) =>
            Task.Run(() => new DeferredRequestResponse() { Sections = [new() { Name = "Onboarding Practitioner", Details = [] }] });

        /// <inheritdoc />
        public Task<DeferredRequestResponse> OnboardingHealthQuestPractitioners(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> OnboardingMedAccessPractitioners(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> OnboardingAvaPractitioners(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<FileContentResult> GetPatientEmrGeneratedLetterContent(int patientId, int generatedLetterId, CancellationToken cancellationToken = default) => throw new NotImplementedException();

        #endregion

        #region Patient Paneling

        /// <inheritdoc />
        public Task<int> HPCFinalizePaneling(BaseUpdateDto[] data, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> GetNextPanelingPatientId(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetPoolPatients(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetMyPoolPatients(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> GeneratePanelingPool(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetPatientPanelingByCustodianGP(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> GPFinalizePaneling(BaseUpdateDto[] data, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForHpcLead(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForHpc(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientPoolItemDto>> GetCompletedPatientsForGp(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<List<int>> ReleasePatientPoolItem(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> SendBack(ConfirmationNoteDto[] data, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> SendBackAll(ConfirmationNoteDto[] data, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PatientClinicalQuestionCountDto> HasDraftQuestions(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientDto>> GetIneligiblePatients(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        /// <inheritdoc />
        public Task<IPagedResults<BaseStatusLogEntityDto<PatientWorkflowStatus>>> GetWorkflowStatusLogsAsPagedResults(int entityId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<Patient> UpdatePatientDemographicInformation(EMRPatientDto patientDto, int patientId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task SynchronizeLastAppointmentDate(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task LogSyncPatientError(int synchronizeUserId, int patientId, string endpointName, string message, string exception, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> UploadEncounterNotes(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task SynchronizePatientEncountersNotes(int patientId, bool updatePatientFlag = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientDto>> GetPatientsFromAssignedPractitioner(int assignmentType, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> GenerateByPatientPool(PatientPool patientPool, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<UpdatePatientChartNotesDto> UpdatePatientChartNotes(UpdatePatientChartNotesDto dto, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PatientChartNotesDto> GetPatientChartNotes(int patientId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<AddKatanaEntityReminderWithActionsDto> AddReminder(int id, AddKatanaEntityReminderWithActionsDto addDto, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<EntityReminderWithActionsDto>> GetReminders(int id, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PrepanelingPatientDto>> GetPrePanelingPatients(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> OnboardingExternalPractitioners(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> AcceptPrepanelingPatients(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> SendCalendar(BaseUpdateDto[] dtoArr, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<(bool success, int migratedConditionsCount)> SyncPatientHistoryItemsFromConditions(int patientId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<bool> OcrPatientAIDocuments(Patient patient, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task BulkPopulateOCRDocument(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> MarkAsIneligible(ConfirmationNoteDto[] data, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int> ToggleEmailCommunication(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task SendPatientToAttentionAsync(Reminder<User> reminder, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task Handle(ReminderProcessingRequest<User> notification, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> MakePatientsAIFlowReady(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// inheritdoc/>
        Task<Dictionary<string, string>> IPatientsService.ProcessOCRPendingDocuments(CancellationToken cancellation) => throw new NotImplementedException();

        /// inheritdoc/>
        public Task<DeferredRequestResponse> ProcessOCRPendingDocumentsOld(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PatientSummaryDto> GetPatientSummary(int patientId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<ConvertAliasDto[]> ConvertToAlias(ConvertAliasDto[] dtos,
            CancellationToken cancellation = default) => Task.FromResult(dtos);

        /// <inheritdoc />
        public Task<int[]> DeleteBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos, CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> RestoreBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            CancellationToken cancellation = default) => Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public async Task<IPagedResults<AliasDto>> GetAllAliasesByEntityId(SieveModel query, int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<AliasDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc />
        public Task<AddAliasDto[]> AddBatchAliases(AddAliasDto[] dtos,
            CancellationToken cancellation = default) => Task.FromResult(dtos);

        /// <inheritdoc />
        public Task<AddAliasDto>
            AddAlias(AddAliasDto dto, CancellationToken cancellation = default) =>
            Task.FromResult(dto);

        /// <inheritdoc />
        public Task<int[]> DisableBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatchAliases(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.FromResult(confirmationNoteDtos.Select(c => c.Id).ToArray());

        /// <inheritdoc />
        public Task<IList<Alias>> GetAllAliasesByEntityId(int id, CancellationToken cancellation = default) =>
            Task.FromResult(new List<Alias>() as IList<Alias>);

        /// <inheritdoc />
        public Task<IPagedResults<AliasDto>> GetAllAliasesByEntity(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />

        public Task<IPagedResults<PatientCommonDto>> GetPatientsToSpecificHousehold(int householdId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientHouseholdDto>> GetPatientHouseholdsFromPatient(int patientId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientCommonDto>> GetPatientsToHousehold(int patientId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<AddPatientHouseholdDto[]> AddPatientsToHousehold(int patientId, AddPatientHouseholdDto[] addDtos, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientCommonDto>> GetPatientsWithSameAddress(int patientId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> RemovePatientsFromPatientHousehold(int patientId, ConfirmationNoteDto[] dtos, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<List<PatientAttachmentDto>> UploadFiles(int id, List<AddPatientAttachmentDto> attachments, bool overrideExisting, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<List<PatientAttachmentDto>> UploadFiles(int id, List<AddPatientAttachmentFromBlobDto> attachments, bool overrideExisting, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<CQPatientPoolItemWorkflowStatus> OcrPatientDocuments(int patientPoolItemId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task MigrateAttachments(int? take, string practitionerIds = null, int[] patientIds = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PatientAttachmentDto> UploadFile(int id, AddPatientAttachmentFromBlobDto blobItem, bool overrideExisting, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task PopulateFirstCQRoundData(int[] patientsId = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task MoveWorkflowStatusesTOPatientPoolItems(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<IPagedResults<PatientAttachmentCQRoundDto>> GetAttachmentsForAskQuestion(int patientId, SieveModel query,
            CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        /// <inheritdoc />
        public Task AskQuestion(int patientId, List<int> attachmentsIds, CancellationToken cancellationToken = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<PatientAttachmentDto> GetAttachmentById(int id, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> UpdatePatientAttachment(UpdatePatientAttachmentDto dto,
            CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<bool> GeneratePatientsAIQuestions(int patientPoolItemId, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<Uri> GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task MovePatientToOCRPendingForTests(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task OCRPatientDocumentsForTests(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task GeneratePatientsAIQuestionsForTests(int patientId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> MovePatientToOCRPendingForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> OCRPatientDocumentsForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> GeneratePatientsAIQuestionsForTests(ConfirmationNoteDto[] patientPoolItemsDtos, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> RequestChartUpdate(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<DeferredRequestResponse> OnboardingAccuroExternalPractitioners(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int> UpdateLastContactedDate(UpdatePatientLastContactedDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();
    }
}
