﻿using Aoshield.Core.Validation;
using Bogus;
using Katana.Core.Entities;
using Katana.Core.Enums;

namespace Katana.Tests.Common.Utils.Seeders
{
    /// <summary>
    /// Careplans Data Seeder.
    /// </summary>
    public partial class TestDataSeeder
    {
        /// <summary>
        /// Seed Careplans entities.
        /// </summary>
        /// <param name="qty"></param>
        /// <param name="skipLast"></param>
        /// <param name="factory"></param>
        public void SeedCareplans(int? qty = null, int? skipLast = null, Func<IList<Careplan>> factory = null)
        {

            var clinicId = Krudder.Set<Clinic>().OrderByDescending(c => c.Id)
             .Skip(skipLast ?? 2)  // SkipLast is a valid Linq expression, but it throws an exception with EF Core, at least with SQLite as provider
             .Select(c => c.Id)
             .ToList();

            var patientId = Krudder.Set<Patient>().OrderByDescending(p => p.Id)
             .Skip(skipLast ?? 2)  // SkipLast is a valid Linq expression, but it throws an exception with EF Core, at least with SQLite as provider
             .Select(p => p.Id)
             .ToList();

            var practitionerId = Krudder.Set<Practitioner>().OrderByDescending(p => p.Id)
             .Skip(skipLast ?? 2)  // SkipLast is a valid Linq expression, but it throws an exception with EF Core, at least with SQLite as provider
             .Select(p => p.Id)
             .ToList();

            var diagnosticId = Krudder.Set<Diagnostic>().OrderByDescending(d => d.Id)
            .Skip(skipLast ?? 2)  // SkipLast is a valid Linq expression, but it throws an exception with EF Core, at least with SQLite as provider
            .Select(d => d.Id)
            .ToList();

            var specialtyId = Krudder.Set<ReferralSpecialist>().OrderByDescending(s => s.Id)
            .Skip(skipLast ?? 2)  // SkipLast is a valid Linq expression, but it throws an exception with EF Core, at least with SQLite as provider
            .Select(s => s.Id)
            .ToList();

            var _factory = factory ?? (() =>
                new Faker<Careplan>()
                .RuleFor(c => c.CareplanImportId, x => x.UniqueIndex)
                .RuleFor(c => c.ClinicId, x => clinicId[GetRandomId(clinicId.Count - 1)])
                .RuleFor(c => c.Diagnostic1Id, x => diagnosticId[GetRandomId(diagnosticId.Count - 1)])
                .RuleFor(c => c.RequestDate, x => x.Date.Future().ToUniversalTime())
                .RuleFor(c => c.PatientId, x => patientId[GetRandomId(patientId.Count - 1)])
                .RuleFor(c => c.GpId, x => practitionerId[GetRandomId(practitionerId.Count - 1)])
                .RuleFor(c => c.SpecialtyId, x => specialtyId[GetRandomId(specialtyId.Count - 1)])
                .RuleFor(c => c.ItpApprovedDate, x => x.Date.Future().ToUniversalTime())
                .RuleFor(c => c.ResponseDate, x => x.Date.Future().ToUniversalTime())
                .RuleFor(c => c.RequestDate, x => x.Date.Recent().ToUniversalTime())
                .RuleFor(c => c.BilledDate, x => x.Date.Future().ToUniversalTime())
                .RuleFor(c => c.CasesNotes, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                .RuleFor(c => c.Recommendations, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
                .RuleFor(c => c.ValidatedAt, x => x.Date.Recent().ToUniversalTime())
                .RuleFor(c => c.ProcessingType, (f, x) =>
                {
                    return ProcessingType.CPS;
                })
                .RuleFor(c => c.ProcessingLoop, (f, x) =>
                {
                    return x.ProcessingType.Equals(ProcessingType.ECC)
                        ? f.PickRandom(new ProcessingLoop[] { ProcessingLoop.Hmed, ProcessingLoop.OtherClinic })
                        : ProcessingLoop.Unknown;
                })
               .RuleFor(c => c.ConsultationReason, x => x.Random.ClampString(x.Lorem.Paragraphs(1), StringLengths.Max))
               .Generate(qty ?? GetEntityQty<Careplan>()));

            SeedEntity(_factory);
        }
    }
}
