﻿using Aoshield.Core.Entities.Models;
using Katana.Services.PatientMedications;
using Katana.Services.PatientMedications.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// PatientMedication CRUD service
    /// </summary>
    public class PatientMedicationsServiceTest : IPatientMedicationsService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddPatientMedicationDto> Add(AddPatientMedicationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddPatientMedicationDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientMedicationDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<PatientMedicationDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<PatientMedicationDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new PatientMedicationDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdatePatientMedicationDto> Update(UpdatePatientMedicationDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdatePatientMedicationDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<IPagedResults<PatientMedicationDto>> GetMedicationsByPatientId(int patientId, SieveModel query) =>
            await Task.Run(() =>
                new PagedResults<PatientMedicationDto>([], default,
                    default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public Task SyncPatientMedications(int patientId, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion
    }
}
