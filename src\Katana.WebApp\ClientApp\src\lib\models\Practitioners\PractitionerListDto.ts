import { ApprovalPeriodOptions } from "lib/models/ApprovalPeriodOptions";
import { QuotaUnit } from "lib/models/QuotaUnit";
import { SortCriteriaOptions } from "lib/models/SortCriteriaOptions";
import { PractitionerCommonActionDto } from "./PractitionerCommonActionDto";
import { ActionableDto } from "../Common/ActionableDto";

export interface PractitionerListDto extends ActionableDto, PractitionerCommonActionDto {
    practId: number | null;
    sp: boolean;
    np: boolean;
    itp: boolean;
    gpsiItp: boolean;
    gpsi: boolean;
    spItp: boolean;
    hpcLead: boolean;
    cps: boolean;
    externalBilling: boolean;
    rp: boolean;
    requestingQuota: number | null;
    requestingQuotaUnit: QuotaUnit | null;
    approvingQuota: number | null;
    approvingQuotaUnit: QuotaUnit | null;
    notesCount: number;
    approvalPeriodOption: ApprovalPeriodOptions | null;
    approvalPeriod: number | null;
    patientPanelingSortField: string;
    dailyRCodeEarningsCap: number | null;
    patientPanelingSortCriteria: SortCriteriaOptions;
    searchIndex: string;
    publicCalendarUrl: string;
    maxDailyQuestionsPerPatient: number | null;
}
