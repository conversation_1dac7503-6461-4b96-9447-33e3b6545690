import { Link, useTheme } from "@mui/material";
import { KHtmlTooltip } from "Components/Common/KHtmlTooltip";
import { KTooltipStatusLabel } from "Components/Common/Status/KStatusLabel";
import { Status } from "lib/models/Status";
import {
    KActionsProps,
    KMUIDataTableColumn,
    KMUIDataTableColumnOptions,
    KMUIDataTableFilters,
    KMUIDataTableInputFilterColumnOptions,
    KMUIDataTableInputFilterType,
    KMUIDataTablePagingOptions,
    KMUIDataTableSelectFilterColumnOptions,
    KMUIDataTableSelectFilterType,
    KMUIDataTableServerSideSelectFilterColumnOptions,
    KMUIDataTableServerSideSelectFilterType,
    KMUIDataTableYesNoFilterColumnOptions,
    KMUIDataTableYesNoFilterType,
    MUIDataTableSelectRows,
    stickyColumnsWidth,
} from "lib/muiDataTable/types";
import { merge } from "lodash";
import moment from "moment";
import {
    Display,
    FilterType,
    MUIDataTableColumn,
    MUIDataTableMeta,
    MUIDataTableOptions,
    MUIDataTableState,
    MUISortOptions,
    SelectableRows,
} from "mui-datatables";
import { Dispatch, SetStateAction, useCallback, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "@tanstack/react-query";
import {
    useKatanaSnackAndNotifyError,
    useKatanaSnackAndNotifySuccess,
} from "../../Components/Common/Hooks/useKatanaSnackNotify";
import { displayAutocomplete } from "../../Components/Common/KMUIDataTable/KAutocompleteFilterWidget";
import { getDisplayContains } from "../../Components/Common/KMUIDataTable/KContainsFilterWidget";
import { displayDateRange } from "../../Components/Common/KMUIDataTable/KDateRangeFilterWidget";
import { customFilterDialogFooter } from "../../Components/Common/KMUIDataTable/KFilterDialogFooter";
import { displayNumberRange } from "../../Components/Common/KMUIDataTable/KNumberRangeFilterWidget";
import KTableActions from "../../Components/Common/KMUIDataTable/KTableActions";
import { displayUseQueryAutocomplete } from "../../Components/Common/KMUIDataTable/KUseQueryAutocompleteFilterWidget";
import { useKatanaApiClient } from "../../Components/Common/Providers/KatanaApiClientProvider";
import { useKatanaAuth } from "../../Components/Common/Providers/KatanaAuthProvider";
import { KatanaDefaultThemeSpacingFactor } from "../../Components/Themes";
import { AppRoles } from "../auth/types";
import { currentUserHasRole } from "../auth/userUtils";
import { IApiClient } from "../katanaApiClient/IApiClient";
import { ApiErrorCode, KatanaApiError } from "../katanaApiClient/types";
import { BaseEntityDto } from "../models/BaseEntityDto";
import { ExportEntityKeys } from "../models/Export/ExportEntityKeys";
import { PagedResults } from "../models/PagedResults";
import { TrueFalseStatus } from "../models/TrueFalseStatus";
import { ReactQueryKeys } from "../reactQuery/reactQueryKeys";
import {
    getAvailableActions,
    getEnumKeys,
    KATANA_EMPTY_FIELD,
    KATANA_SHORT_DATE_FORMAT,
    nameofFactory,
} from "../utensils";
import { ActionableActionProp } from "Components/Common/Hooks/Actions/types";
import { KRichTooltipProps } from "Components/Common/Hooks/useRenderRichTooltip";
import { displayRadioButtons } from "Components/Common/KMUIDataTable/KRadioButtonsFilterWidget";
import { displayCheckboxes } from "Components/Common/KMUIDataTable/KCheckboxesFilterWidget";
import { KTrueFalseIcon } from "Components/Common/KTrueFalseIcon/KTrueFalseIcon";

// Alias type to avoid duplicating this signature
type CustomFilterFn = (name: string, values: string[]) => string[];

/**
 * Build filters in Sieve format
 *
 * @param showDeleted Whether to show deleted records
 * @param filters MUIDataTable filters
 * @returns Sieve filters
 */
export const buildSieveFilters = (showDeleted: boolean, filters?: KMUIDataTableFilters): string => {
    if (!filters) {
        return "";
    }
    let formattedFilters: string[] = [];
    for (const key in filters) {
        if (!filters[key] || !filters[key].values.length) {
            continue;
        }
        // Escape commas and pipes in filter values by adding backslashes
        const actualFilterValues = filters[key].values.map((value) => value.replace(/([|,])/g, "\\$1"));

        switch (filters[key].type) {
            case KMUIDataTableSelectFilterType.Autocomplete:
                formattedFilters = formattedFilters.concat(
                    filters[key].customFilter
                        ? (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                        : [`${key}==${actualFilterValues.map((v) => v.split(":")[0]).join("|")}`]
                );
                break;
            case KMUIDataTableSelectFilterType.Checkboxes:
                formattedFilters = formattedFilters.concat(
                    filters[key].customFilter
                        ? (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                        : [`${key}==${actualFilterValues.map((v) => v.split(":")[0]).join("|")}`]
                );
                break;
            case KMUIDataTableSelectFilterType.RadioButtons:
                formattedFilters = formattedFilters.concat(
                    filters[key].customFilter
                        ? (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                        : [`${key}==${actualFilterValues.join("|")}`]
                );
                break;
            case KMUIDataTableYesNoFilterType.YesNo:
                formattedFilters = formattedFilters.concat(
                    filters[key].customFilter
                        ? (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                        : [
                              `${key}==${actualFilterValues
                                  .map((v) => (v === "True" ? 0 : "0|1"))
                                  .join("|")}`,
                          ]
                );
                break;
            case KMUIDataTableInputFilterType.Contains:
                formattedFilters = formattedFilters.concat(
                    filters[key].customFilter
                        ? (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                        : [`${key}@=${actualFilterValues[0]}`]
                );
                break;
            case KMUIDataTableInputFilterType.Equals:
                if (filters[key].customFilter) {
                    formattedFilters = formattedFilters.concat(
                        (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                    );
                } else {
                    if (actualFilterValues[0]) {
                        formattedFilters.push(`${key}=${actualFilterValues[0]}`);
                    }
                }
                break;
            case KMUIDataTableInputFilterType.NumberRange:
                if (filters[key].customFilter) {
                    formattedFilters = formattedFilters.concat(
                        (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                    );
                } else {
                    if (actualFilterValues[0]) {
                        formattedFilters.push(`${key}>=${actualFilterValues[0]}`);
                    }
                    if (actualFilterValues.length > 1 && actualFilterValues[1]) {
                        formattedFilters.push(`${key}<=${actualFilterValues[1]}`);
                    }
                }
                break;
            case KMUIDataTableInputFilterType.DateRange:
                if (filters[key].customFilter) {
                    formattedFilters = formattedFilters.concat(
                        (filters[key].customFilter as CustomFilterFn)(key, actualFilterValues)
                    );
                } else {
                    if (actualFilterValues[0]) {
                        formattedFilters.push(`${key}>=${actualFilterValues[0]}`);
                    }
                    if (actualFilterValues.length > 1 && actualFilterValues[1]) {
                        formattedFilters.push(`${key}<=${actualFilterValues[1]}`);
                    }
                }
                break;
            default:
                break;
        }
    }
    let deletedFilter = "";
    if (!showDeleted || !filters[`${nameOf("softDeleteLevel")}`]) {
        deletedFilter = `${nameOf("softDeleteLevel")}==0`;
    }
    let filtersStr = formattedFilters.join();
    filtersStr = filtersStr ? `${filtersStr},${deletedFilter}` : deletedFilter;
    return filtersStr;
};

/**
 * Generates a string representation of the sorting order for a sieve function.
 *
 * @param sortOrder MUIDataTable Sorts - The sorting order object containing `name` and `direction` properties.
 * @param alternativeSortName - Sieve CustomSort Method Name . An alternative name to use for sorting if provided.
 * @returns Sieve Sorts
 */
export const buildSieveSorts = (sortOrder?: MUISortOptions, alternativeSortName?: string): string => {
    return sortOrder && sortOrder.name
        ? `${sortOrder.direction === "desc" ? "-" : ""}${alternativeSortName ?? sortOrder.name}`
        : "";
};

const nameOf = nameofFactory<BaseEntityDto>();

/**
 *
 * @param filtersList Processes the filters. Creates the KMUIDataTableFilters structure which is used to buildSieveFilters
 * @param columns Columns
 * @param pagingOptions Paging options
 * @param setPagingOptions Function for setting the Paging options
 * @param showDeleted Whether to show deleted records
 */
const processFilters = (
    filtersList: string[][],
    columns: KMUIDataTableColumn[],
    pagingOptions: KMUIDataTablePagingOptions,
    setPagingOptions: Dispatch<SetStateAction<KMUIDataTablePagingOptions>>,
    showDeleted: boolean
) => {
    const filters: KMUIDataTableFilters = {};
    // Target the column to filter on.
    columns.forEach((c, i) => {
        const col = c as KMUIDataTableColumn;
        if (!col || col.filterType === undefined) {
            return;
        }
        if (filtersList[i]?.length) {
            filters[col.name] = {
                type: col.filterType,
                values: filtersList[i],
                customFilter: col.customFilter,
            };
        }
    });
    const newFilters = buildSieveFilters(showDeleted, filters);
    if (pagingOptions.filters !== newFilters) {
        setPagingOptions({
            ...pagingOptions,
            ...{ page: 1, filtersList: filters, filters: newFilters },
        });
    }
};

/** MUI DataTable Page Sizes (rows per page) */
export const muiDataTablePageSizes = {
    s: 10,
    m: 20,
    l: 50,
    xl: 100,
};

/**
 * Calculates the PageSize based on the viewport height
 *
 * @returns page size
 */
export const getDynamicPageSize = (): number => {
    if (window.innerHeight >= 2000) {
        return muiDataTablePageSizes.xl;
    } else if (window.innerHeight >= 1000) {
        return muiDataTablePageSizes.l;
    } else if (window.innerHeight >= 700) {
        return muiDataTablePageSizes.m;
    }

    return muiDataTablePageSizes.s;
};

export interface KMUIDataTableOptions {
    options: MUIDataTableOptions;
    columns: KMUIDataTableColumn[];
    exportCallback?: () => void;
    isDownloading: boolean;
    rowsSelected: number[];
    setRowsSelected: React.Dispatch<React.SetStateAction<number[]>>;
    displayColumns: {
        [name: string]: Display | undefined;
    };
}

/** action definition for Katana tables (select toolbar) */
export interface KTableAction {
    /** Action's icon */
    icon: React.ReactNode;

    /** Action's tooltip */
    tooltip: string;

    /** Action's tooltip as a React.Element */
    customTooltip?: KRichTooltipProps;

    /** Handler for the click event for multiple action */
    onClick?: (selectedRows: any[], confirmationMessage?: string, onSettled?: () => void) => void;

    /** Action's text */
    text: string;

    /** Whether the action is only valid for single selection or not (default is multi selection) */
    singleAction?: boolean;

    /** Whether the action will be hidden from single row options */
    hideOnSingle?: boolean;

    /** Whether the action is disabled or not (default is enabled) */
    disabled?: boolean;

    /** Whether the action is hidden or not (default is visible) */
    hidden?: boolean;

    /** Whether the action will be always visible or not */
    sticky?: boolean;

    /** Confirmation message that we show on dialog */
    confirmationMessage?: string;

    /** */
    onSettled?: () => void;
}

export type KTableActionable = KTableAction & ActionableActionProp;

export type OverrideRowItemColors = {
    active?: string | { color: string };
    disabled?: string | { color: string };
    deleted?: string | { color: string };
};

const stickyColumnStyles = {
    whiteSpace: "nowrap",
    position: "sticky",
};

/**
 * Get an MUIDataTableOptions object with some useful defaults and server-side
 * pagination support.
 *
 * @param pagingOptions pagination options
 * @param setPagingOptions setPagingOption method (this should be a useState set method)
 * @param columns List columns. Filters will not work if this parameter is not passed
 * @param data PagedResults object
 * @param rowActions Row actions definition containing columnName and getMenu function
 * @param rowActions.columnName Name of the field used to deconstruct the Actions row's value. Default is "id"
 * @param rowActions.columnTitle Actions row's name. Default is " "
 * @param rowActions.getActions Get row actions definition
 * @param selectableRows selectableRows option. Used to calculate Actions column left. Default value is "multiple"
 * @param tablePagingOptionsChangeCallback function that is called when table options have changed
 * @param showDeleted Whether to show deleted records
 * @param exportQueryAttachment Attachment get endpoint action
 * @param currentClient CurrentClient
 * @param exportEntity Export entity name
 * @param statusLogCallback callbackFunction to link for IStatusLog Entities
 * @param disableStatusColumn show status log column (default true)
 * @param hideChips Hide filter Chips
 * @param overrideRowItemColors Override row item colors
 * @returns MUIDataTableOptions object
 */
export const useDefaultMUIDataTableOptions = <TDto extends BaseEntityDto>(
    pagingOptions: KMUIDataTablePagingOptions,
    setPagingOptions: Dispatch<SetStateAction<KMUIDataTablePagingOptions>>,
    columns: KMUIDataTableColumn[],
    data?: PagedResults<TDto>,
    rowActions?: KActionsProps<TDto>,
    selectableRows?: SelectableRows,
    tablePagingOptionsChangeCallback?: (tablePagingOptions: KMUIDataTablePagingOptions) => void,
    showDeleted?: boolean,
    exportQueryAttachment?: string,
    currentClient?: IApiClient,
    exportEntity?: ExportEntityKeys,
    statusLogCallback?: (entityId: number) => void,
    disableStatusColumn?: boolean,
    hideChips?: boolean,
    overrideRowItemColors?: OverrideRowItemColors
): KMUIDataTableOptions => {
    // Query client
    const queryClient = useQueryClient();

    // Katana Api client
    const katanaApiClient = useKatanaApiClient();

    const theme = useTheme();
    const katanaAuth = useKatanaAuth();
    const { t } = useTranslation();

    // Notifications
    const snackAndNotifyError = useKatanaSnackAndNotifyError();
    const snackAndNotifySuccess = useKatanaSnackAndNotifySuccess();

    const [isDownloading, setIsDownloading] = useState(false);

    //used to set selected records
    const [rowsSelected, setRowsSelected] = useState<number[]>([]);

    showDeleted =
        showDeleted === undefined &&
        katanaAuth.currentUser &&
        currentUserHasRole(katanaAuth.currentUser, AppRoles.Application_Administrators)
            ? true
            : false;

    // used to dynamically calculate the actions column width.
    // The setCellHeaderProps callback is fired after the last customBodyRender, so
    // we don't need to use state here to force a render. If a new version of the MUIDataTable
    // changes this callback sequence, change useRef by useState.
    const stickyActionsLength = useRef(0);
    const moreActionsLength = useRef(0);

    const getClassName = useCallback(
        (entity: BaseEntityDto) => {
            return !entity
                ? ""
                : entity.softDeleteLevel !== 0
                ? overrideRowItemColors?.deleted ?? "deleted"
                : entity.status === Status.Enabled
                ? overrideRowItemColors?.active ?? "active"
                : overrideRowItemColors?.disabled ?? "disabled";
        },
        [overrideRowItemColors]
    );

    const setCellsProps = (_: string, rowIndex: number) => {
        const entity = data?.items[rowIndex] as BaseEntityDto;
        return {
            className: getClassName(entity),
        };
    };

    //reordering columns so we place sticky columns first and add styles
    const stickyColumns: MUIDataTableColumn[] = [];
    let stickyColumnsCurrentWidth = rowActions ? stickyColumnsWidth.ActionsDefault : 0;

    const displayColumns: {
        [name: string]: Display | undefined;
    } = {};

    //removing softDeleteLevel column in case that exists (we define that column internally)
    columns = columns.filter((c) => c.name !== nameOf("softDeleteLevel"));

    //adding common status column
    !disableStatusColumn &&
        columns.push(
            getKColumn({
                name: nameOf("status"),
                label: `${t(`_entities:baseEntity.${nameOf("status")}`)}`,
                filterOptions: {
                    filterType: KMUIDataTableSelectFilterType.RadioButtons,
                    pagingOptions: pagingOptions,
                    options: getEnumKeys(Status),
                },
                options: {
                    customBodyRender: (_, tableMeta: MUIDataTableMeta) => {
                        const entity = data?.items[tableMeta.rowIndex] as BaseEntityDto;
                        if (entity) {
                            return !statusLogCallback ? (
                                <KTooltipStatusLabel
                                    title={entity.statusNotes ?? ""}
                                    status={entity.status}
                                />
                            ) : (
                                <Link onClick={() => statusLogCallback(entity.id)}>
                                    <KTooltipStatusLabel
                                        title={entity.statusNotes ?? ""}
                                        status={entity.status}
                                    />
                                </Link>
                            );
                        }
                        return KATANA_EMPTY_FIELD;
                    },
                },
            })
        );

    //adding softDeleteLevel column
    columns.push(
        getKColumn({
            name: `${nameOf("softDeleteLevel")}`,
            label: `${t("_common:deleted")}`,
            filterOptions: {
                filterType: KMUIDataTableSelectFilterType.RadioButtons,
                pagingOptions: pagingOptions,
                filterDisplay: showDeleted,
                filterLabel: `${t("_common:hideDeleted")}`,
                options: getEnumKeys(TrueFalseStatus),
                customFilter: (_, values: string[]) => {
                    return [
                        `${nameOf("softDeleteLevel")}==${values
                            .map((v) => (v === TrueFalseStatus.True ? 0 : "0|1"))
                            .join("|")}`,
                    ];
                },
            },
            options: {
                display: showDeleted ? "false" : "excluded",
                customBodyRender: (_, tableMeta: MUIDataTableMeta) => {
                    const entity = data?.items[tableMeta.rowIndex] as BaseEntityDto;
                    if (!!entity) {
                        const deleteLabel = !entity?.softDeleteLevel ? (
                            <KTrueFalseIcon />
                        ) : (
                            <KTrueFalseIcon checked />
                        );
                        return !!entity.deleteNotes ? (
                            <KHtmlTooltip title={`${entity.deleteNotes}`} arrow>
                                <div>{deleteLabel}</div>
                            </KHtmlTooltip>
                        ) : (
                            deleteLabel
                        );
                    }
                    return KATANA_EMPTY_FIELD;
                },
                setCellProps: setCellsProps,
            },
        })
    );

    const nonStickyColumns = columns.filter((c) => {
        c.options = c.options ?? {};
        if (pagingOptions.displayColumns?.[c.name]) {
            c.options.display = pagingOptions.displayColumns[c.name];
        }
        c.options.display ??= "true";
        displayColumns[c.name] = c.options.display;
        c.options.setCellProps = c.options?.setCellProps ?? setCellsProps;
        if (c.sticky && c.sticky > 0 && c.options.display === "true") {
            const left =
                selectableRows !== "none"
                    ? KatanaDefaultThemeSpacingFactor +
                      KatanaDefaultThemeSpacingFactor * stickyColumnsCurrentWidth
                    : 0;
            c.options = {
                ...c.options,
                setCellProps: (_: string, rowIndex: number) => {
                    const entity = data?.items[rowIndex] as BaseEntityDto;
                    return {
                        style: {
                            ...stickyColumnStyles,
                            zIndex: 100,
                            left: `${
                                left +
                                KatanaDefaultThemeSpacingFactor *
                                    (stickyActionsLength.current * 10 +
                                        (moreActionsLength.current ? 10 : 0))
                            }px`,
                        },
                        className: getClassName(entity),
                    };
                },
                setCellHeaderProps: () => {
                    return {
                        style: {
                            ...stickyColumnStyles,
                            zIndex: 101,
                            left: `${
                                left +
                                KatanaDefaultThemeSpacingFactor *
                                    (stickyActionsLength.current * 10 +
                                        (moreActionsLength.current ? 10 : 0))
                            }px`,
                        },
                    };
                },
            };
            stickyColumnsCurrentWidth += c.sticky;
            stickyColumns.push(c);
            return false;
        }
        return true;
    });

    //reordering sticky and non sticky columns
    columns = stickyColumns.concat(nonStickyColumns);

    selectableRows = selectableRows ?? "multiple";

    if (rowActions) {
        const actionColStickyStyles = {
            ...stickyColumnStyles,
            whiteSpace: "nowrap",
            position: "sticky",
            left: `${
                selectableRows !== "none" && data?.items?.length
                    ? KatanaDefaultThemeSpacingFactor * stickyColumnsWidth.ActionsDefault
                    : 0
            }px`,
            width: `${
                KatanaDefaultThemeSpacingFactor *
                (stickyActionsLength.current * 10 + (moreActionsLength.current ? 10 : 0))
            }px`,
            padding: selectableRows !== "none" ? theme.spacing(0, 4) : theme.spacing(0, 2, 0, 4),
        };
        const rowActionsColumn: MUIDataTableColumn = {
            name: rowActions.columnName ?? "id",
            label: rowActions.columnTitle ?? " ",
            options: {
                filter: false,
                sort: false,
                viewColumns: false,
                setCellProps: (_: string, rowIndex: number) => ({
                    style: {
                        ...actionColStickyStyles,
                        zIndex: 100,
                    },
                }),
                setCellHeaderProps: () => ({
                    style: {
                        ...actionColStickyStyles,
                        zIndex: 101,
                    },
                }),
                customBodyRender: (_, tableMeta: MUIDataTableMeta) => {
                    const selectedRows = {
                        data: [
                            {
                                dataIndex: tableMeta.rowIndex,
                            },
                        ],
                    } as MUIDataTableSelectRows;
                    const selectedDataItems = selectedRows.data.map(
                        (x) => data?.items[x.dataIndex] as TDto
                    );
                    const actions = data
                        ? (getAvailableActions(
                              selectedDataItems.map((item) =>
                                  rowActions.getActions(item, selectedDataItems)
                              )
                          ) as KTableAction[])
                        : [];
                    const stickyActions = actions.filter((a) => a.sticky);
                    const moreActions = actions.filter((a) => !a.sticky);

                    // save sticky actions and more actions length to calculate the headers col width
                    stickyActionsLength.current = stickyActions.length;
                    moreActionsLength.current = moreActions.length;

                    return <KTableActions actions={actions} selectedRows={selectedDataItems} />;
                },
            },
        };
        columns.splice(0, 0, rowActionsColumn);
    }

    const exportCallback = () => {
        setIsDownloading(true);
        const url =
            (currentClient?.getBaseUrl() ?? "") +
            (exportQueryAttachment ? `/${exportQueryAttachment}` : "");
        katanaApiClient.Exports.Export(
            {
                endpoint: url,
                entityName: exportEntity,
                columns: columns
                    .map((x) => {
                        return pagingOptions.displayColumns &&
                            pagingOptions?.displayColumns[x.name] === "true"
                            ? { columnName: x.label, dataPath: x.name, extraInfo: x.exportMetadata }
                            : null;
                    })
                    .filter((x) => x),
            },
            pagingOptions
        )
            .then((_: any) => {
                snackAndNotifySuccess(`Export successfully queued`);
                queryClient.invalidateQueries([ReactQueryKeys.Exports]);
            })
            .catch((error: any) => {
                if (error instanceof KatanaApiError) {
                    if (error.response.code === ApiErrorCode.ExportError) {
                        snackAndNotifyError(`${error.response.title} | ${error.response.detail}`);
                    } else {
                        snackAndNotifyError(error.message);
                    }
                }
            })
            .finally(() => {
                setIsDownloading(false);
            });
    };
    const hasCustomExport = currentClient !== undefined && exportEntity !== undefined;

    return {
        options: {
            onViewColumnsChange: (changedColumn: string, action: string) => {
                const index = columns.findIndex((c) => c.name === changedColumn && c.sticky);
                if (index !== -1) {
                    const displayValue = action === "add" ? "true" : "false";
                    columns[index] = {
                        ...columns[index],
                        options: { ...columns[index].options, display: displayValue },
                    };
                }
            },
            // responsive: "simple",
            //
            // Ensures that tables don't overflow, particularly when text wrapping is disabled
            // tableBodyMaxHeight: "499px",
            selectableRows: selectableRows,
            rowsSelected: rowsSelected,
            jumpToPage: true,
            elevation: 0,
            serverSide: true,
            page: pagingOptions.page ? pagingOptions.page - 1 : 0,
            sortOrder: pagingOptions.sortOrder,
            rowsPerPage: pagingOptions.pageSize,
            count: data?.totalCount ?? 0,
            rowsPerPageOptions: Object.values(muiDataTablePageSizes),
            filterArrayFullMatch: true,
            confirmFilters: true,
            search: false,
            download: !hasCustomExport,
            setFilterChipProps: !hideChips
                ? undefined
                : (colIdx, colName, data) => {
                      return {
                          className: "hideChip",
                          label: `${colName}: ${data}`,
                      };
                  },
            onFilterChange: (
                _: string | MUIDataTableColumn | null,
                filterList: MUIDataTableState["filterList"],
                type: FilterType | "chip" | "reset"
            ) => {
                if (type === "chip") {
                    processFilters(
                        filterList,
                        columns,
                        pagingOptions,
                        setPagingOptions,
                        showDeleted as boolean
                    );
                }
            },
            onFilterConfirm: (filterList: string[][]) => {
                filterList = filterList.map((f) => {
                    const hasValue =
                        f.filter((v) => {
                            return v !== "" && v !== null;
                        }).length > 0;
                    if (hasValue) {
                        return f;
                    }
                    return [];
                });
                processFilters(
                    filterList,
                    columns,
                    pagingOptions,
                    setPagingOptions,
                    showDeleted as boolean
                );
            },
            onFilterChipClose: (index: number, removedFilter: string, filterList: string[][]) => {
                filterList[index] = [];
                processFilters(
                    filterList,
                    columns,
                    pagingOptions,
                    setPagingOptions,
                    showDeleted as boolean
                );
            },
            // Calling the applyNewFilters parameter applies the selected filters to the table
            customFilterDialogFooter: customFilterDialogFooter,

            // see MUIDataTableOptions object
            /** @inheritdoc */
            onTableChange: async (action: string, tableState: MUIDataTableState) => {
                let page = tableState.page;
                if (tableState.rowsPerPage !== pagingOptions.pageSize) {
                    page = 0;
                }
                const columnsDisplay = Object.fromEntries(
                    tableState.columns.map((c) => [c.name, c.display])
                );
                const currentSorts = pagingOptions.sorts ?? "";
                let sorts = currentSorts;
                if (action === "sort") {
                    const index = !!tableState.activeColumn ? parseInt(tableState.activeColumn, 10) : 0;
                    const customSortMethod = columns[index].customSortMethodName;
                    sorts = buildSieveSorts(tableState.sortOrder, customSortMethod);
                }
                if (tablePagingOptionsChangeCallback) {
                    tablePagingOptionsChangeCallback({
                        page: page + 1,
                        pageSize: tableState.rowsPerPage,
                        sortOrder: tableState.sortOrder,
                        sorts: sorts,
                        filtersList: pagingOptions.filtersList,
                        filters: pagingOptions.filters ?? "",
                        displayColumns: columnsDisplay,
                        location: window.location.pathname,
                    });
                }
            },
            onRowSelectionChange: (
                _: any[],
                allRowsSelected: { index: number; dataIndex: number }[],
                _1?: any[]
            ) => {
                const rowsIndexes = allRowsSelected.map((r) => r.dataIndex);
                setRowsSelected(rowsIndexes);
            },
            //We need to unselect rows if we change of page
            onChangePage(_: number) {
                setRowsSelected([]);
            },
            customToolbarSelect: (selectedRows: MUIDataTableSelectRows) => {
                if (rowActions) {
                    const selectedDataItems = selectedRows.data.map(
                        (x) => data?.items[x.dataIndex] as TDto
                    );
                    const actions = data
                        ? getAvailableActions(
                              selectedDataItems.map((item) =>
                                  rowActions.getActions(item, selectedDataItems)
                              )
                          )
                        : [];
                    return (
                        <KTableActions
                            actions={actions as KTableAction[]}
                            selectedRows={selectedDataItems}
                        />
                    );
                }
            },
            onColumnSortChange: () => {
                setRowsSelected([]);
            },
        },
        columns: columns,
        exportCallback: hasCustomExport ? exportCallback : undefined,
        isDownloading: isDownloading,
        rowsSelected,
        setRowsSelected,
        displayColumns: displayColumns,
    };
};

/**
 * Gets a KMUIDataTableColumn object with some useful defaults for filtering and sorts purposes
 *
 * @param options Columns options
 * @returns KMUIDataTableColumn
 */
export const getKColumn = (options: KMUIDataTableColumnOptions): KMUIDataTableColumn => {
    options.sticky = options.sticky ?? 0;
    if (options.sticky) {
        options.options = options.options ?? {};
        options.options.display = "true";
    }
    let column: KMUIDataTableColumn = {
        name: options.name,
        label: options.label,
        customFilter: options.filterOptions?.customFilter,
        options: {
            sort: options.sort ?? true,
        },
        customSortMethodName: options.customSortMethodName,
        sticky: options.sticky,
        exportMetadata: options.exportMetadata,
    };
    if (options.filterOptions) {
        switch (options.filterOptions.filterType) {
            case KMUIDataTableInputFilterType.NumberRange:
                column = getDefaultNumberRangeFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableInputFilterColumnOptions
                );
                break;
            case KMUIDataTableInputFilterType.DateRange:
                column = getDefaultDateRangeFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableInputFilterColumnOptions
                );
                break;
            case KMUIDataTableSelectFilterType.Autocomplete:
                column = getDefaultAutocompleteFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableSelectFilterColumnOptions
                );
                break;
            case KMUIDataTableSelectFilterType.RadioButtons:
                column = getDefaultRadioButtonsFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableSelectFilterColumnOptions
                );
                break;
            case KMUIDataTableSelectFilterType.Checkboxes:
                column = getDefaultCheckboxesFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableSelectFilterColumnOptions
                );
                break;
            case KMUIDataTableYesNoFilterType.YesNo:
                column = getDefaultYesNoFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableYesNoFilterColumnOptions
                );
                break;
            case KMUIDataTableServerSideSelectFilterType.ServerSideAutocomplete:
                column = getServerSideAutocompleteFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableServerSideSelectFilterColumnOptions
                );
                break;
            default:
                column = getDefaultContainsFilterableColumn(
                    column,
                    options.filterOptions as KMUIDataTableInputFilterColumnOptions
                );
                break;
        }
    }
    //merging column options at the end so provided options win
    if (options.options) {
        column.options = merge(column.options, options.options);
    }
    if (options.filterOptions?.fullWidth && column.options?.filterOptions) {
        column.options.filterOptions.fullWidth = options.filterOptions?.fullWidth;
    }
    return column;
};

/**
 * Gets a KMUIDataTableColumn object with some useful defaults for filtering purposes
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions:
        | KMUIDataTableInputFilterColumnOptions
        | KMUIDataTableSelectFilterColumnOptions
        | KMUIDataTableYesNoFilterColumnOptions
        | KMUIDataTableServerSideSelectFilterColumnOptions
): KMUIDataTableColumn => {
    filterOptions.filterDisplay =
        filterOptions.filterDisplay ??
        (column.options?.display !== false &&
            column.options?.display !== "excluded" &&
            column.options?.display !== "false");
    column.options = merge(column.options, {
        filterList:
            filterOptions.pagingOptions.filtersList &&
            filterOptions.pagingOptions.filtersList[column.name]
                ? filterOptions.pagingOptions.filtersList[column.name].values
                : [],
        filter: filterOptions.filterDisplay,
        filterType: "custom",
    });
    return column;
};

/**
 * Gets a KMUIDataTableColumn object with Contains type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultContainsFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableInputFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableInputFilterType.Contains;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        customFilter: (name: string, values: string[]) => {
            const searchFields = filterOptions.searchFields ?? [name];
            const filterName = searchFields.join("|");
            return [
                `${filterName}${
                    filterOptions.filterType === KMUIDataTableInputFilterType.Equals ? "==" : "@="
                }${values[0]}`,
            ];
        },
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    const filterLabel = `${filterOptions.filterLabel ?? column.label ?? column.name} ${
                        filterOptions.filterType === KMUIDataTableInputFilterType.Equals
                            ? "(equals)"
                            : "(contains)"
                    }`;
                    return getDisplayContains(filterList, onChange, index, column, filterLabel);
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    const filterLabel = `${filterOptions.filterLabel ?? column.label ?? column.name} ${
                        filterOptions.filterType === KMUIDataTableInputFilterType.Equals
                            ? "(equals)"
                            : "(contains)"
                    }`;
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterLabel}: ${v.join(",")}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with Number Range type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultNumberRangeFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableInputFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableInputFilterType.NumberRange;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayNumberRange(
                        filterList,
                        onChange,
                        index,
                        column,
                        filterOptions.filterLabel,
                        {
                            inputLeftProps: filterOptions?.inputLeftProps,
                            inputRightProps: filterOptions?.inputRightProps,
                        }
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    if (v.length === 1) {
                        v.push("");
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name} (range): ${v
                        .map((l) => (l ? l : "Any"))
                        .join(" to ")}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with Date Range type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultDateRangeFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableInputFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableInputFilterType.DateRange;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayDateRange(
                        filterList,
                        onChange,
                        index,
                        column,
                        filterOptions.filterLabel
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    if (v.length === 1) {
                        v.push("");
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name} (range): ${v
                        .map((item) =>
                            item ? moment.utc(item).local().format(KATANA_SHORT_DATE_FORMAT) : "Any"
                        )
                        .join(" to ")}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with Autocomplete (Yes/ No) type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultCheckboxesFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableSelectFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableSelectFilterType.Checkboxes;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayCheckboxes(
                        filterList,
                        onChange,
                        index,
                        column,
                        filterOptions.options ?? [],
                        filterOptions.filterLabel
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name}: ${v.join(
                        " | "
                    )}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with Autocomplete type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultAutocompleteFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableSelectFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableSelectFilterType.Autocomplete;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayAutocomplete(
                        filterList,
                        onChange,
                        index,
                        column,
                        filterOptions.options,
                        filterOptions.filterLabel,
                        filterOptions.multiple ?? true
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name}: ${v.join(
                        " | "
                    )}`;
                },
            },
        },
    });
};
/**
 * Gets a KMUIDataTableColumn object with Autocomplete (Yes/ No) type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultRadioButtonsFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableSelectFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableSelectFilterType.RadioButtons;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayRadioButtons(
                        filterList,
                        onChange,
                        index,
                        column,
                        filterOptions.options ?? [],
                        filterOptions.filterLabel
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name}: ${v.join(
                        " | "
                    )}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with Autocomplete (Yes/ No) type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getDefaultYesNoFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableYesNoFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableYesNoFilterType.YesNo;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    return displayAutocomplete(
                        filterList,
                        onChange,
                        index,
                        column,
                        getEnumKeys(TrueFalseStatus),
                        filterOptions.filterLabel,
                        false
                    );
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name}: ${v.join(
                        " | "
                    )}`;
                },
            },
        },
    });
};

/**
 * Gets a KMUIDataTableColumn object with ClinicAutocomplete type of filter
 *
 * @param column KMUIDataTableColumn
 * @param filterOptions Options for Filterable Column
 * @returns KMUIDataTableColumn
 */
const getServerSideAutocompleteFilterableColumn = (
    column: KMUIDataTableColumn,
    filterOptions: KMUIDataTableServerSideSelectFilterColumnOptions
): KMUIDataTableColumn => {
    column.filterType = KMUIDataTableSelectFilterType.Autocomplete;
    return merge(getDefaultFilterableColumn(column, filterOptions), {
        options: {
            filterOptions: {
                display: (
                    filterList: string[][],
                    onChange: (
                        val: string | string[],
                        index: number,
                        column: MUIDataTableColumn
                    ) => void,
                    index: number,
                    column: MUIDataTableColumn
                ) => {
                    filterOptions.multiple = filterOptions.multiple ?? true;
                    //removing KMUIDataTableServerSideSelectFilterColumnOptions values
                    const {
                        filterType,
                        filterDisplay,
                        pagingOptions,
                        ...autocompleteFilterWidgetProps
                    } = filterOptions;
                    return displayUseQueryAutocomplete({
                        ...autocompleteFilterWidgetProps,
                        filterList: filterList,
                        onChange: onChange,
                        index: index,
                        column: column,
                    });
                },
            },
            customFilterListOptions: {
                render: (v: any) => {
                    if (!Array.isArray(v)) {
                        return "";
                    }
                    return `${filterOptions.filterLabel ?? column.label ?? column.name}: ${v
                        .map((v) => v.split(":")[1])
                        .join(" | ")}`;
                },
            },
        },
    });
};
