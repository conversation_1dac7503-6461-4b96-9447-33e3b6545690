﻿using Aoshield.Core.Entities.Models;
using Katana.Services.TranscriberGroupConfigs;
using Katana.Services.TranscriberGroupConfigs.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// TranscribersGroupConfig CRUD service
    /// </summary>
    public class TranscriberGroupConfigsServiceTest : ITranscribersGroupConfigService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddTranscribersGroupConfigDto> Add(AddTranscribersGroupConfigDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddTranscribersGroupConfigDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<TranscribersGroupConfigDto>> GetAsPagedResults(
            SieveModel query, CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<TranscribersGroupConfigDto>([],
                    default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<TranscribersGroupConfigDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new TranscribersGroupConfigDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateTranscribersGroupConfigDto> Update(
            UpdateTranscribersGroupConfigDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateTranscribersGroupConfigDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id)
                .ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default)
            => Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
