﻿using Aoshield.Core.Entities.Models;
using Katana.Services.DiagnosticAliases;
using Katana.Services.DiagnosticAliases.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// DiagnosticAlias CRUD service
    /// </summary>
    public class DiagnosticAliasesServiceTest : IDiagnosticAliasService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddDiagnosticAliasDto> Add(AddDiagnosticAliasDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddDiagnosticAliasDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<DiagnosticAliasDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<DiagnosticAliasDto>([], default,
                    default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<DiagnosticAliasDto> GetById(int id,
            CancellationToken cancellation = default) => await Task.Run(() =>
            id <= 0 ? null : new DiagnosticAliasDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateDiagnosticAliasDto> Update(UpdateDiagnosticAliasDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateDiagnosticAliasDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<IPagedResults<DiagnosticAliasDto>> GetDiagnosticAliasesByDiagnostic(
            int diagnosticId, SieveModel query)
            => await Task.Run(() =>
                new PagedResults<DiagnosticAliasDto>([], default,
                    default, default, default, default, default, default, default));

        #endregion
    }
}
