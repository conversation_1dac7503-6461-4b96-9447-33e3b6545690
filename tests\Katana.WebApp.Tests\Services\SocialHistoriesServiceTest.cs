﻿using Aoshield.Core.Entities.Models;
using Katana.Services.SocialHistories;
using Katana.Services.SocialHistories.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// SocialHistory CRUD service
    /// </summary>
    public class SocialHistoriesServiceTest : ISocialHistoriesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddSocialHistoryDto> Add(AddSocialHistoryDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddSocialHistoryDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<SocialHistoryDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<SocialHistoryDto>([],
                default, default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<SocialHistoryDto> GetById(int id,
            CancellationToken cancellation = default)
            => await Task.Run(() => id <= 0 ? null : new SocialHistoryDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateSocialHistoryDto> Update(UpdateSocialHistoryDto dto,
            CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateSocialHistoryDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() =>
                confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
