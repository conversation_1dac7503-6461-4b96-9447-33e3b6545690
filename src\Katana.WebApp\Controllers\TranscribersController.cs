﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Katana.Services.Common.Models;
using Katana.Services.Faxes;
using Katana.Services.Faxes.Models;
using Katana.Services.Transcribers;
using Katana.Services.Transcribers.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Transcribers Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class TranscribersController : ControllerBase
    {
        private readonly ITranscribersService _transcribersService;
        private readonly IFaxesService _faxesService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="transcribersService"></param>
        /// <param name="faxesService"></param>
        public TranscribersController(
            ITranscribersService transcribersService,
            IFaxesService faxesService
        )
        {
            _transcribersService = transcribersService;
            _faxesService = faxesService;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<TranscriberDto>> GetTranscribers(
            [FromQuery] SieveModel query) =>
            await _transcribersService.GetAsPagedResults(query);

        /// <summary>
        /// Get entity by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns>Entity with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<TranscriberDto>> GetTranscriber(int id)
        {
            var dto = await _transcribersService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Add entity
        /// </summary>
        /// <param name="newTransc">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        // POST: api/Users
        [HttpPost]
        public async Task<ActionResult<AddTranscriberDto>> PostTranscriber(
            AddTranscriberDto newTransc,
            CancellationToken cancellation)
        {
            var dto = await _transcribersService.Add(newTransc, cancellation: cancellation);
            return StatusCode(StatusCodes.Status201Created, dto);
        }

        /// <summary>
        /// Update entity
        /// </summary>
        /// <param name="id">Id of the entity to update</param>
        /// <param name="entity">Entity's data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<UpdateTranscriberDto>> PutTranscriber(int id,
            UpdateTranscriberDto entity, CancellationToken cancellation) =>
            id != entity.Id
                ? BadRequest()
                : await _transcribersService.Update(entity, cancellation: cancellation);

        /// <summary>
        /// Delete Transcriber
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<int?>> DeleteTranscriber(int id,
            CancellationToken cancellation = default) =>
            await _transcribersService.Delete(id, cancellation: cancellation);

        /// <summary>
        /// Delete batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to delete</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("DeleteBatch")]
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcribersService.DeleteBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore batch entities
        /// </summary>
        /// <param name="dtoArr">Entities to restore</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Dto</returns>
        [HttpDelete("RestoreBatch")]
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcribersService.RestoreBatch(dtoArr, cancellation: cancellation);

        /// <summary>
        /// Restore entity
        /// </summary>
        /// <param name="id">Id of the entity to restore</param>
        /// <returns></returns>
        [HttpPatch("{id}")]
        public async Task<ActionResult<int?>> RestoreTranscriber(int id) =>
            await _transcribersService.Restore(id, notify: false);

        /// <summary>
        /// Get status log
        /// </summary>
        /// <param name="id">Transcriber's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Status")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>> GetStatusLog(int id,
            [FromQuery] SieveModel query) =>
            await _transcribersService.GetStatusLogsAsPagedResults(id, query);

        /// <summary>
        /// Assign or unassign transcribersGroup
        /// </summary>
        /// <param name="dto">Contains id of TrancriberGroup and id(s) of Transcriber(s) to assign or unassign </param>
        /// <returns></returns>
        // To protect from overposting attacks, enable the specific properties you want to bind to, for
        // more details, see https://go.microsoft.com/fwlink/?linkid=2123754.
        [HttpPut("AssignGroup")]
        public async Task<int> AssignToGroup([FromBody] AssignTranscribersGroupDto[] dto) =>
            await _transcribersService.AssignToTranscriberGroup(dto);

        /// <summary>
        /// Get assigned faxes
        /// </summary>
        /// <returns>List</returns>
        /// <param name="id">Transcriber Id</param>
        /// <param name="query">Sieve model</param>
        /// <param name="cancellation">Cancellation token</param>
        // GET: api/Faxes
        [HttpGet("{id}/Faxes")]
        //[EnableQuery]
        public async Task<IPagedResults<FaxListDto>> GetAssignedFaxes(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _faxesService.GetFaxesByTranscriber(id, query, cancellation);

        /// <summary>
        /// Disable batch entities with notes
        /// </summary>
        /// <param name="dtoArr">Entities with note to disable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("DisableBatch")]
        public async Task<int[]> DisableBatch(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) =>
            await _transcribersService.DisableBatch(dtoArr, cancellation: cancellation);


        /// <summary>
        /// Enable batch entities
        /// </summary>
        /// <param name="dtoArr">Entities id to enable</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        [HttpPut("EnableBatch")]
        public async Task<int[]> EnableBatch(
            [FromBody] ConfirmationNoteDto[] dtoArr, CancellationToken cancellation) =>
            await _transcribersService.EnableBatch(dtoArr,
                cancellation: cancellation);
    }
}
