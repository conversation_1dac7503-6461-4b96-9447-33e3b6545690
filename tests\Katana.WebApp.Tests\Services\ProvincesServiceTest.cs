﻿using Aoshield.Core.Entities.Models;
using Katana.Services.Provinces;
using Katana.Services.Provinces.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Province CRUD service
    /// </summary>
    public class ProvincesServiceTest : IProvincesService
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddProvinceDto> Add(AddProvinceDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddProvinceDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<ProvinceDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<ProvinceDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<ProvinceDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new ProvinceDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateProvinceDto> Update(UpdateProvinceDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateProvinceDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public Task<(int addedCountries, int updatedCountries, int addedProvinces, int updatedProvinces)> SynchronizeCountriesAndProvices(CancellationToken cancellation) => throw new NotImplementedException();

        #endregion
    }
}
