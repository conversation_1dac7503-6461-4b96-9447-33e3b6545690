﻿using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Katana.Core.Entities;
using Katana.Services.Careplans;
using Katana.Services.Careplans.Models;
using Katana.Services.Treatmentplans;
using Katana.Services.Treatmentplans.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Treatmentplan Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class TreatmentplansController : ControllerBase
    {
        private readonly ITreatmentplansService _treatmentplansService;
        private readonly ICareplansService _careplansService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="treatmentplansService">CRUD service</param>
        /// <param name="careplansService"></param>
        public TreatmentplansController(
            ITreatmentplansService treatmentplansService,
            ICareplansService careplansService
        )
        {
            _treatmentplansService = treatmentplansService;
            _careplansService = careplansService;
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet]
        public async Task<IPagedResults<TreatmentplanListDto>> GetTreatmentplans(
            [FromQuery] SieveModel query)
            => await _treatmentplansService.GetTreatmentplans(query);

        /// <summary>
        /// Get entity by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns>Entity with provided id</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<TreatmentplanDto>> GetTreatmentplan(int id)
        {
            var dto = await _treatmentplansService.GetById(id);

            return dto != null ? Ok(dto) : NotFound();
        }

        /// <summary>
        /// Get list of attachments
        /// </summary>
        /// <param name="id"> Treatmentplan's id</param>
        /// <param name="query">Sieve Model</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Attachments")]
        public async Task<IPagedResults<BlobItemDto>> GetListFiles(int id,
            [FromQuery] SieveModel query, CancellationToken cancellation) =>
            await _treatmentplansService.ListFiles(id, query, cancellation);

        /// <summary>
        /// Get public SAS url
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("Attachments/{id}/uri")]
        public Task<ActionResult> GetFileSasUri(int id, [FromQuery] string fileName)
        {
            var result = _treatmentplansService.GeneratePublicUri(id, fileName);
            return Task.FromResult<ActionResult>(Ok(new { Url = result.ToString() }));
        }

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List</returns>
        [HttpGet("{id}/careplans")]
        public async Task<IPagedResults<CareplanListDto>> GetCareplans(int id,
            [FromQuery] SieveModel query) =>
            await _careplansService.GetCareplansByTreatmentplanId(id, query);

        /// <summary>
        /// Get notes
        /// </summary>
        /// <param name="id">Entity's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Notes")]
        public async Task<IPagedResults<TreatmentplanNoteDto>> GetNotes(int id, [FromQuery] SieveModel query)
            => await _treatmentplansService.GetNotesAsPagedResults(id, query);

        /// <summary>
        /// Get workflow statuses
        /// </summary>
        /// <param name="id">Entity's id</param>
        /// <param name="query">Sieve Model</param>
        /// <returns>List</returns>
        [HttpGet("{id}/Workflowstatus")]
        public async Task<IPagedResults<BaseStatusLogEntityDto<TreatmentplanWorkflowStatus>>>
            GetWorkflowStatusLogs(int id, [FromQuery] SieveModel query)
            => await _treatmentplansService.GetWorkflowStatusLogsAsPagedResults(id, query);
    }
}
