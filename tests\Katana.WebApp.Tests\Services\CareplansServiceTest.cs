﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Core.Services.Audit;
using Aoshield.Core.Services.Notification;
using Aoshield.Core.Validation;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Services.CareplanResponseTemplates.Models;
using Katana.Services.Careplans;
using Katana.Services.Careplans.Models;
using Katana.Services.ClinicPractitioners.Models;
using Katana.Services.Common.Models;
using Katana.Services.Dashboards.Model;
using Katana.Services.DeclineReasons.Models;
using Katana.Services.InvoiceItems.Models;
using Katana.Services.Treatmentplans.Models;
using Microsoft.AspNetCore.Http;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Careplan CRUD service
    /// </summary>
    public class CareplansServiceTest : ICareplansService
    {
        /// <inheritdoc />
        public IAuditStorageProvider AuditStorageProvider { get; }

        /// <inheritdoc />
        public IKrudder<User> Krudder { get; }

        /// <inheritdoc/>
        public IValidator<Careplan> Validator => throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddCareplanDto> Add(AddCareplanDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddCareplanDto(), cancellation);

        /////<inheritdoc/>
        //public bool AddCareplanImportId(List<Careplan> careplans) =>
        //    throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<IPagedResults<CareplanListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<CareplanListDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<CareplanDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new CareplanDto() { Id = id });

        /// <inheritdoc/>
        public async Task<UpdateCareplanDto> Update(UpdateCareplanDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateCareplanDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        /// <inheritdoc />
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => await Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddCareplanNoteDto> AddNote(AddCareplanNoteDto addNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddCareplanNoteDto());

        ///<inheritdoc/>
        public async Task<CareplanNoteDto> GetNoteById(int id,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new CareplanNoteDto() { Id = id });

        ///<inheritdoc/>
        public async Task<IPagedResults<CareplanNoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<CareplanNoteDto>([], default, default, default, default,
                default, default, default, default));

        ///<inheritdoc/>
        public async Task<UpdateCareplanNoteDto> UpdateNote(UpdateCareplanNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateCareplanNoteDto() { Id = updateNoteDto.Id });

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await Task.Run(() => ids);

        #endregion

        #region Workflow Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<CareplanWorkflowStatus>>>
            GetWorkflowStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<BaseStatusLogEntityDto<CareplanWorkflowStatus>>(
                [], default, default,
                default, default, default, default, default, default));

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Careplan> ConfigureSet(IQueryable<Careplan> _, Type _1,
            ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Careplan _, IList<Careplan> _1, string _2,
            Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Custom

        ///<inheritdoc/>
        public async Task<UpdateCareplanRecommendationsDto> UpdateCareplanRecommendations(
            UpdateCareplanRecommendationsDto dto, CancellationToken cancellation) =>
            await Task.Run(() => new UpdateCareplanRecommendationsDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<UpdateCareplanCasesNotesDto> UpdateCareplanCasesNotes(
            UpdateCareplanCasesNotesDto dto, CancellationToken cancel = default)
            => await Task.Run(() => new UpdateCareplanCasesNotesDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<UpdateCareplanConsultationReasonDto> UpdateCareplanConsultationReason(
            UpdateCareplanConsultationReasonDto dto,
            CancellationToken cancel = default)
            => await Task.Run(() => new UpdateCareplanConsultationReasonDto() { Id = dto.Id });

        #endregion

        #region Operations

        ///<inheritdoc/>
        public async Task<CareplanResponseTemplateDto> CopyResponseTemplate(int careplanId,
            int templateId, CancellationToken cancellation = default)
            => await Task.Run(() => new CareplanResponseTemplateDto() { Id = templateId });

        /// <inheritdoc />
        public static async Task<MemoryStream> GenerateResponseReport(int _, string _1,
            CancellationToken _2 = default) =>
            await Task.Run(() => Task.FromResult(new MemoryStream()));

        ///<inheritdoc/>
        public async Task<int> GenerateFromFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> ManualAssignSpEcc(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> ManualAssignItpEcc(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<List<int>> PickUp(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Select(d => d.Id).ToList());

        ///<inheritdoc/>
        public Task<List<int>> ReleaseCareplan(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int> ReloopEcc(CareplanReloopActionDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> BackInPool(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> SendBackCareplan(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<int> AssociateClinicGp(AddClinicPractitionerDto[] dtoArr, bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<int> AdjustmentsCareplan(CareplanAdjustmentDto[] _,
            CancellationToken _1 = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<Careplan> CommonAdd(Careplan careplan, string rulesSet = null,
            CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        ///<inheritdoc/>
        public Task<QuotasCardDto> GetApprovedSummary(
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<QuotasCardDto> GetApprovedSummary(
            Practitioner practitioner, CancellationToken cancellation = default) =>
            throw new NotImplementedException();


        ///<inheritdoc/>
        public Task<QuotasCardDto> GetRequestedSummary(
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<QuotasCardDto> GetRequestedSummary(Practitioner practitioner,
            CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> GetRequestedQuotasSummary(
            Practitioner practitioner, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> AssignDiagnosticCpsCareplan(AssignDiagnosticDto[] dtos,
            CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> SetConfirmedReview(ConfirmedReviewDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        #region Attachments

        ///<inheritdoc/>
        public async Task<IPagedResults<BlobItemDto>> ListFiles(int Id, SieveModel query,
            CancellationToken cancellation) =>
            await Task.Run(() => new PagedResults<BlobItemDto>([], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<BlobItemDto> UploadFile(int id, AddBlobItemDto blobItem,
            bool overrideExisting = true, CancellationToken cancellation = default) =>
            await Task.Run(() => new BlobItemDto() { Id = id });

        ///<inheritdoc/>
        public async Task<BlobItemDto> DeleteFile(int id, string fileName,
            CancellationToken cancellation) =>
            await Task.Run(() => new BlobItemDto() { Id = id });

        /// <inheritdoc />
        public Task<BlobItemDto> DeleteFile(DeleteStorageFileDto dto,
            CancellationToken cancellation = default) =>
            Task.Run(() => new BlobItemDto() { Id = dto.ParentId }, cancellation);

        /// <inheritdoc />
        public async Task<IList<BlobItemDto>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) => await Task.Run(() =>
            dtos.Select(dto => new BlobItemDto() { Id = dto.ParentId }).ToList());

        ///<inheritdoc/>
        public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) =>
            throw new NotImplementedException();

        #endregion

        #region Queries

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansAttentionMine(SieveModel query,
            CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansByTreatmentplanId(int id,
            SieveModel query, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<List<BlobItemDto>> UploadFiles(int id, List<IFormFile> files,
            bool overrideExisting, CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<string> GetPdfVersionUrl(int careplanId,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<BlobItemDto> DownloadFile(int id, string fileName,
            CancellationToken cancellation)
            => await Task.Run(() => new BlobItemDto()
            {
                Id = id,
                Content = new MemoryStream(),
                ContentType = "application/octet-stream"
            });

        ///<inheritdoc/>
        public Task<int> GenerateCareplanUploadRequest(BaseUpdateDto[] _,
            CancellationToken _1 = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int> DoNotBillOCode(DoNotBillOCodeDto[] dtoArr,
            CancellationToken cancellation = default) => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> DoNotBillRCode(DoNotBillRCodeDto[] dtoArr,
            CancellationToken cancellation = default) => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task
            GenerateUploadFaxEccCareplan(Careplan careplan, CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<Careplan> AdjustRequestDate(Careplan careplan,
            int daysToAdjust,
            int currentUserId,
            List<Careplan> careplansToDeliver = null,
            CancellationToken cancellation = default)
            => await Task.Run(() => careplan);

        ///<inheritdoc/>
        public async Task<int> AdjustCareplanRequestDateEcc(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<int> AutoAssignSpEcc(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<int> AssignSpecialtyEccCareplan(AssignSpecialtyToCareplanDto[] dtos,
            CancellationToken cancellation) => Task.Run(() => dtos.Length);

        /// <inheritdoc />
        public async Task<IPagedResults<CareplanListDto>> GetCpsAsPagedResults(SieveModel query) =>
            await Task.Run(() => new PagedResults<CareplanListDto>([], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<IPagedResults<CareplanListDto>> GetCareplansAttention(SieveModel query,
            CancellationToken cancellation = default)
            => await Task.Run(() => new PagedResults<CareplanListDto>([], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public async Task<int> CancelCareplan(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation = default)
            => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public Task<int> GetApprovedQuotasSummary(Practitioner practitioner,
            CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> AutomaticUploadFaxDelivery(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();


        ///<inheritdoc/>
        public Task<int> CreateEccAsCareplanAsDraft(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ContinueEccCareplanToItpPending(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
            => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ApproveItp(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int>
            ApproveSp(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<AuditCareplanExportRequestDto> CreateAuditCareplansPdfExportRequest(
            AddAuditCareplanExportRequestDto request, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task ExportAuditCareplanPdfs(CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> RetryByExportAuditCareplanPdfsFailed(
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<SecondLoopCareplansCasesNotesDto> GetSecondLoopCareplanCasesNotesByHpu(
            int treatmentplanId, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanInvoiceItemDto>> GetInvoiceItemsByCareplanId(int id,
            SieveModel query, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansForReviewSpResponses(
            SieveModel query, CancellationToken cancellation = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ReviewSpResponseCpsCareplan(ReviewSpResponseDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansForSpResponses(SieveModel query,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<UpdateCareplanPatientMedicationsDto> UpdateCareplanPatientMedications(
            UpdateCareplanPatientMedicationsDto dto, CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<UpdateCareplanPatientConditionsDto> UpdateCareplanPatientConditions(
            UpdateCareplanPatientConditionsDto dto, CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<UpdateCareplanInvestigationsDto> UpdateCareplanInvestigations(
            UpdateCareplanInvestigationsDto dto, CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<UpdateCareplanCaseNotesUnifiedDto> UpdateCareplanCaseNotesUnified(UpdateCareplanCaseNotesUnifiedDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateCareplanCaseNotesUnifiedDto() { Id = dto.Id });

        ///<inheritdoc/>
        public Task<CareplanPatientMedicationsDto> GetCareplanPatientMedications(int careplanId,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<CareplanPatientConditionsDto> GetCareplanPatientConditions(int careplanId,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<CareplanInvestigationsDto> GetCareplanInvestigations(int careplanId,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<List<Careplan>> GetCareplansPendingForSpApproval(
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ReloopCareplanToSpForNewGpWorkflow(SendBackToSpDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ReloopCareplanToItpForNewGpWorkflow(SendBackToItpDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<Careplan> GenerateCareplanSecondLoopForNewGpWorkflow(int treatmentplanId,
            IList<ActionToNotify<CareplansActions>> actionsToNotify, User currentUser,
            DateTime requestDate, Careplan existingCp, Practitioner spItp, Practitioner sp,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<PractitionerAssignment> CreatePractitionerAssignment(string currentUser,
            int practitionerId, PractitionerType practitionerType, DateTime? assignmentDate = null,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> SendBackCpsCareplanToFullDispatch(SendBackToItpDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<DeferredRequestResponse>
            AutomaticGPApproval(CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<Careplan> CreateCareplanFromHpuForNewGpWorkflow(int treatmentplanId,
            CareplanWorkflowStatus workflowStatus, CancellationToken cancellation) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<((Practitioner, PractitionerAssignment) spItp, bool
            existSpItpBySpecialtyOrDiagnostic)> GetNextSpItpNewWorkflow(
            DateTime assigningDate, List<Careplan> careplansAssignedDuringBatch,
            List<(Practitioner Practitioner, PractitionerAssignment PractitionerAssignment)>
                allSpItps, int defaultEntityFilterId, int? patientAge,
            CancellationToken cancellation, int? defaultApprovingQuota = null) =>
            throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int> CloseClinicalQuestion(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetUnansweredClinicalQuestions(SieveModel query,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ManualAssignItpCpsCareplan(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ManualAssignSpNewGpWorkflow(AssignPractitionerDto[] dtoArr,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<DeferredRequestResponse> MoveCareplansFromUpcomingToAvailableForSPs(CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetAvailableCareplans(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetUpcomingCareplansForSPs(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion


        #region Clinical Questions

        ///<inheritdoc/>
        public Task SetUpProjectedRequestDateToClinicalQuestion(Careplan careplan, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> DeclineClinicalQuestion(DeclineClinicalQuestionDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task DeclineClinicalQuestionCommon(Careplan careplan, string note, int? declineClinicalQuestionReasonId, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<AddClinicalQuestionDto[]> GenerateClinicalQuestion(AddClinicalQuestionDto[] dto, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<Careplan> InitializeClinicalQuestion(Careplan careplan, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<ReAskClinicalQuestionDto> ReAskClinicalQuestion(ReAskClinicalQuestionDto dto, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ApproveClinicalQuestion(BaseUpdateDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<DeferredRequestResponse> ScheduleClinicalQuestions(CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<ClinicalQuestionDto>> GetClinicalQuestions(SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<ClinicalQuestionDto>> GetClinicalQuestionsByPatient(int patientId, SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> UpdateClinicalQuestion(UpdateClinicalQuestionDto[] dtos, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<IPagedResults<DeclineReasonDto>> GetDeclineReasonForCQ(SieveModel query, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> ChangeClinicalQuestionGp(AssignPractitionerDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<ClinicalQuestionDto> GetClinicalQuestionById(int id, CancellationToken cancellation) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<DeferredRequestResponse> DispatchClinicalQuestions(CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task<int> DispatchClinicalQuestionsAt(DateTime assigningDate, BaseUpdateDto[] cqToDispatchDtos, BaseUpdateDto[] cqToExcludeDtos = null, IEnumerable<int> itpsIds = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task CopyInformationFromPatient(Careplan careplan, Patient patient, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        /// <inheritdoc/>
        public Task<int> DispatchClinicalQuestionsForTests(BaseUpdateDto[] dtos, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task MigrateNonDispatchedCQToCP(string patientsIds = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task MigrateDispatchedCQToCP(string patientsIds = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<AddClinicalQuestionDto[]> GenerateClinicalQuestionFromPatient(AddClinicalQuestionDto[] dto, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task MigratePendingWorkflowStatusLogs(CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<PdfOutput> GenerateCpPdf(Careplan careplan, bool appendEducationalCareplans, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task ProcessItpApprovedCareplans(IEnumerable<int> ids, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<DeferredRequestResponse> CronProcessUploadFax(CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<string> GetUploadFaxPdfVersion(int faxId, CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task ProcessSPsToUpdatePool(IEnumerable<int> ids, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<DeferredRequestResponse> ScheduleClinicalQuestionsAt(BaseUpdateDto[] cqToDispatchDtos = null, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansForGP(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansForITP(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<IPagedResults<CareplanListDto>> GetCareplansForSP(SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc/>
        public Task<int> UpdateCareplansToDoNotBill(CancellationToken cancellation = default) => throw new NotImplementedException();
    }
}
