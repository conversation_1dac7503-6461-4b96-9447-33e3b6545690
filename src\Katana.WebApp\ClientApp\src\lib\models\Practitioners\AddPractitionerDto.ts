import { ApprovalPeriodOptions } from "../ApprovalPeriodOptions";
import { BaseAddUserEntityDto } from "../BaseAddUserEntityDto";
import { QuotaUnit } from "../QuotaUnit";
import { SortCriteriaOptions } from "../SortCriteriaOptions";

export interface AddPractitionerDto extends BaseAddUserEntityDto {
    practId: number | null;
    gp: boolean;
    sp: boolean;
    np: boolean;
    itp: boolean;
    gpsiItp: boolean;
    gpsi: boolean;
    spItp: boolean;
    cps: boolean;
    gpItp: boolean;
    hpc: boolean;
    hpcLead: boolean;
    mvca: boolean;
    vca: boolean;
    rp: boolean;
    externalBilling: boolean;
    requestingQuota: number | null;
    requestingQuotaUnit: QuotaUnit | null;
    approvingQuota: number | null;
    approvingQuotaUnit: QuotaUnit | null;
    approvalPeriod: number | null;
    approvalPeriodOption: ApprovalPeriodOptions;
    patientPanelingSortField: string;
    patientPanelingSortCriteria: SortCriteriaOptions | null;
    publicCalendarUrl: string | null;
    maxDailyQuestionsPerPatient: number | string | null;
}
