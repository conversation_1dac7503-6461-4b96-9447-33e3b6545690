﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Validation;
using Katana.Core.Entities;
using Katana.Services.Diagnostics;
using Katana.Services.Diagnostics.Models;
using Katana.Services.EducationalCareplans.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// Diagnostic CRUD service
    /// </summary>
    public class DiagnosticsServiceTest : IDiagnosticsService
    {
        ///<inheritdoc/>
        public FluentValidation.IValidator<Diagnostic> Validator => throw new NotImplementedException();

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddDiagnosticDto> Add(AddDiagnosticDto dto, CancellationToken cancellation = default) =>
            await Task.Run(() => new AddDiagnosticDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<DiagnosticListDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<DiagnosticListDto>([], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<DiagnosticDto>
            GetById(int id, CancellationToken cancellation = default) => await Task.Run(()
            => id <= 0 ? null : new DiagnosticDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateDiagnosticDto> Update(UpdateDiagnosticDto dto,
            CancellationToken cancellation = default)
            => await Task.Run(() => new UpdateDiagnosticDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    [], default, default, default, default,
                    default, default, default, default));

        #endregion

        #region ValidationStatus

        ///<inheritdoc/>
        public IQueryable<Diagnostic> ConfigureSet(IQueryable<Diagnostic> _, Type _1, ValidationTriggerAction _2,
            int[] _3 = null, bool _4 = false) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public Task SetValidationContextData(Diagnostic _, IList<Diagnostic> _1, string _2, Dictionary<string, object> _3,
            CancellationToken _4) => throw new NotImplementedException();

        ///<inheritdoc/>
        public Task Handle(SBValidationInquiryMessage _, CancellationToken _1) =>
            throw new NotImplementedException();

        #endregion

        #region Custom

        /// <summary>
        /// Retrieves educational careplans by diagnosis ID.
        /// </summary>
        /// <param name="diagnosticId">The diagnostic ID.</param>
        /// <param name="query">The query model.</param>
        /// <param name="cancellation">The cancellation token.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the paged results of educational careplans.</returns>
        public Task<IPagedResults<EducationalCareplanDto>> GetEducationalCareplansByDiagnosisId(
            int diagnosticId, SieveModel query, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion

        #region Operations

        ///<inheritdoc/>
        public async Task<int> AssignEducationalCareplan(AssignEducationalCareplanDto[] dtoArr, CancellationToken cancellation = default)
             => await Task.Run(() => dtoArr.Length);

        ///<inheritdoc/>
        public async Task<int> ConvertDiagnosticsToAlias(ConvertDiagnosticToAliasDto dto, CancellationToken cancellation = default)
            => await Task.Run(() => dto.AliasToConvert.Count);

        #endregion

        /// <summary>
        /// Sets the dispatch review for the given diagnostics.
        /// </summary>
        /// <param name="dtoArr">Array of diagnostic DTOs.</param>
        /// <param name="cancellation">Cancellation token.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        public Task<int> SetDispatchReview(DispatchReviewDto[] dtoArr, CancellationToken cancellation = default) => throw new NotImplementedException();
    }
}
