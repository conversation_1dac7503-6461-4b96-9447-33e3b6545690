using Aoshield.Core.DataAccess.AzureFileStorage;
using Aoshield.Core.DataAccess.AzureFileStorage.Models;
using Aoshield.Core.DataAccess.Extensions;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Exceptions;
using Aoshield.Core.Extensions;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Services.AI.FormRecognizer;
using Aoshield.Services.Core.Exceptions;
using Aoshield.Services.Core.SharepointFileStorage;
using Aoshield.Services.Messaging.Faxes;
using Aoshield.Services.Messaging.RingCentral;
using Aoshield.Services.ServicesBus.PubSub;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using FluentValidation;
using Katana.Core.Entities;
using Katana.Core.Enums;
using Katana.Database.Configurations.Extensions;
using Katana.Services.Common;
using Katana.Services.Common.FaxesService;
using Katana.Services.Common.FaxesService.Messaging;
using Katana.Services.Common.Models;
using Katana.Services.Faxes.Models;
using Katana.Services.KatanaTelemetries;
using Katana.Services.KatanaTelemetries.Models;
using Katana.Services.KatanaTelemetryDetails.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PdfSharpCore.Pdf.IO;

namespace Katana.Services.Faxes
{
    /// <summary>
    /// Faxes CRUD service
    /// </summary>
    public class FaxesService : KatanaCrudService<Fax, FaxListDto, FaxDto, AddFaxDto, UpdateFaxDto>,
        IFaxesService
    {
        private readonly IOptions<FaxesConfiguration> _faxServiceContext;
        private readonly KatanaFormRecognizerConfiguration _recognizerConfiguration;
        private readonly IValidator<FaxNote> _faxNoteValidator;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IAzureFileStorageService _fileStorageService;
        private readonly SharepointStorage _sharepointStorageService;

        private readonly ISBSender<SBPendingProcessingFaxMessage> _pendingProcessingFaxMessageSender;
        private readonly IServiceProvider _serviceProvider;
        private readonly IRingCentralClient _ringCentralClient;
        private readonly IKatanaTelemetriesService _katanaTelemetriesService;

        /// <summary>
        /// Fax CRUD service
        /// </summary>
        public FaxesService(
            IOptions<FaxesConfiguration> faxServiceContext,
            IOptions<KatanaFormRecognizerConfiguration> recognizerConfiguration,
            IKrudder<User> krudder,
            IValidator<Fax> faxValidator,
            IValidator<FaxNote> faxNoteValidator,
            IServiceScopeFactory serviceScopeFactory,
            IAzureFileStorageService fileStorageService,
            SharepointStorage sharepointStorageService,
            IServiceProvider serviceProvider,
            IKatanaTelemetriesService katanaTelemetriesService) : base(krudder, faxValidator)
        {
            _faxServiceContext = faxServiceContext;
            _faxNoteValidator = faxNoteValidator;
            _serviceScopeFactory = serviceScopeFactory;
            _fileStorageService = fileStorageService;
            _sharepointStorageService = sharepointStorageService;
            _ringCentralClient = serviceProvider.GetService<IRingCentralClient>();
            _pendingProcessingFaxMessageSender = serviceProvider.GetService<ISBSender<SBPendingProcessingFaxMessage>>();
            _serviceProvider = serviceProvider;
            _katanaTelemetriesService = katanaTelemetriesService;
            _recognizerConfiguration = recognizerConfiguration.Value;
        }

        ///<inheritdoc/>
        private async Task<Fax> GetByCloudId(string cloudId,
            CancellationToken cancellation = default) =>
            await _krudder.Set<Fax>()
                .FirstOrDefaultAsync(f => f.CloudFaxId.Equals(cloudId), cancellation);

        #region CRUD

        ///<inheritdoc/>
        public override Task<AddFaxDto> Add(AddFaxDto _, CancellationToken _2 = default) =>
            throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<int> ImportFaxes(ImportFaxDto importFaxDto,
            CancellationToken cancellation = default)
        {
            var countFaxImported = 0;
            var files = importFaxDto.Files;

            if (!files.Any())
            {
                var msg = "Wrong action. Missing files. Please add the file(s) to upload.";
                _krudder.Logger.LogError("{msg}", msg);
                throw new ReadableException(msg);
            }

            var duplicates = files.GroupBy(x => x.FileName).Where(g => g.Count() > 1)
                .Select(g => g.Key);
            if (duplicates.Any())
            {
                var msg = $"Attempt to import duplicate faxes: {string.Join(",", duplicates)}";
                _krudder.Logger.LogError("{msg}", msg);
                throw new ReadableException(msg);
            }

            var isAutomaticFaxProcessingDisabled = _recognizerConfiguration.ModelId.Length == 0;
            foreach (var file in files)
            {
                var fax = await CreateIncomingFaxImport(importFaxDto, isAutomaticFaxProcessingDisabled, file, cancellation);
                countFaxImported++;

                if (!isAutomaticFaxProcessingDisabled)
                {
                    if (_pendingProcessingFaxMessageSender != null)
                    {
                        var requesterUserId = await _krudder.GetCurrentUserId(cancellation);
                        await _pendingProcessingFaxMessageSender.SendMessage(
                            new SBPendingProcessingFaxMessage(requesterUserId) { Id = fax.Id }, cancellation);
                    }
                    else
                    {
                        await RecognizeFax(fax.Id, cancellation);
                    }
                }
            }

            return countFaxImported;
        }

        /// <inheritdoc/>
        public async Task CommonAdd(Fax fax,
            string rulesSet = null,
            BeforeSaveDelegate<Fax> beforeSave = null,
            AfterSaveDelegate<Fax> afterSave = null,
            CancellationToken cancellation = default)
        {
            //adding statuses
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            await fax
                .AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                    _krudder, fax.WorkflowStatus, authorId, cancellation: cancellation);

            //saving the entity with status logs
            await _krudder.Add(fax, Validator, beforeSave: beforeSave, afterSave: afterSave, rulesSet: rulesSet, cancellation: cancellation);
        }

        /// <inheritdoc/>
        public override async Task<FaxDto> GetById(int id, CancellationToken cancellation = default) => await _krudder.GetById<Fax, FaxDto>(id, cancellation: cancellation);

        ///<inheritdoc/>
        public override async Task<UpdateFaxDto> Update(UpdateFaxDto dto,
            CancellationToken cancellation = default)
        {
            var entity = await _krudder.GetById<Fax>(dto.Id, (set) =>
            {
                return set.Include(r => r.StatusLogs);
            }, true, cancellation: cancellation);
            entity = _krudder.Map(dto, _krudder.Mapper.Map, entity,
                GlobalActions.Update); //setting new values

            //adding statuses
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            await entity
                .AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                    _krudder, dto.WorkflowStatus, authorId, cancellation: cancellation);

            await _krudder.Update(entity, Validator, cancellation: cancellation);
            return dto;
        }

        #endregion

        #region Status Logs

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await _krudder.GetAsPagedResults<FaxStatusLog, BaseStatusLogEntityDto<Status>>(query,
                (set) => set.Where(fn => fn.ParentId.Equals(parentId))
                    .OrderByDescending(n => n.CreatedAt), cancellation: cancellation);

        #endregion

        #region WorkflowStatus Logs

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<FaxWorkflowStatus>>>
            GetWorkflowStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await _krudder
                .GetAsPagedResults<FaxWorkflowStatusLog, BaseStatusLogEntityDto<FaxWorkflowStatus>>(
                    query,
                    (set) => set.Where(fn => fn.ParentId.Equals(parentId))
                        .OrderByDescending(n => n.CreatedAt), cancellation: cancellation);

        #endregion

        #region Notes

        ///<inheritdoc/>
        public async Task<AddNoteDto>
            AddNote(AddNoteDto addNoteDto, CancellationToken cancellation = default) =>
            await this.AddNoteEntity<Fax, FaxNote, NoteDto, AddNoteDto, UpdateNoteDto>(addNoteDto, _krudder, _faxNoteValidator,
                cancellation);

        ///<inheritdoc/>
        public async Task<NoteDto> GetNoteById(int id, CancellationToken cancellation = default) =>
            await _krudder.GetById<FaxNote, NoteDto>(id, cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<NoteDto>> GetNotesAsPagedResults(
            int parentId,
            SieveModel query,
            CancellationToken cancellation = default) =>
            await _krudder.GetAsPagedResults<FaxNote, NoteDto>(query,
                (set) => set.Where(fn => fn.ParentId.Equals(parentId))
                    .OrderByDescending(n => n.CreatedAt), cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<UpdateNoteDto> UpdateNote(UpdateNoteDto updateNoteDto,
            CancellationToken cancellation = default) =>
            await this.UpdateNoteEntity<Fax, FaxNote, NoteDto, AddNoteDto, UpdateNoteDto>(updateNoteDto, _krudder, _faxNoteValidator,
                cancellation);

        ///<inheritdoc/>
        public async Task<int?> DeleteNote(int id, CancellationToken cancellation = default) =>
            await this.DeleteNoteEntity<Fax, FaxNote>(id, _krudder, _faxNoteValidator,
                cancellation);

        ///<inheritdoc/>
        public async Task<int[]> DeleteNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await this.DeleteNoteEntityBatch<Fax, FaxNote>(ids, _krudder, _faxNoteValidator,
                cancellation);

        ///<inheritdoc/>
        public async Task<int?> RestoreNote(int id, CancellationToken cancellation = default) =>
            await this.RestoreNoteEntity<Fax, FaxNote>(id, _faxNoteValidator, _krudder, cancellation);

        ///<inheritdoc/>
        public async Task<int[]> RestoreNoteBatch(int[] ids,
            CancellationToken cancellation = default) =>
            await this.RestoreNoteEntityBatch<Fax, FaxNote>(ids, _krudder, _faxNoteValidator,
                cancellation);

        #endregion

        #region ValidationStatus Logs

        ///<inheritdoc/>
        public override IQueryable<Fax> ConfigureSet(IQueryable<Fax> set, Type _, ValidationTriggerAction _2,
            IEnumerable<int> _1, string rulesSet, bool _3)
        {
            if (rulesSet.Equals(ValidationStatusRuleSets.CheckStatus.Name))
            {
                set = set
                .Include(e => e.Clinic)
                .Include(e => e.Transcriber)
                .Include(e => e.TranscribersGroup)
                .Where(f => !f.Type.Equals(FaxType.Outgoing));
            }
            return set;
        }

        private readonly string[] ignoreClinicFields = [nameof(Clinic.EnabledSync), nameof(Clinic.IntegratorType)];

        ///<inheritdoc/>
        public override Task Handle(SBValidationInquiryMessage notification, CancellationToken _)
        {
            if (!new string[] { nameof(Clinic), nameof(Transcriber) }.Contains(notification
                    .TriggerEntityType?.Name))
            {
                return Task.CompletedTask;
            }

            if (notification.TriggerEntityType.Name.Equals(nameof(Clinic)) &&
                !notification.ChangedFields.Any(field => !ignoreClinicFields.Contains(field)))
            {
                return Task.CompletedTask;
            }

            notification.AddEntityToValidate(typeof(Fax));
            return Task.CompletedTask;
        }

        #endregion

        #region Operations

        ///<inheritdoc/>
        public async Task<int> AssignToTranscriberGroup(AssignTranscribersGroupDto[] dtoArr,
            CancellationToken cancellation = default)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            return await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.AssignTranscriberGroup.Name,
                beforeSave: async (fax, _, _, cancellation) =>
                {
                    fax.TranscriberId = null;
                    fax.AssignedUserId = null;
                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, fax.TranscribersGroupId.HasValue ? FaxWorkflowStatus.Grouped : FaxWorkflowStatus.New, authorId, cancellation: cancellation);

                    return false;
                }, cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<int> AssignToTranscriber(AssignTranscriberDto[] dtoArr,
            CancellationToken cancellation = default)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            return await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.AssignTranscriber.Name,
                beforeSave: async (fax, _, _, cancellation) =>
                {
                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, fax.TranscriberId.HasValue ? FaxWorkflowStatus.Assigned : FaxWorkflowStatus.New, authorId, cancellation: cancellation);

                    if (fax.WorkflowStatus is FaxWorkflowStatus.New)
                    {
                        fax.TranscribersGroupId = null;
                    }

                    return false;
                }, cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<int> ProcessFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            return await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.MarkAsProcessed.Name,
                beforeSave: async (fax, _, _, cancellation) =>
                {
                    if (!fax.WorkflowStatus.Equals(FaxWorkflowStatus.Assigned))
                    {
                        var msg = $"Wrong action. The {TypeExtensions.TypeDisplayName<Fax>()} is not assigned. Id: {fax.Id}";
                        throw new ReadableException(msg);
                    }

                    if (!fax.Type.Equals(FaxType.Outgoing))
                    {
                        var msg = $"Wrong action. The {TypeExtensions.PropertyDisplayName<Fax>(f => f.Type)} is invalid. Id: {fax.Id}";
                        throw new ReadableException(msg);
                    }

                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, FaxWorkflowStatus.Processed, authorId, cancellation: cancellation);
                    fax.ProcessedDate = DateTime.UtcNow;
                    fax.Method = FaxDeliveryMethod.Manual;
                    fax.UploadRequestOrigin = UploadRequestOrigin.Gp;

                    var careplan = await _krudder.Set<Careplan>().FirstOrDefaultAsync(tp => tp.DeliveryFaxId.Equals(fax.Id), cancellation);

                    if (careplan is null)
                    {
                        var msg = $"Wrong action. The {TypeExtensions.TypeDisplayName<Careplan>()} associated not found. Id: {fax.Id}";
                        throw new ReadableException(msg);
                    }

                    if (careplan != null)
                    {
                        careplan.Delivered = true;
                        careplan.DeliveryDate = fax.ProcessedDate;
                    }

                    return false;
                }, cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task RollBackFaxToNewStatus(int faxId, CancellationToken cancellation = default)
        {
            await _krudder.Update(faxId, Validator,
                configureSet: set => set.Include(f => f.WorkflowStatusLogs),
                beforeSave: async (fax, cancellation) =>
            {
                if (fax.WorkflowStatus is not FaxWorkflowStatus.BeingProcess)
                {
                    return;
                }

                await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, FaxWorkflowStatus.New, null, cancellation: cancellation);
            }, cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<int> AutomaticProcessFax(BaseUpdateDto[] dtoArr,
            CancellationToken cancellation = default)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            var faxesIds = new List<int>();
            var count = await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.AutomaticProcess.Name,
                beforeSave: async (fax, _, _, cancellation) =>
                {
                    fax.AssignedUserId = null;
                    fax.TranscriberId = null;
                    fax.TranscribersGroupId = null;
                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, FaxWorkflowStatus.BeingProcess, authorId, cancellation: cancellation);

                    return true;
                },
                afterSave: (fax, _, cancellation) =>
                {
                    faxesIds.Add(fax.Id);
                    return Task.CompletedTask;
                },
                cancellation: cancellation);

            if (_pendingProcessingFaxMessageSender != null)
            {
                var requesterUserId = await _krudder.GetCurrentUserId(cancellation);
                await _pendingProcessingFaxMessageSender.SendMessages(faxesIds.Select(id => new SBPendingProcessingFaxMessage(requesterUserId) { Id = id }), cancellation);
            }
            else
            {
                foreach (var id in faxesIds)
                {
                    await RecognizeFax(id, cancellation);
                }
            }

            return count;
        }

        ///<inheritdoc/>
        public async Task<int> SendBackFax(ConfirmationNoteDto[] dtoArr,
            CancellationToken cancellation = default)
        {
            var authorId = await _krudder.GetCurrentUserId(cancellation);
            return await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.SendBackFax.Name,
                configureSet: (set) =>
                {
                    return set.Include(f => f.WorkflowStatusLogs);
                }, beforeSave: async (fax, i, _, cancellation) =>
                {
                    if (fax.WorkflowStatus is FaxWorkflowStatus.BeingProcess)
                    {
                        var careplan = await _krudder.Set<Careplan>()
                            .Where(c => c.FaxId.HasValue && c.FaxId.Equals(fax.Id)).FirstOrDefaultAsync(cancellation);
                        if (careplan != null)
                        {
                            careplan.SoftDeleteLevel = 1;
                        }
                    }

                    fax.AssignedUserId = null;
                    fax.TranscriberId = null;
                    fax.TranscribersGroupId = null;
                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, FaxWorkflowStatus.Pending, authorId, cancellation: cancellation);

                    await fax.AddNote<Fax, FaxNote>(
                        _krudder, dtoArr[i].Note, authorId, cancellation);

                    return false;
                }, cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<int> Cancel(ConfirmationNoteDto[] dtoArr, CancellationToken cancellation = default)
        {
            var currentUserId = await _krudder.GetCurrentUserId(cancellation);
            return await _krudder.UpdateBatch(dtoArr, Validator, FaxesActions.Cancel.Name,
                beforeSave: async (fax, i, _, cancellation) =>
                {
                    if (string.IsNullOrEmpty(dtoArr[i].Note))
                    {
                        var msg = $"Wrong action. The cancel reason must not be empty. {nameof(Fax)} Id: {fax.Id}";
                        _krudder.Logger.LogError("{msg}", msg);
                        throw new ReadableException(msg);
                    }
                    await fax.AddWorkflowStatus<Fax, FaxWorkflowStatusLog, FaxWorkflowStatus>(
                        _krudder, FaxWorkflowStatus.Cancelled, currentUserId, cancellation: cancellation);
                    await fax.AddNote<Fax, FaxNote>(
                        _krudder, $"Cancel reason: {dtoArr[i].Note}", currentUserId, cancellation);

                    return false;
                }, cancellation: cancellation);
        }

        #endregion

        #region Queries

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetFaxesByTranscribersGroup(int groupId,
            SieveModel query, CancellationToken cancellation = default)
        {
            var currentUser = await _krudder.GetCurrentUser(cancellation);

            if (currentUser.TranscribersManager == null ||
                !currentUser.TranscribersManager.TranscribersGroupId.HasValue ||
                !currentUser.TranscribersManager.TranscribersGroupId.Equals(groupId))
            {
                var msg = $"Error getting faxes by group for user {currentUser.DisplayName}";
                _krudder.Logger.LogError("{msg}", msg);
                throw new ServiceException(msg);
            }

            return await _krudder.GetAsPagedResults<Fax, FaxListDto>(query, configureSet: set =>
                    set.Where(f => f.TranscribersGroupId == currentUser.TranscribersManager.TranscribersGroupId),
                cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetFaxesByTranscriber(int transcriberId,
            SieveModel query, CancellationToken cancellation = default)
        {
            var currentUser = await _krudder.GetCurrentUser(cancellation);

            if (currentUser.Transcriber == null || !currentUser.Transcriber.Id.Equals(transcriberId))
            {
                var msg = $"Error getting faxes by transcriber for user {currentUser.DisplayName}";
                _krudder.Logger.LogError("{msg}", msg);
                throw new ServiceException(msg);
            }

            return await _krudder.GetAsPagedResults<Fax, FaxListDto>(query, configureSet: set =>
                set.Where(f => f.TranscriberId == currentUser.Transcriber.Id), cancellation: cancellation);
        }

        #endregion

        #region Process Incoming Faxes

        private async Task<bool> ProcessIncomingFax(BlobItemDto item,
            CancellationToken cancellation = default)
        {
            try
            {
                var nameExt = Path.GetExtension(item.Name);
                if (!nameExt.Equals(".pdf"))
                {
                    _krudder.Logger.LogError("Error getting faxes, {item.Name} is not a pdf file", item.Name);
                    return false;
                }

                var clinicName = item.FullPathName.Split('/')[^2];
                var clinicInfo = await GetClinicByName(clinicName, cancellation);
                if (clinicInfo is null)
                {
                    _krudder.Logger.LogWarning("Warning processing faxes, folder name {clinicName} is not a valid clinic", clinicName);
                }

                var blobItem =
                    await _sharepointStorageService.DownloadFile(item.FullPathName + item.Name,
                        cancellation);
                if (blobItem is null)
                {
                    _krudder.Logger.LogError("Error getting faxes, error recovering {FullPathName} file", item.FullPathName + item.Name);
                    return false;
                }

                var pageCount = PdfReader.Open(blobItem.Content, PdfDocumentOpenMode.InformationOnly).PageCount;

                if (blobItem.Content is not null || pageCount != 0)
                {
                    var fileName = clinicInfo is not null
                        ? !string.IsNullOrEmpty(clinicInfo.FaxNumber)
                            ? clinicInfo.FaxNumber
                            : !string.IsNullOrEmpty(clinicInfo.Telephone)
                                ? clinicInfo.Telephone
                                : clinicInfo.Name.Replace(" ", string.Empty)
                        : "unknown";

                    var date = DateTime.UtcNow;
                    var isAutomaticFaxProcessingDisabled = _recognizerConfiguration.ModelId.Length == 0;
                    var fax = await CreateIncomingSharePointFax(item, clinicInfo, blobItem, pageCount, fileName, date, isAutomaticFaxProcessingDisabled, cancellation);

                    await _sharepointStorageService.DeleteFile(item.FullPathName + item.Name, cancellation);

                    if (!isAutomaticFaxProcessingDisabled)
                    {
                        if (_pendingProcessingFaxMessageSender != null)
                        {
                            var requesterUserId = await _krudder.GetCurrentUserId(cancellation);
                            await _pendingProcessingFaxMessageSender.SendMessage(
                                new SBPendingProcessingFaxMessage(requesterUserId) { Id = fax.Id }, cancellation);
                        }
                        else
                        {
                            await RecognizeFax(fax.Id, cancellation);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _krudder.Logger.LogError(ex, "Error processing faxes from sharepoint, {Message}", ex.Message);
                return false;
            }

            return true;
        }

        private async Task<Clinic> GetClinicByName(string clinicName,
            CancellationToken cancellation) =>
            await _krudder.Set<Clinic>()
                .FirstOrDefaultAsync(f => f.Name.Equals(clinicName), cancellation);

        ///<inheritdoc/>
        public async Task<ProcessIncomingFaxesResult> ProcessIncomingFaxes(string folderName, SieveModel query = default,
            CancellationToken cancellation = default)
        {
            var folder = folderName ?? _faxServiceContext.Value.FaxesIncomingFolder;
            if (folder == null)
            {
                _krudder.Logger.LogError("Error processing faxes folder: Folder is not specified.");
                return new ProcessIncomingFaxesResult();
            }
            var result = new ProcessIncomingFaxesResult();
            var blobItems = await _sharepointStorageService.ListFiles(folder, query, true, cancellation);
            foreach (var item in blobItems.Items)
            {
                if (item.Type == AzureStorageBlobType.File)
                {
                    var processed = await ProcessIncomingFax(item, cancellation);
                    if (processed)
                    {
                        result.ProcessedFaxesCount++;
                    }
                    else
                    {
                        result.FailedFaxesCount++;
                    }
                }
                else
                {
                    result.ScannedFoldersCount++;
                    var innerResult = await ProcessIncomingFaxes(item.FullPathName + item.Name, query, cancellation);
                    result.ProcessedFaxesCount += innerResult.ProcessedFaxesCount;
                    result.FailedFaxesCount += innerResult.FailedFaxesCount;
                    result.ScannedFoldersCount += innerResult.ScannedFoldersCount;
                }
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<DeferredRequestResponse> ProcessIncomingFaxes(CancellationToken cancellation = default)
        {
            var today = DateTime.UtcNow;
            var result = await ProcessIncomingFaxes(null, default, cancellation);

            // saving katana telemetry

            var katanaTelemetry = new AddKatanaTelemetryDto()
            {
                Key = (int) KatanaTelemetryTasks.ProcessIncomingFaxes,
                StartAt = today,
                FinishAt = DateTime.UtcNow,
                Result = ExecutionStatus.Success,
                Details = [
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessIncomingFaxesCron.ProcessedFaxesCount,
                        Value = result.ProcessedFaxesCount.ToString(),
                    },
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessIncomingFaxesCron.FailedFaxesCount,
                        Value = result.FailedFaxesCount.ToString(),
                    },
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessIncomingFaxesCron.ScannedFoldersCount,
                        Value = result.ScannedFoldersCount.ToString(),
                    },
                ]
            };

            await _katanaTelemetriesService.Add(katanaTelemetry, cancellation);

            return new DeferredRequestResponse
            {
                Sections = [
                    new()
                    {
                        Name = "Faxes were processed successfully",
                        Details = new Dictionary<string, string>
                        {
                            { "Processed Faxes", result.ProcessedFaxesCount.ToString() },
                            { "Failed Faxes", result.FailedFaxesCount.ToString() },
                            { "Scanned Folders", result.ScannedFoldersCount.ToString() }
                        }
                    }
                ]
            };
        }


        /// <summary>
        /// Load incoming fax from from cloud fax service
        /// </summary>
        /// <returns></returns>
        public async Task<DeferredRequestResponse> ProcessCloudIncomingFaxes(CancellationToken cancellation = default)
        {
            var today = DateTime.UtcNow;
            var newFaxesProcessed = 0;
            var faxesProcessed = 0;
            var failFaxesProcessed = 0;
            var error = "";

            var processResult = new DeferredRequestResponse
            {
                Sections = [
                    new() { Name = "Cloud Incoming Faxes processed", Details = [] }
                ]
            };

            if (_ringCentralClient == null)
            {
                processResult.Sections[0].Details = new Dictionary<string, string> { { "RingCentral Status", "Unavailable" } };
            }
            else
            {
                var faxes = await _ringCentralClient.GetUreadFaxes();
                if (faxes == null)
                {
                    processResult.Sections[0].Details = new Dictionary<string, string>
                    {
                        { "Processed Faxes", "0" },
                        { "New Faxes", "0" },
                        { "Failed Faxes", "0" },
                        { "RingCentral Status", "Available" }
                    };
                }
                else
                {
                    var requesterUserId = await _krudder.GetCurrentUserId(cancellation);
                    var scope = await _serviceScopeFactory.GetUserScope(requesterUserId, cancellation);
                    var faxesService = scope.ServiceProvider.GetService<IFaxesService>();
                    foreach (var fax in faxes)
                    {
                        try
                        {
                            faxesProcessed++;
                            if (await faxesService.ProcessCloudIncomingFax(fax.id.ToString(), Convert.ToInt32(fax.faxPageCount), cancellation))
                            {
                                newFaxesProcessed++;
                            }
                        }
                        catch (Exception ex)
                        {
                            failFaxesProcessed++;
                            scope.Dispose();
                            scope = await _serviceScopeFactory.GetUserScope(requesterUserId, cancellation);
                            faxesService = scope.ServiceProvider.GetService<IFaxesService>();
                            _krudder.Logger.LogError(ex, "Error processing faxes from Cloud, {Message}", ex.Message);
                            error += $"Error processing faxes from Cloud, {ex.Message}";
                        }
                    }

                    processResult.Sections[0].Details = new Dictionary<string, string>
                    {
                        { "Processed Faxes", faxesProcessed.ToString() },
                        { "New Faxes", newFaxesProcessed.ToString() },
                        { "Failed Faxes", failFaxesProcessed.ToString() },
                        { "RingCentral Status", "Available" }
                    };

                    if (!string.IsNullOrEmpty(error))
                    {
                        processResult.Sections[0].Details.Add("Error", error);
                    }
                }
            }

            // saving katana telemetry

            var katanaTelemetry = new AddKatanaTelemetryDto()
            {
                Key = (int) KatanaTelemetryTasks.ProcessCloudIncomingFaxes,
                StartAt = today,
                FinishAt = DateTime.UtcNow,
                Result = ExecutionStatus.Success,
                Details = [
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessCloudIncomingFaxesCron.ProcessedFaxesCount,
                        Value = faxesProcessed.ToString(),
                    },
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessCloudIncomingFaxesCron.NewFaxes,
                        Value = newFaxesProcessed.ToString(),
                    },
                    new AddKatanaTelemetryDetailDto()
                    {
                        Key = (int) ProcessCloudIncomingFaxesCron.FailedFaxesCount,
                        Value = failFaxesProcessed.ToString(),
                    },
                ]
            };

            await _katanaTelemetriesService.Add(katanaTelemetry, cancellation);

            return processResult;
        }

        /// <inheritdoc/>
        public async Task<bool> ProcessCloudIncomingFax(string cloudId, int TotalPagesCount, CancellationToken cancellation = default)
        {

            var fax = await GetByCloudId(cloudId, cancellation);

            if (fax != null)
            {
                await _ringCentralClient.MarkAsRead(cloudId); //mark as read now that we have it in our database
                return false;
            }
            var faxDocument = await _ringCentralClient.GetMessageInfo(cloudId);
            //from a valid clinic...create fax record
            var date = DateTime.UtcNow;
            var isAutomaticFaxProcessingDisabled = _recognizerConfiguration.ModelId.Length == 0;
            fax = await CreateIncomingCloudFax(cloudId, TotalPagesCount, faxDocument, date, isAutomaticFaxProcessingDisabled, cancellation);

            if (!isAutomaticFaxProcessingDisabled)
            {
                if (_pendingProcessingFaxMessageSender != null)
                {
                    var requesterUserId = await _krudder.GetCurrentUserId(cancellation);
                    await _pendingProcessingFaxMessageSender.SendMessage(new SBPendingProcessingFaxMessage(requesterUserId)
                    {
                        Id = fax.Id
                    }, cancellation);
                }
                else
                {
                    await RecognizeFax(fax.Id, cancellation);
                }
            }

            return true;
        }

        ///<inheritdoc/>
        public async Task<int> CreateIncomingFileStructure(string folderName, SieveModel query,
            CancellationToken cancellation = default)
        {
            var processCount = 0;
            var clinicsInfo = await _krudder.Set<Clinic>().ToListAsync(cancellation);

            foreach (var clinic in clinicsInfo)
            {
                processCount +=
                    await _sharepointStorageService.Createfolder(folderName, clinic.Name,
                        cancellation)
                        ? 1
                        : 0;
            }

            return processCount;
        }

        ///<inheritdoc/>
        public async Task RecognizeFax(int faxId, CancellationToken cancellation)
        {
            var fax = await _krudder.Set<Fax>().Include(f => f.WorkflowStatusLogs).FirstOrDefaultAsync(f => f.Id.Equals(faxId), cancellation);
            if (fax is null)
            {
                var msg = $"Error recognizing fax. Fax with id: {faxId} not found";
                _krudder.Logger.LogError("{msg}", msg);
                throw new ServiceException(msg); //preventing the message arrived before the fax was saved into the DB
            }
            var formRecognizerService = _serviceProvider.GetService<IFormRecognizerService>();
            var katanaFormRecognizerConfiguration = _serviceProvider.GetService<IOptions<KatanaFormRecognizerConfiguration>>();
            var modelIds = katanaFormRecognizerConfiguration.Value.ModelId;
            if (formRecognizerService == null || katanaFormRecognizerConfiguration == null || modelIds.Length == 0)
            {
                var msg = $"Error recognizing fax. Form recognizer services ot its configuration not found.";
                _krudder.Logger.LogError("{msg}", msg);
                await RollBackFaxToNewStatus(faxId, cancellation);
                return;
            }
            if (!fax.WorkflowStatus.Equals(FaxWorkflowStatus.BeingProcess))
            {
                var msg = $"Error recognizing fax. Fax with id: {faxId} is not the BeingProcess workflow status";
                _krudder.Logger.LogError("{msg}", msg);
                return;
            }
            var attachment = await DownloadFile(fax.Id, fax.Name, cancellation);
            if (attachment is null)
            {
                var msg = $"Error recognizing fax. Fax with id: {faxId} does not contain any attachments";
                _krudder.Logger.LogError("{msg}", msg);
                await RollBackFaxToNewStatus(fax.Id, cancellation);
                return;
            }

            var faxableServices = _serviceProvider.GetServices<IFaxable>();
            if (!faxableServices.Any())
            {
                _krudder.Logger.LogWarning("Fax with id: {faxId} could not be recognized. No faxable services were found", faxId);
                await RollBackFaxToNewStatus(fax.Id, cancellation);
                return;
            }

            //processing the fax
            var faxFileUri = GeneratePublicUri(fax.Id, fax.Name, DateTimeOffset.UtcNow.AddMinutes(5)); // 5 mins should be enough for Azure Form Recognizer to process the file

            var totalPagesCount = fax.TotalPagesCount ?? 1;
            foreach (var modelId in modelIds)
            {
                for (var i = 1; i <= Math.Min(2, totalPagesCount); i++)
                {
                    var operation = await formRecognizerService.Analyze(faxFileUri, modelId: modelId,
                    analyzeDocumentOptions: new AnalyzeDocumentOptions
                    {
                        Pages = { i.ToString() } //we only analyze the form's page
                    }, cancellationToken: cancellation);

                    if (!operation.HasValue || !operation.Value.Documents.Any())
                    {
                        var msg = $"Fax with id: {faxId} could not be recognized. Operation failed";
                        _krudder.Logger.LogError("{msg}", msg);
                        throw new ServiceException(msg);
                    }
                    // Notify
                    foreach (var faxable in faxableServices)
                    {
                        await faxable.ProcessFax(fax, attachment.Content.ToArray(), attachment.ContentType, operation, i, cancellation);

                        if (fax.WorkflowStatus.Equals(FaxWorkflowStatus.Processed))
                        {
                            return;
                        }
                    }
                }
            }
            //if we reach this point, it means that the fax could not be recognized
            var infoMsg = $"Fax with id: {faxId} could not be recognized by any service";
            _krudder.Logger.LogWarning("{infoMsg}", infoMsg);
            await RollBackFaxToNewStatus(fax.Id, cancellation);
        }

        #endregion

        #region Attachments

        ///<inheritdoc/>
        public async Task<IPagedResults<BlobItemDto>> ListFiles(int id, SieveModel query,
            CancellationToken cancellation = default) =>
            await _fileStorageService.ListFiles<Fax>(id, query, true, cancellation);

        ///<inheritdoc/>
        public async Task<BlobItemDto> UploadFile(int id, AddBlobItemDto blobItem,
            bool overrideExisting, CancellationToken cancellation = default) =>
            await _fileStorageService.UploadFile<Fax>(id, blobItem, overrideExisting, cancellation);

        ///<inheritdoc/>
        public async Task<List<BlobItemDto>> UploadFiles(int id, List<IFormFile> files,
            bool overrideExisting, CancellationToken cancellation = default) =>
            await _fileStorageService.UploadFiles<Fax>(id, files, overrideExisting, cancellation);

        ///<inheritdoc/>
        public async Task<BlobItemDto> DownloadFile(int id, string fileName,
            CancellationToken cancellation = default) =>
            await _fileStorageService.DownloadFile<Fax>(id, fileName, cancellation);

        ///<inheritdoc/>
        public async Task<BlobItemDto> DeleteFile(int id, string fileName,
            CancellationToken cancellation = default) =>
            await _fileStorageService.DeleteFile<Fax>(id, fileName, cancellation);

        ///<inheritdoc/>
        public async Task<BlobItemDto> DeleteFile(DeleteStorageFileDto dto,
            CancellationToken cancellation = default) =>
            await _fileStorageService.DeleteFile<Fax>(dto, cancellation);

        ///<inheritdoc/>
        public async Task<IList<BlobItemDto>> DeleteFiles(DeleteStorageFileDto[] dtos,
            CancellationToken cancellation = default) =>
            await _fileStorageService.DeleteFiles<Fax>(dtos, cancellation);

        ///<inheritdoc/>
        public Uri GeneratePublicUri(int id, string fileName, DateTimeOffset? expiresOn = null) =>
            _fileStorageService.GeneratePublicUri<Fax>(id, fileName, expiresOn);

        #endregion

        #region Utils

        ///<inheritdoc/>
        public async Task<Fax> CreateOutgoingFax(int id, Clinic clinic, ProcessingType processingType, int? practitionerId, int? patientId, UploadRequestOrigin requestOrigin = UploadRequestOrigin.Automatic, CancellationToken cancellation = default)
        {
            var currentUserId = await _krudder.GetCurrentUserId(cancellation);
            var uploadFax = new Fax
            {
                Name = $"UpLoad_{id}_{clinic?.Name ?? "UNKNOWN"}_{DateTime.UtcNow}.pdf",
                ClinicId = clinic?.Id,
                PractitionerId = practitionerId,
                PatientId = patientId,
                ProcessingType = processingType,
                Date = DateTime.UtcNow,
                WorkflowStatus = FaxWorkflowStatus.New,
                Type = FaxType.Outgoing,
                UploadRequestOrigin = requestOrigin,
            };
            await CommonAdd(uploadFax, cancellation: cancellation);

            return uploadFax;
        }

        /// <summary>
        /// Create incoming fax from cloud
        /// </summary>
        /// <param name="cloudId"></param>
        /// <param name="TotalPagesCount"></param>
        /// <param name="faxDocument"></param>
        /// <param name="date"></param>
        /// <param name="isAutomaticFaxProcessingDisabled"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        private async Task<Fax> CreateIncomingCloudFax(string cloudId, int TotalPagesCount, FaxInfo faxDocument, DateTime date, bool isAutomaticFaxProcessingDisabled, CancellationToken cancellation)
        {
            var fax = new Fax
            {
                Name = $"{faxDocument.From}_{date:yyyyMMddHHmmss}.pdf".Replace("+", ""),
                Date = date,
                CloudFaxId = cloudId,
                TotalPagesCount = TotalPagesCount,
                WorkflowStatus = isAutomaticFaxProcessingDisabled ? FaxWorkflowStatus.New : FaxWorkflowStatus.BeingProcess,
                Method = FaxDeliveryMethod.CloudFax,
                Type = FaxType.Incoming,
                Status = Status.Enabled
            };
            await CommonAdd(fax, afterSave: async (fax, cancellation) =>
            {
                await _ringCentralClient.MarkAsRead(cloudId); //mark as read now that we have it in our database
                if (faxDocument.Attachments.Any())
                {
                    var attachment = faxDocument.Attachments.ElementAt(0); //we only attach the first attachment since accuro put everything into one attachment
                    await UploadFile(fax.Id, new AddBlobItemDto
                    {
                        Name = fax.Name,
                        Content = new MemoryStream(attachment.Content),
                        ContentType = attachment.ContentType,
                    }, false, cancellation);
                }
            }, cancellation: cancellation);
            return fax;
        }

        /// <summary>
        /// Create incoming fax from sharepoint
        /// </summary>
        /// <param name="item"></param>
        /// <param name="clinicInfo"></param>
        /// <param name="blobItem"></param>
        /// <param name="pageCount"></param>
        /// <param name="fileName"></param>
        /// <param name="date"></param>
        /// <param name="isAutomaticFaxProcessingDisabled"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        private async Task<Fax> CreateIncomingSharePointFax(BlobItemDto item, Clinic clinicInfo, BlobItemDto blobItem, int pageCount, string fileName, DateTime date, bool isAutomaticFaxProcessingDisabled, CancellationToken cancellation)
        {
            var fax = new Fax
            {
                Name = $"{fileName}_{date:yyyyMMddHHmmss}.pdf".Replace("+", ""),
                Date = date,
                CloudFaxId = Path.GetFileNameWithoutExtension(item.Name),
                TotalPagesCount = pageCount,
                WorkflowStatus = isAutomaticFaxProcessingDisabled ? FaxWorkflowStatus.New : FaxWorkflowStatus.BeingProcess,
                ClinicId = clinicInfo?.Id,
                Status = Status.Enabled,
                Method = FaxDeliveryMethod.SharePointFile,
                Type = FaxType.Incoming
            };
            await CommonAdd(fax, afterSave: async (_, cancellation) =>
            {
                await UploadFile(fax.Id,
                    new AddBlobItemDto
                    {
                        Name = fax.Name,
                        Content = blobItem.Content,
                        ContentType = blobItem.ContentType,
                    }, true, cancellation);
            }, cancellation: cancellation);
            return fax;
        }

        /// <summary>
        /// Create incoming fax from import
        /// </summary>
        /// <param name="importFaxDto"></param>
        /// <param name="isAutomaticFaxProcessingDisabled"></param>
        /// <param name="file"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        private async Task<Fax> CreateIncomingFaxImport(ImportFaxDto importFaxDto, bool isAutomaticFaxProcessingDisabled, IFormFile file, CancellationToken cancellation)
        {
            var fax = _krudder.Map(importFaxDto, _krudder.Mapper.Map, new Fax(), GlobalActions.Add);

            var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream, cancellation);

            var pageCount = PdfReader.Open(memoryStream, PdfDocumentOpenMode.InformationOnly).PageCount;
            fax.TotalPagesCount = pageCount;
            fax.WorkflowStatus = isAutomaticFaxProcessingDisabled ? FaxWorkflowStatus.New : FaxWorkflowStatus.BeingProcess;
            fax.Method = FaxDeliveryMethod.Manual;
            fax.Type = FaxType.Incoming;
            await CommonAdd(fax, afterSave: async (fax, cancellation) =>
            {
                await UploadFile(fax.Id,
                    new AddBlobItemDto
                    {
                        Name = fax.Name,
                        Content = memoryStream,
                        ContentType = file.ContentType,
                    }, false, cancellation);
            }, cancellation: cancellation);
            return fax;
        }

        #endregion

        #region Scripts

        /// <summary>
        /// Updates the UploadRequestOrigin property of faxes based on their notes content.
        /// If a note contains "Upload Requested By Gp", the fax is set to Manually requested.
        /// Otherwise, it's set to Automatic.
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Task</returns>
        private async Task<int> UpdateFaxUploadOriginFromNotes(CancellationToken cancellation = default)
        {
            var faxes = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                .Where(f => !f.UploadRequestOrigin.HasValue && f.Type.Equals(FaxType.Outgoing))
                .Select(f => new Fax { Id = f.Id, })
                .ToListAsync(cancellation);

            if (faxes.Count == 0)
            {
                Console.WriteLine("No faxes to process.");
                return 0;
            }

            var maxBatchSize = 200;
            var processedFaxes = 0;

            await faxes.ProcessByChucks(async (faxesChunk, cancellation) =>
            {
                await _krudder.ExecuteInTransaction(async (CancellationToken) =>
                {
                    var faxesToProcess = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                    .Include(f => f.Notes)
                    .Where(f => faxesChunk.Select(c => c.Id).Contains(f.Id))
                    .ToListAsync(cancellation);

                    foreach (var fax in faxesToProcess)
                    {
                        var hasGpRequestNote = fax.Notes != null &&
                            fax.Notes.Any(n => n.Notes != null &&
                                         n.Notes.Contains("Upload Requested By Gp", StringComparison.OrdinalIgnoreCase));

                        fax.UploadRequestOrigin = hasGpRequestNote
                            ? UploadRequestOrigin.Gp
                            : UploadRequestOrigin.Automatic;

                        processedFaxes++;
                        Console.WriteLine($"Processed {processedFaxes} / {faxes.Count}.");
                    }
                }, cancellation);

                return true;
            }, maxBatchSize, cancellation);



            Console.WriteLine($"Processed {processedFaxes} faxes.");
            return processedFaxes;
        }

        /////<inheritdoc/>
        //private async Task UpdateUploadFaxesWithPractitioner(CancellationToken cancellation)
        //{
        //    var maxBatchSize = 500;
        //    var processed = 0;

        //    var faxes = await _krudder.Set<Fax>().Where(f => f.Type.Equals(FaxType.Outgoing) && !f.PractitionerId.HasValue).ToListAsync(cancellation);

        //    if (faxes.Count <= 0)
        //    {
        //        return;
        //    }

        //    var total = faxes.Count;
        //    processed = 0;
        //    var data = new Dictionary<string, object>();
        //    var faxesChunks = faxes.Select((x, i) => new { Index = i, Value = x })
        //        .GroupBy(x => x.Index / maxBatchSize).Select(x => x.Select(v => v.Value)).ToList();

        //    IEnumerable<Fax> faxesChunk = null;

        //    async Task processFaxesChunk(CancellationToken cancellation)
        //    {
        //        var hpus = await _krudder.Set<Treatmentplan>().AsNoTracking().Where(t => t.DeliveryFaxId.HasValue && faxesChunk.Select(f => f.Id).Contains(t.DeliveryFaxId.Value)).ToListAsync(cancellation);

        //        var careplans = await _krudder.Set<Careplan>().AsNoTracking().Where(c => c.DeliveryFaxId.HasValue && faxesChunk.Select(f => f.Id).Contains(c.DeliveryFaxId.Value)).ToListAsync(cancellation);

        //        foreach (var fax in faxesChunk)
        //        {
        //            var hpu = hpus.FirstOrDefault(h => h.DeliveryFaxId.Equals(fax.Id));
        //            var careplan = careplans.FirstOrDefault(c => c.DeliveryFaxId.Equals(fax.Id));

        //            if (hpu is null && careplan is null)
        //            {
        //                _krudder.Logger.LogDebug("Wrong Fax: {fax.Id}", fax.Id);
        //                continue;
        //            }

        //            if (hpu is not null)
        //            {
        //                fax.PractitionerId = hpu.RequesterPractitionerId;
        //            }
        //            else if (careplan is not null)
        //            {
        //                fax.PractitionerId = careplan.GpId;
        //            }

        //            processed++;
        //            _krudder.Logger.LogDebug("It was processed: {processed}/ {total}", processed, total);
        //        }
        //    }

        //    while (faxesChunks.Count != 0)
        //    {
        //        faxesChunk = faxesChunks.First();
        //        await _krudder.ExecuteInTransaction(processFaxesChunk, cancellation);
        //        faxesChunks.RemoveAt(0);
        //    }
        //}


        ///<inheritdoc/>
        public async Task<string> MigrateFaxesRelease33(CancellationToken cancellation = default)
        {
            var methodAndTypeUpdated = await ParseSourceFromFaxNotUpload(cancellation);
            methodAndTypeUpdated += await ParseUploadedFaxes(cancellation);
            methodAndTypeUpdated += await ParseWebClinicFaxes(cancellation);
            var uploadRequestOrigin = await UpdateFaxUploadOriginFromNotes(cancellation);
            var patientNavigation = await UpdatePatientOutgoingFaxes(cancellation);

            return $"{methodAndTypeUpdated} were updated with Method and Type. {uploadRequestOrigin} faxes were updated with UploadRequestOrigin from notes. {patientNavigation} patient navigations were stablished";
        }

        /// <summary>
        /// Set the Source property of the Fax entity to the correct value as done by FaxToFaxDto inputMethod autoMapper
        /// </summary>
        /// <param name="cancellation">cancellation token</param>
        /// <returns></returns>
        private async Task<int> ParseSourceFromFaxNotUpload(CancellationToken cancellation = default)
        {
            var faxes = await _krudder.Set<Fax>(EntityActiveStatus.Any).Where(f => f.Method == null && !f.ProcessingType.Equals(ProcessingType.Upload))
                .Select(f => f.Id)
                .ToListAsync(cancellation);

            if (faxes.Count == 0)
            {
                Console.WriteLine($"No faxes to process. ");
                return 0;
            }

            var maxBatchSize = 200;
            var processedFaxes = 0;

            var faxesChunks = faxes.Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / maxBatchSize)
                .Select(x => x.Select(v => v.Value))
                .ToList();

            IEnumerable<int> faxesChunk = null;

            Task processFaxesChunks(CancellationToken cancellation)
            {
                var faxesToProcess = _krudder.Set<Fax>(EntityActiveStatus.Any).Where(f => faxesChunk.Contains(f.Id))
                    .Include(f => f.Clinic)
                    .ToList();

                foreach (var fax in faxesToProcess)
                {
                    fax.Method = fax.CreatedById is null ? fax.CloudFaxId is null ? FaxDeliveryMethod.SharePointFile : FaxDeliveryMethod.CloudFax : FaxDeliveryMethod.Manual;
                    fax.Type = FaxType.Incoming;
                    processedFaxes++;
                    Console.WriteLine($"Processed {processedFaxes} / {faxes.Count}.");
                }

                return Task.CompletedTask;
            }

            while (faxesChunks.Count != 0)
            {
                faxesChunk = faxesChunks.First();
                await _krudder.ExecuteInTransaction(processFaxesChunks, cancellation);
                faxesChunks.RemoveAt(0);
            }

            Console.WriteLine($"Processed {processedFaxes} faxes.");
            return processedFaxes;
        }

        /// <summary>
        /// Set the Source property of uploaded Faxes
        /// </summary>
        /// <param name="cancellation">cancellation token</param>
        /// <returns></returns>
        private async Task<int> ParseUploadedFaxes(CancellationToken cancellation = default)
        {
            var faxes = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                .OrderByDescending(f => f.Date)
                .Where(f => f.ProcessingType.Equals(ProcessingType.Upload) && f.Method == null)
                .Select(f => f.Id)
                .ToListAsync(cancellation);

            if (faxes.Count == 0)
            {
                Console.WriteLine($"No faxes to process.");
                return 0;
            }

            var maxBatchSize = 200;
            var ignored = new List<int>();
            var processedFaxes = 0;

            var faxesChunks = faxes.Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / maxBatchSize)
                .Select(x => x.Select(v => v.Value))
                .ToList();

            IEnumerable<int> faxesChunk = null;

            async Task processFaxesChunks(CancellationToken cancellation)
            {
                var faxesToProcess = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                    .Include(f => f.Clinic)
                    .Where(f => faxesChunk.Contains(f.Id))
                    .ToListAsync(cancellation);

                var careplans = await _krudder.Set<Careplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, DeliveryFaxId = cp.DeliveryFaxId.Value, cp.ProcessingType })
                    .ToListAsync(cancellation);

                var treatmentplans = await _krudder.Set<Treatmentplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, DeliveryFaxId = cp.DeliveryFaxId.Value })
                    .ToListAsync(cancellation);

                var badFaxIds = faxesChunk.Where(id => !faxesToProcess.Select(x => x.Id).Contains(id));
                ignored.AddRange(badFaxIds);

                foreach (var fax in faxesToProcess)
                {
                    var careplan = careplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    var treatmentplan = treatmentplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    if (careplan is null && treatmentplan is null)
                    {
                        continue;
                    }
                    fax.Type = FaxType.Outgoing;
                    fax.Method = fax.TranscriberId.HasValue ? FaxDeliveryMethod.Manual : FaxDeliveryMethod.Unknown;
                    fax.ProcessingLoop = ProcessingLoop.Unknown;
                    fax.ProcessingType = careplan?.ProcessingType ?? ProcessingType.CPS;
                    processedFaxes++;
                    Console.WriteLine($"Processed {processedFaxes} / {faxes.Count}.");
                }
            }

            while (faxesChunks.Count != 0)
            {
                faxesChunk = faxesChunks.First();
                await _krudder.ExecuteInTransaction(processFaxesChunks, cancellation);
                faxesChunks.RemoveAt(0);
            }

            Console.WriteLine($"Processed {processedFaxes} faxes.");
            Console.WriteLine($"Ignored {ignored.Count}, {string.Join(", ", ignored)} because the fax is not associated with any Careplan.");

            return processedFaxes;
        }

        /// <summary>
        /// Set the Source property of uploaded Faxes
        /// </summary>
        /// <param name="cancellation">cancellation token</param>
        /// <returns></returns>
        private async Task<int> ParseWebClinicFaxes(CancellationToken cancellation = default)
        {
            var faxes = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                .OrderByDescending(f => f.Date)
                .Where(f => f.ProcessingType.Equals(ProcessingType.Upload) && f.Method == null)
                .Select(f => f.Id)
                .ToListAsync(cancellation);

            var importedCareplans = await _krudder.Set<Careplan>(EntityActiveStatus.Any)
                .Where(f => f.CareplanImportId.HasValue && !f.Id.Equals(f.CareplanImportId))
                .Select(f => new { f.Id, f.CareplanImportId, f.ProcessingType })
                .ToListAsync(cancellation);

            if (faxes.Count == 0)
            {
                Console.WriteLine($"No faxes to process.");
                return 0;
            }

            var maxBatchSize = 200;
            var ignored = new List<int>();
            var processedFaxes = 0;

            var faxesChunks = faxes.Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / maxBatchSize)
                .Select(x => x.Select(v => v.Value))
                .ToList();

            IEnumerable<int> faxesChunk = null;

            async Task processFaxesChunks(CancellationToken cancellation)
            {
                var faxesToProcess = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                    .Include(f => f.Clinic)
                    .Where(f => faxesChunk.Contains(f.Id))
                    .ToListAsync(cancellation);

                var careplans = await _krudder.Set<Careplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, DeliveryFaxId = cp.DeliveryFaxId.Value, cp.ProcessingType })
                    .ToListAsync(cancellation);

                var treatmentplans = await _krudder.Set<Treatmentplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, DeliveryFaxId = cp.DeliveryFaxId.Value })
                    .ToListAsync(cancellation);

                foreach (var fax in faxesToProcess)
                {
                    var careplan = careplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    var treatmentplan = treatmentplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    if (careplan is not null || treatmentplan is not null)
                    {
                        continue;
                    }
                    var webClinicFaxEcc = fax.Name.Contains("ECC_");
                    fax.Type = FaxType.Outgoing;
                    fax.Method = fax.TranscriberId.HasValue ? FaxDeliveryMethod.Manual : FaxDeliveryMethod.Unknown;
                    fax.ProcessingLoop = ProcessingLoop.Unknown;
                    fax.ProcessingType = webClinicFaxEcc ? ProcessingType.ECC : ProcessingType.Unknown;
                    processedFaxes++;
                    Console.WriteLine($"Processed {processedFaxes} / {faxes.Count}.");
                }
            }

            while (faxesChunks.Count != 0)
            {
                faxesChunk = faxesChunks.First();
                await _krudder.ExecuteInTransaction(processFaxesChunks, cancellation);
                faxesChunks.RemoveAt(0);
            }

            Console.WriteLine($"Processed {processedFaxes} faxes.");
            Console.WriteLine($"Ignored {ignored.Count}, {string.Join(", ", ignored)} because the fax is not associated with any Careplan.");

            return processedFaxes;
        }

        /// <summary>
        /// Set the PatientId property of Outgoing Faxes
        /// </summary>
        /// <param name="cancellation">cancellation token</param>
        /// <returns></returns>
        private async Task<int> UpdatePatientOutgoingFaxes(CancellationToken cancellation = default)
        {
            var faxes = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                .Where(f => f.Type == FaxType.Outgoing)
                .Select(f => f.Id)
                .ToListAsync(cancellation);

            if (faxes.Count == 0)
            {
                Console.WriteLine($"No faxes to process.");
                return 0;
            }

            var maxBatchSize = 200;
            var processedFaxes = 0;

            var faxesChunks = faxes.Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / maxBatchSize)
                .Select(x => x.Select(v => v.Value))
                .ToList();

            IEnumerable<int> faxesChunk = null;

            async Task processFaxesChunks(CancellationToken cancellation)
            {
                var faxesToProcess = await _krudder.Set<Fax>(EntityActiveStatus.Any)
                    .Where(f => faxesChunk.Contains(f.Id))
                    .ToListAsync(cancellation);

                var careplans = await _krudder.Set<Careplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, PatientId = cp.PatientId.Value, DeliveryFaxId = cp.DeliveryFaxId.Value })
                    .ToListAsync(cancellation);

                var treatmentplans = await _krudder.Set<Treatmentplan>(EntityActiveStatus.Any)
                    .Where(cp => cp.DeliveryFaxId.HasValue && faxesChunk.Contains(cp.DeliveryFaxId.Value))
                    .Select(cp => new { cp.Id, PatientId = cp.PatientId.Value, DeliveryFaxId = cp.DeliveryFaxId.Value })
                    .ToListAsync(cancellation);

                foreach (var fax in faxesToProcess)
                {
                    var careplan = careplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    var treatmentplan = treatmentplans.FirstOrDefault(cp => cp.DeliveryFaxId == fax.Id);
                    if (careplan is null && treatmentplan is null)
                    {
                        continue;
                    }
                    fax.PatientId = careplan is not null ? careplan.PatientId : treatmentplan.PatientId;
                    processedFaxes++;
                    Console.WriteLine($"Processed {processedFaxes} / {faxes.Count}.");
                }
            }

            while (faxesChunks.Count != 0)
            {
                faxesChunk = faxesChunks.First();
                await _krudder.ExecuteInTransaction(processFaxesChunks, cancellation);
                faxesChunks.RemoveAt(0);
            }

            Console.WriteLine($"Processed {processedFaxes} faxes.");

            return processedFaxes;
        }

        #endregion

        #region Faxes Direction

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetOutgoings(SieveModel query, CancellationToken cancellation = default)
        {
            // Get outgoing faxes
            return await _krudder.GetAsPagedResults<Fax, FaxListDto>(
                query,
                configureSet: set => set.Where(f => f.Type == FaxType.Outgoing),
                cancellation: cancellation);
        }

        ///<inheritdoc/>
        public async Task<IPagedResults<FaxListDto>> GetIncomings(SieveModel query, CancellationToken cancellation = default)
        {
            // Get incoming faxes
            return await _krudder.GetAsPagedResults<Fax, FaxListDto>(
                query,
                configureSet: set => set.Where(f => f.Type == FaxType.Incoming),
                cancellation: cancellation);
        }

        #endregion
    }
}
