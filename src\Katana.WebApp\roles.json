{"RolesDefinitionsSection": {"RolesDefinitions": [{"Role": "Transcribers_Management", "Definitions": [{"AuthResourceType": "Transcribers", "AuthPermissionType": "Manage"}]}, {"Role": "TranscriberManagers_Management", "Definitions": [{"AuthResourceType": "Transcribers", "AuthPermissionType": "Manage"}, {"AuthResourceType": "TranscriberManagers", "AuthPermissionType": "Manage"}]}, {"Role": "Faxes_Management", "Definitions": [{"AuthResourceType": "Faxes", "AuthPermissionType": "Manage"}]}, {"Role": "Faxes_AssignmentTranscribersGroup", "Definitions": [{"AuthResourceType": "Faxes", "AuthPermissionType": "AssignTranscribersGroup"}]}, {"Role": "Faxes_AssignmentTranscriber", "Definitions": [{"AuthResourceType": "Faxes", "AuthPermissionType": "AssignTranscriber"}]}, {"Role": "Faxes_Processing", "Definitions": [{"AuthResourceType": "Faxes", "AuthPermissionType": "Process"}, {"AuthResourceType": "HpuAssignment", "AuthPermissionType": "Read"}]}, {"Role": "Faxes_Read_All", "Definitions": [{"AuthResourceType": "Faxes", "AuthPermissionType": "ReadAll"}]}, {"Role": "Careplans_Management", "Definitions": [{"AuthResourceType": "Careplans", "AuthPermissionType": "Manage"}, {"AuthResourceType": "CareplanAll", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanAssignment", "AuthPermissionType": "Read"}, {"AuthResourceType": "Allergies", "AuthPermissionType": "Manage"}]}, {"Role": "Careplans_Processing", "Definitions": [{"AuthResourceType": "Careplans", "AuthPermissionType": "Process"}, {"AuthResourceType": "CareplanAssignment", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanMine", "AuthPermissionType": "Read"}, {"AuthResourceType": "QuotaCard", "AuthPermissionType": "Read"}, {"AuthResourceType": "ResponseTemplate", "AuthPermissionType": "Write"}]}, {"Role": "Careplans_Assignment_ITP", "Definitions": [{"AuthResourceType": "Careplans", "AuthPermissionType": "AssignITP"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}]}, {"Role": "Careplans_Assignment_SP", "Definitions": [{"AuthResourceType": "Careplans", "AuthPermissionType": "Assign<PERSON>"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}]}, {"Role": "CPS_Careplans_Management", "Definitions": [{"AuthResourceType": "CPS_Careplans", "AuthPermissionType": "Manage"}, {"AuthResourceType": "HpuAll", "AuthPermissionType": "Read"}, {"AuthResourceType": "HpuAttention", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanCps", "AuthPermissionType": "Read"}]}, {"Role": "CPS_Careplans_Processing", "Definitions": [{"AuthResourceType": "CPS_Careplans", "AuthPermissionType": "Process"}, {"AuthResourceType": "CareplanAssignment", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanMine", "AuthPermissionType": "Read"}, {"AuthResourceType": "QuotaCard", "AuthPermissionType": "Read"}, {"AuthResourceType": "ResponseTemplate", "AuthPermissionType": "Write"}]}, {"Role": "CPS_Careplans_Assignment_GPSIITP", "Definitions": [{"AuthResourceType": "CareplanCps", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}]}, {"Role": "CPS_Careplans_Assignment_GP", "Definitions": [{"AuthResourceType": "CPS_Careplans", "AuthPermissionType": "AssignGP"}]}, {"Role": "CPS_Careplans_Assignment_SPITP", "Definitions": [{"AuthResourceType": "CPS_Careplans", "AuthPermissionType": "AssignSPITP"}, {"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}, {"AuthResourceType": "CareplanCps", "AuthPermissionType": "Read"}]}, {"Role": "Referrals_Management", "Definitions": [{"AuthResourceType": "Referrals", "AuthPermissionType": "Manage"}, {"AuthResourceType": "Allergies", "AuthPermissionType": "Manage"}]}, {"Role": "Referrals_Processing", "Definitions": [{"AuthResourceType": "Referrals", "AuthPermissionType": "Process"}]}, {"Role": "Nps_Management", "Definitions": [{"AuthResourceType": "Nps", "AuthPermissionType": "Manage"}]}, {"Role": "ClinicalQuestion_Manage", "Definitions": [{"AuthResourceType": "ClinicalQuestion", "AuthPermissionType": "Manage"}]}, {"Role": "Application_Support", "Definitions": [{"AuthResourceType": "Impersonation", "AuthPermissionType": "Manage"}]}, {"Role": "ResponseTemplates_Management", "Definitions": [{"AuthResourceType": "ResponseTemplate", "AuthPermissionType": "Manage"}, {"AuthResourceType": "ResponseTemplate", "AuthPermissionType": "Write"}]}, {"Role": "Workflows_Management", "Definitions": [{"AuthResourceType": "Workflow", "AuthPermissionType": "Manage"}]}, {"Role": "Clinics_Management", "Definitions": [{"AuthResourceType": "Clinics", "AuthPermissionType": "Manage"}]}, {"Role": "Patients_Management", "Definitions": [{"AuthResourceType": "Patients", "AuthPermissionType": "Manage"}]}, {"Role": "Diagnoses_Management", "Definitions": [{"AuthResourceType": "Diagnosis", "AuthPermissionType": "Manage"}]}, {"Role": "Careplantypes_Management", "Definitions": [{"AuthResourceType": "CareplansTypes", "AuthPermissionType": "Manage"}]}, {"Role": "Specialties_Management", "Definitions": [{"AuthResourceType": "Specialties", "AuthPermissionType": "Manage"}]}, {"Role": "Specialists_Management", "Definitions": [{"AuthResourceType": "Specialists", "AuthPermissionType": "Manage"}]}, {"Role": "Practitioners_Management", "Definitions": [{"AuthResourceType": "Practitioners", "AuthPermissionType": "Manage"}]}, {"Role": "Application_Administrators", "Definitions": [{"AuthResourceType": "DeadLetterMessages", "AuthPermissionType": "Manage"}, {"AuthResourceType": "Practitioners", "AuthPermissionType": "SendPanelToPrePaneling"}]}, {"Role": "Billing_Management", "Definitions": [{"AuthResourceType": "BillingSettings", "AuthPermissionType": "Manage"}]}, {"Role": "Billing_Processing", "Definitions": [{"AuthResourceType": "Invoice", "AuthPermissionType": "Process"}, {"AuthResourceType": "InvoiceItem", "AuthPermissionType": "Process"}]}, {"Role": "Treatmentplans_Management", "Definitions": [{"AuthResourceType": "Treatmentplans", "AuthPermissionType": "Manage"}, {"AuthResourceType": "HpuAll", "AuthPermissionType": "Read"}, {"AuthResourceType": "HpuAssignment", "AuthPermissionType": "Read"}]}, {"Role": "CPS_Careplans_Assignment_SP", "Definitions": [{"AuthResourceType": "CareplanAttention", "AuthPermissionType": "Read"}]}, {"Role": "Users_Management", "Definitions": [{"AuthResourceType": "Users", "AuthPermissionType": "Manage"}]}, {"Role": "Groups_Management", "Definitions": [{"AuthResourceType": "Groups", "AuthPermissionType": "Manage"}]}, {"Role": "Nomenclators_Management", "Definitions": [{"AuthResourceType": "Countries", "AuthPermissionType": "Manage"}, {"AuthResourceType": "Provinces", "AuthPermissionType": "Manage"}, {"AuthResourceType": "Notifications_Templates", "AuthPermissionType": "Manage"}]}, {"Role": "Me<PERSON>_Read", "Definitions": [{"AuthResourceType": "<PERSON><PERSON>", "AuthPermissionType": "Read"}]}, {"Role": "Merges_Read_All", "Definitions": [{"AuthResourceType": "<PERSON><PERSON>", "AuthPermissionType": "ReadAll"}]}, {"Role": "Accuro_Management", "Definitions": [{"AuthResourceType": "Accuro", "AuthPermissionType": "Manage"}]}, {"Role": "AI_Summarization_Read", "Definitions": [{"AuthResourceType": "AI_Summarization", "AuthPermissionType": "Read"}]}, {"Role": "ClinicalQuestion_Manage", "Definitions": [{"AuthResourceType": "ClinicalQuestion", "AuthPermissionType": "Manage"}]}, {"Role": "ClinicalQuestion_Write", "Definitions": [{"AuthResourceType": "ClinicalQuestion", "AuthPermissionType": "Write"}]}, {"Role": "ClinicalQuestion_Read_All", "Definitions": [{"AuthResourceType": "ClinicalQuestion", "AuthPermissionType": "ReadAll"}]}, {"Role": "Reports_Read_All", "Definitions": [{"AuthResourceType": "Reports", "AuthPermissionType": "ReadAll"}]}, {"Role": "Reports_Npmetrics_Read", "Definitions": [{"AuthResourceType": "Reports_Npmetrics", "AuthPermissionType": "Read"}]}, {"Role": "ExportRequest_Read_All", "Definitions": [{"AuthResourceType": "ExportRequest", "AuthPermissionType": "ReadAll"}]}, {"Role": "ExportRequest_Read", "Definitions": [{"AuthResourceType": "ExportRequest", "AuthPermissionType": "Read"}]}, {"Role": "PatientPools_Read_All", "Definitions": [{"AuthResourceType": "PatientPool", "AuthPermissionType": "ReadAll"}]}, {"Role": "PatientPools_Read", "Definitions": [{"AuthResourceType": "PatientPool", "AuthPermissionType": "Read"}]}, {"Role": "AIPrompt_Manage", "Definitions": [{"AuthResourceType": "AiPrompts", "AuthPermissionType": "Manage"}]}, {"Role": "Practitioners_Read", "Definitions": [{"AuthResourceType": "PractitionersMine", "AuthPermissionType": "Read"}]}]}}