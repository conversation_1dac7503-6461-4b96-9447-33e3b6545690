﻿using Aoshield.Core.Entities.Models;
using Katana.Services.Common;
using Katana.Services.InquiryFormQuestions;
using Katana.Services.InquiryFormQuestions.Model;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// InquiryFormQuestion CRUD service
    /// </summary>
    public class InquiryFormQuestionsServiceTest : IInquiryFormQuestionsServices
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<AddInquiryFormQuestionDto> Add(AddInquiryFormQuestionDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new AddInquiryFormQuestionDto(), cancellation);

        ///<inheritdoc/>
        public async Task<IPagedResults<InquiryFormQuestionDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<InquiryFormQuestionDto>(
                [], default,
                default, default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<InquiryFormQuestionDto> GetById(int id,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new InquiryFormQuestionDto() { Id = id });

        /// <inheritdoc/>     )
        public async Task<UpdateInquiryFormQuestionDto> Update(UpdateInquiryFormQuestionDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new UpdateInquiryFormQuestionDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) => Task.Run(() =>
            confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<IPagedResults<InquiryEntityRecordCommonDto>> GetRecordsByEntityRelated(
            string entity, SieveModel query, CancellationToken cancellation = default)
            => await Task.Run(() => new PagedResults<InquiryEntityRecordCommonDto>(
                [], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public List<string> GeInquiriyEntities() => [];

        ///<inheritdoc/>
        public async Task<IEnumerable<string>> GetQuestionsValuesByQuestion(int questionId,
            CancellationToken cancellation)
            => await Task.Run(() => new List<string>() { });

        ///<inheritdoc/>
        public async Task<IPagedResults<InquiryFormQuestionDto>> GetQuestionsWithValuesByStep(
            int stepId, SieveModel query, CancellationToken cancellation = default)
            => await Task.Run(() => new PagedResults<InquiryFormQuestionDto>(
                [], default,
                default, default, default, default, default, default, default));

        ///<inheritdoc/>
        public Task<int> ReassignQuestion(ReassignInquiryFormQuestionDto[] dtoArr, CancellationToken cancellation = default) => Task.Run(() => 0);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion
    }
}
