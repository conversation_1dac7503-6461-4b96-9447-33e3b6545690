﻿import { BaseUpdateDto } from "../BaseUpdateDto";
import { BaseUserEntityDto } from "../BaseUserEntityDto";
import { ClinicCommonDto } from "../Common/ClinicCommonDto";
import { PharmacyCommonDto } from "../Common/PharmacyCommonDto";
import { PractitionerCommonDto } from "../Common/PractitionerCommonDto";
import { SyncStatus } from "../Common/SyncStatus";
import { SynchronizableEntityDto } from "../Common/SynchronizableEntityDto";
import { NoteDto } from "../Notes/NoteDto";
import { ProvinceDto } from "../Provinces/ProvinceDto";
import { UserRegistrationStatus } from "../Users/<USER>";

export interface PatientDto extends BaseUserEntityDto, SynchronizableEntityDto {
    patientId: number | null;
    age: number;
    pronouns: string | null;
    preferredContactPhone: string | null;
    addressType: string | null;
    phn: string | null;
    otherPhn: string | null;
    phnProvince: ProvinceDto | null;
    diagnoses: string | null;
    custodianGp: PractitionerCommonDto | null;
    notesCount: number;
    userRegistrationStatus: UserRegistrationStatus;
    pharmacy: PharmacyCommonDto | null;
    syncStatus: SyncStatus;
    lastAppointmentDate: string | null;
    panelingPosition?: number | null;
    allowEmailCommunication: boolean | null;
    prePaneling: boolean;
    prePanelingStartDate: string;
    clinic: ClinicCommonDto;
    hasAiSummary: boolean;
    clinicId?: number;
    ineligible: boolean;
    notes: NoteDto[];
    waitingForClinicalQuestions: boolean;
    hasActiveCQRequest: boolean;
    lastContactedByHPC: string | null;
}

export type PatientPanelingSortPropsType = Pick<PatientDto, "age" | "lastAppointmentDate" | "gender"> &
    BaseUpdateDto;
