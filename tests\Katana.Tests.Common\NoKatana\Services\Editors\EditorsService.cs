﻿using Aoshield.Core.DataAccess;
using Aoshield.Core.Entities.Models;
using FluentValidation;
using Katana.Tests.Common.NoKatana.Core.Entities;
using Katana.Tests.Common.NoKatana.Services.Editors.Models;

namespace Katana.Tests.Common.NoKatana.Services.Editors
{
    /// <summary>
    /// Editor CRUD service
    /// </summary>
    /// <remarks>
    /// Main constructor
    /// </remarks>
    public class EditorsService(
        IKrudder krudder,
        IValidator<Editor> validator) : IEditorsService
    {
        private readonly IKrudder _krudder = krudder;
        private readonly IValidator<Editor> _validator = validator;

        #region CRUD

        ///<inheritdoc/>
        public async Task<AddEditorDto> Add(AddEditorDto dto, CancellationToken cancellation = default)
        {
            var entity = _krudder.Map(dto, _krudder.Mapper.Map, new Editor(), GlobalActions.Add);

            //saving the entity with status logs
            entity = await _krudder.Add(entity, _validator, cancellation: cancellation);
            dto.Id = entity.Id;
            return dto;
        }

        ///<inheritdoc/>
        public async Task<IPagedResults<EditorDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await _krudder.GetAsPagedResults<Editor, EditorDto>(query, cancellation: cancellation);

        /// <inheritdoc/>
        public async Task<EditorDto> GetById(int id, CancellationToken cancellation = default) =>
            await _krudder.GetById<Editor, EditorDto>(id, cancellation: cancellation);

        /// <inheritdoc/>
        public async Task<UpdateEditorDto> Update(UpdateEditorDto dto, CancellationToken cancellation = default)
        {
            var entity =
                await _krudder.GetById<Editor>(dto.Id, track: true, cancellation: cancellation);
            entity = _krudder.Map(dto, _krudder.Mapper.Map, entity,
                GlobalActions.Update); //setting new values

            await _krudder.Update(entity, _validator, cancellation: cancellation);
            return dto;
        }

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await _krudder.Delete<Editor, EditorDto>(id, _validator, cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await _krudder.Restore<Editor, EditorDto>(id, null, cancellation: cancellation);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default)
            => _krudder.DisableBatch(confirmationNoteDtos, _validator, cancellation: cancellation);

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true, CancellationToken cancellation = default)
            => _krudder.EnableBatch(confirmationNoteDtos, _validator, cancellation: cancellation);

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) => throw new NotImplementedException();

        #endregion
    }
}
