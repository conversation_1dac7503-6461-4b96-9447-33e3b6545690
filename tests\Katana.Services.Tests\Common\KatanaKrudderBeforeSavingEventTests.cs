using Aoshield.Core.DataAccess;
using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using AutoMapper;
using Bogus;
using Katana.Core.Entities;
using Katana.Tests.Common.Utils;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sieve.Models;
using Xunit;

namespace Katana.Services.Tests.Common
{
    /// <summary>
    /// KatanaDbContext (the real one) tests.
    /// </summary>
    public class KatanaKrudderBeforeSavingEventTests : ServicesUnitTestConfiguration
    {
        private readonly Krudder<User> _krudder;
        private readonly IDataContext _dataContext;

        /// <summary>
        /// Public constructor
        /// </summary>
        public KatanaKrudderBeforeSavingEventTests(ConfigurationFixture configurationFixture) : base(configurationFixture)
        {
            _dataContext = ServiceProvider.GetService<IDataContext>();
            _krudder = new Krudder<User>(ServiceProvider.GetService<DataContextTransactionTracker>(),
                ServiceProvider.GetService<IServiceProvider>(),
                ServiceProvider.GetService<IServiceScopeFactory>(),
                ServiceProvider.GetService<IDataContext>(),
                ServiceProvider.GetService<ILogger<IKrudder<User>>>(),
                ServiceProvider.GetService<ISieveProcessor>(),
                ServiceProvider.GetService<IOptions<SieveOptions>>(),
                ServiceProvider.GetService<IMapper>(),
                ServiceProvider.GetService<IOptions<KrudderDbConfiguration>>(),
                ServiceProvider.GetService<IUniqueValidationController<User>>(),
                ServiceProvider.GetService<IClaimsPrincipalContext<User>>());
        }

        /// <summary>
        /// Test for BeforeSaving event
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task OnBeforeSaving_EventFired()
        {
            // Arrange
            var country = new Faker<Country>()
                .RuleFor(c => c.Name, x => x.Random.Words(3))
                .Generate();

            var eventFired = false;

            // Act
            //testing saving event
            void On_SavingChanges(object _, IList<EntityEntry> entries) => eventFired = true;
            _dataContext.SavingChanges += On_SavingChanges;
            await _krudder.ExecuteInTransaction(async (cancellation) =>
            {
                await _krudder.Add(country, null, cancellation: cancellation);
            }, CancellationToken.None);
            _dataContext.SavingChanges -= On_SavingChanges;

            // Assert
            Assert.True(eventFired);
        }
    }


}
