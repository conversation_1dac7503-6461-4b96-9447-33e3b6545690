﻿using Aoshield.Core.DataAccess.Models;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.Services.ApiCall;
using Aoshield.Services.Core.Identity;
using Aoshield.Services.Core.Identity.Entities;
using Aoshield.Services.Core.Identity.Models;
using Katana.Services.Users.Models;

namespace Katana.WebApp.Tests.Services
{
    /// <summary>
    /// User CRUD service
    /// </summary>
    public class UsersServiceTest : IUsersService<User, UserDto, BaseAddUserDto, BaseUpdateUserDto>
    {
        #region CRUD

        ///<inheritdoc/>
        public async Task<BaseAddUserDto>
            Add(BaseAddUserDto dto, string groupObjectId = null, CancellationToken cancellation = default) =>
            await Task.Run(() => new BaseAddUserDto(), cancellation);

        ///<inheritdoc/>
        public Task<BaseAddUserDto> Add(BaseAddUserDto dto,
            CancellationToken cancellation = default) => throw new NotImplementedException();

        ///<inheritdoc/>
        public async Task<IPagedResults<UserDto>> GetAsPagedResults(SieveModel query,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new PagedResults<UserDto>(new List<UserDto>(), default, default,
                default, default, default, default, default, default));

        /// <inheritdoc/>
        public async Task<UserDto> GetById(int id, CancellationToken cancellation = default) =>
            await Task.Run(() => id <= 0 ? null : new UserDto() { Id = id });

        /// <inheritdoc/>
        public async Task<BaseUpdateUserDto> Update(BaseUpdateUserDto dto,
            CancellationToken cancellation = default) =>
            await Task.Run(() => new BaseUpdateUserDto() { Id = dto.Id });

        ///<inheritdoc/>
        public async Task<int?> Delete(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DeleteBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public async Task<int[]> RestoreBatch(ConfirmationNoteDto[] confirmationNoteDtos, bool notify = true,
            CancellationToken cancellation = default)
            => await Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> DisableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        /// <inheritdoc />
        public Task<int[]> EnableBatch(ConfirmationNoteDto[] confirmationNoteDtos,
            bool notify = true,
            CancellationToken cancellation = default) =>
            Task.Run(() => confirmationNoteDtos.Select(x => x.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int?> Restore(int id, bool notify = true,
            CancellationToken cancellation = default) =>
            await Task.Run(() => id);

        ///<inheritdoc/>
        public async Task<int[]> DisableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        ///<inheritdoc/>
        public async Task<int[]> EnableBatch(ChangeActiveStatusWithReasonDto[] dtos, bool notify = true, CancellationToken cancellation = default) =>
            await Task.Run(() => dtos.Select(d => d.Id).ToArray());

        #endregion

        #region Status

        ///<inheritdoc/>
        public async Task<IPagedResults<BaseStatusLogEntityDto<Status>>>
            GetStatusLogsAsPagedResults(int parentId, SieveModel query,
                CancellationToken cancellation = default) =>
            await Task.Run(() =>
                new PagedResults<BaseStatusLogEntityDto<Status>>(
                    new List<BaseStatusLogEntityDto<Status>>(), default, default, default, default,
                    default, default, default, default));

        #endregion

        #region Custom

        /// <inheritdoc />
        public async Task<IPagedResults<DeferredUserInviteDto>> GetUserInvites(SieveModel query,
            CancellationToken cancellation = default) => await Task.Run(() =>
            new PagedResults<DeferredUserInviteDto>(new List<DeferredUserInviteDto>(), default,
                default,
                default, default, default, default, default, default), cancellation);

        /// <inheritdoc />
        public Task<User> InviteUser(UserInviteRequest userInviteRequest, bool instantExecution = false, CancellationToken cancellationToken = default) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<DeferredRequestResponse> ProcessInvite(int invites, CancellationToken cancellationToken) => throw new NotImplementedException();

        /// <inheritdoc />
        public Task<bool> UpdateSettings(int UserId, UpdateUserSettingsDto dto,
            CancellationToken cancellation) => throw new NotImplementedException();

        /// <inheritdoc />
        public async Task<IPagedResults<UserSettingsDto>> GetSettings(int userId, SieveModel settings,
            CancellationToken cancellation = default) => await Task.Run(() => new PagedResults<UserSettingsDto>(new List<UserSettingsDto>(), default, default,
            default, default, default, default, default, default));

        /// <inheritdoc />
        public async Task<UserDto> OauthSingIn(CancellationToken cancellationToken = default) => await Task.Run(() => new UserDto());

        #endregion
    }
}
