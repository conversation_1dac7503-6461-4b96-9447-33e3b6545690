using Aoshield.Core.DataAccess.Sieve;
using Aoshield.Core.Entities;
using Aoshield.Core.Entities.Abstractions;
using Aoshield.Core.Entities.Models;
using Aoshield.Core.EntityActions;
using AutoMapper;
using FluentValidation;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Sieve.Models;
using Sieve.Services;
using System.Reflection;

namespace Aoshield.Core.DataAccess
{
    /// <summary>
    /// Delegate for after query a batch
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entities"></param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    public delegate Task AfterQueryBatchDelegate<T>(IEnumerable<T> entities, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// Before entity save delegate
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public delegate Task BeforeSaveDelegate<T>(T entity, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// Before batch save delegate
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <param name="index"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public delegate Task BeforeSaveBatchDelegate<T>(T entity, int index, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// Before object batch save delegate
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="index"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public delegate Task BeforeSaveObjectBatchDelegate(object entity, int index, CancellationToken cancellationToken);

    /// <summary>
    /// Delegate for before save with rules
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entity">Entity being saved</param>
    /// <param name="index">Index within the batch</param>
    /// <param name="validationContextData">Validation context data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    public delegate Task<bool?> BeforeSaveWithRulesDelegate<T>(T entity, int index, IDictionary<string, object> validationContextData, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// After entity save delegate
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entity">Entity being saved</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    public delegate Task AfterSaveDelegate<T>(T entity, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// After batch save delegate
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <param name="index"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public delegate Task AfterSaveBatchDelegate<T>(T entity, int index, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// After object batch save delegate
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="index"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public delegate Task AfterSaveObjectBatchDelegate(object entity, int index, CancellationToken cancellationToken);

    /// <summary>
    /// Delegate for after batch save with rules
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entities"></param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    public delegate Task AfterSaveAllBatchDelegate<T>(IEnumerable<T> entities, CancellationToken cancellationToken) where T : BaseEntity;

    /// <summary>
    /// Delegate for after batch save with rules
    /// </summary>
    /// <param name="entities"></param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    public delegate Task AfterSaveAllObjectBatchDelegate(IEnumerable<object> entities, CancellationToken cancellationToken);

    /// <summary>
    /// Krudder interface
    /// </summary>
    public interface IKrudder
    {
        /// <summary>
        /// Logger handler.
        /// </summary>
        ILogger Logger { get; }

        /// <summary>
        /// Mapper
        /// </summary>
        IMapper Mapper { get; }

        /// <summary>
        /// Sieve processor
        /// </summary>
        ISieveProcessor SieveProcessor { get; }

        /// <summary>
        /// Clears DbContext Tracker
        /// </summary>
        void ClearTracker();

        /// <summary>
        /// Gets Db enities types
        /// </summary>
        /// <returns></returns>
        IEnumerable<Type> GetEntitiesTypes();

        /// <summary>
        /// Resets the DbContext current user
        /// </summary>
        /// <returns></returns>
        void InitCurrentUser();

        /// <summary>
        /// Gets logged user's id
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<int> GetCurrentUserId(CancellationToken cancellation = default);

        /// <summary>
        /// Gets logged user's id
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<string> GetCurrentUserObjectId(CancellationToken cancellation = default);

        /// <summary>
        /// Creates a IQueryable that can be used to query instances of TEntity.
        /// </summary>
        /// <param name="activeStatus">Active status to filter</param>
        /// <param name="customActiveFilterMethod">Custom active method to apply on top of Active method</param>
        /// <param name="includedDisabled"></param>
        /// <typeparam name="TEntity">The type of entity for which a set should be returned.</typeparam>
        /// <returns>A set for the given entity type.</returns>
        IQueryable<TEntity> Set<TEntity>(EntityActiveStatus activeStatus = EntityActiveStatus.Active,
            string customActiveFilterMethod = null, bool includedDisabled = true) where TEntity : class;

        /// <summary>
        /// Saves changes to the underlaying data layer
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>A task that represents the asynchronous save operation. The task result contains the number of entities written to underlaying data layer.</returns>
        Task<int> SaveChangesAsync(CancellationToken cancellation = default);

        /// <summary>
        /// Executes provided function in a transaction. Ensures that custom CRUD logic gets executed while saving
        /// </summary>
        /// <param name="actionExecute">Action to execute within the transaction</param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        Task<int> ExecuteInTransaction(Func<CancellationToken, Task> actionExecute, CancellationToken cancellation);

        /// <summary>
        /// Load collection by name. Good when lazy load is being used.
        /// </summary>
        /// <param name="entity">Entity to load the collection into</param>
        /// <param name="collectionMember">Collection member</param>
        /// <param name="cancellation">Cancellation token</param>
        Task LoadCollection(object entity, PropertyInfo collectionMember, CancellationToken cancellation);

        /// <summary>
        /// Get foreign keys of provided entity
        /// </summary>
        /// <param name="entityType">Entity type</param>
        /// <returns>Foreign keys</returns>
        IEnumerable<IForeignKey> GetForeignKeys(Type entityType);

        ///<inheritdoc cref="IDataContext.GetPrimaryKeyPropertyInfo(Type)"/>
        PropertyInfo GetPrimaryKeyPropertyInfo(Type entityType);

        ///<inheritdoc cref="IDataContext.GetForeignKeyIdPropertyInfo(Type, PropertyInfo)"/>
        PropertyInfo GetForeignKeyIdPropertyInfo(Type entityType, PropertyInfo navigationProperty);

        /// <summary>
        /// Create scope and setting up the current user
        /// </summary>
        /// <param name="userId">User's Id</param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        Task<IServiceScope> GetUserScope(int userId, CancellationToken cancellation);

        /// <summary>
        /// Create scope and setting up the current user
        /// </summary>
        /// <param name="objectId">User's object Id</param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        Task<IServiceScope> GetUserScope(string objectId, CancellationToken cancellation);

        /// <summary>
        /// Get current user Graph's information
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        ///
        public Task<User> GetCurrentUserGraphInfo(CancellationToken cancellationToken);

        /// <summary>
        /// Gets the object id from the claims principal
        /// </summary>
        /// <returns></returns>
        public string GetClaimsPrincipalObjectId();

        #region CREATE

        /// <summary>
        /// Adds an Entity from a DTO
        /// </summary>
        /// <typeparam name="TAddDto">Add DTO type</typeparam>
        /// <typeparam name="T">Entity Type</typeparam>
        /// <param name="dto">Dto to add entity from</param>
        /// <param name="dtoInToEntity">Function for projecting the DTO into the entity. If none is provided, "Mapper.Map&lt;TEntity&gt;" will be used.</param>
        /// <param name="beforeSave">Function to be invoked before saving the entity to the database</param>
        /// <param name="afterSave">Function to be invoked after saving the entity to the database; Only invoked if changes were detected and saved to the database</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="rulesSet">Validation rules set.</param>
        /// <param name="includeRulesNotInRuleSet"></param>
        /// <returns>Added entity converted to DTO</returns>
        Task<TAddDto> Add<TAddDto, T>(TAddDto dto, IValidator<T> validator,
            Func<TAddDto, T> dtoInToEntity = null,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            bool includeRulesNotInRuleSet = true,
            CancellationToken cancellation = default)
            where TAddDto : BaseAddDto
            where T : BaseEntity;

        /// <summary>
        /// Adds Entity
        /// </summary>
        /// <returns>Added entity</returns>
        /// <inheritdoc cref="Add{TAddDto, T}(TAddDto, IValidator{T}, Func{TAddDto, T}, IDictionary{string, object}, BeforeSaveDelegate{T}, AfterSaveDelegate{T}, string, bool, CancellationToken)"/>
        Task<T> Add<T>(T entity, IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            bool includeRulesNotInRuleSet = true,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        /// <summary>
        /// Adds entities batch from dtos
        /// </summary>
        /// <typeparam name="TAddDto">Add DTO type</typeparam>
        /// <typeparam name="T">Entity Type</typeparam>
        /// <param name="dtos">Dtos to add entities from</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="dtoInToEntity">Function for projecting the DTO into the entity. If none is provided, "Mapper.Map&lt;TEntity&gt;" will be used.</param>
        /// <param name="entityToTDtoOut">Function for projecting the T into the TDtoOut. If none is provided, "Mapper.Map&lt;TEntity&gt;" will be used.</param>
        /// <param name="validationContextData">Validation Context Data</param>
        /// <param name="beforeSave">Function to be invoked before saving each entity to the database</param>
        /// <param name="afterSave">Function to be invoked after saving each entity to the database; Only invoked if changes were detected and saved to the database</param>
        /// <param name="afterBatchSave">Function to be invoked after the whole batch vas saved</param>
        /// <param name="cancellation">Cancellation Token</param>
        /// <param name="rulesSet">Validation rules sets</param>
        /// <returns>Added entities converted to dtos</returns>
        public Task<TAddDto[]> AddBatch<TAddDto, T>(
            TAddDto[] dtos,
            IValidator<T> validator,
            Func<TAddDto, T> dtoInToEntity = null,
            Func<T, TAddDto> entityToTDtoOut = null,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where TAddDto : BaseAddDto
            where T : BaseEntity;

        /// <summary>
        /// Add entities batch
        /// </summary>
        /// <returns>Added entities</returns>
        ///<inheritdoc cref="AddBatch{TAddDto, T}(TAddDto[], IValidator{T}, Func{TAddDto, T}, Func{T, TAddDto}, IDictionary{string, object}, BeforeSaveBatchDelegate{T}, AfterSaveBatchDelegate{T}, AfterSaveAllBatchDelegate{T}, string, CancellationToken)"/>
        public Task<List<T>> AddBatch<T>(
            List<T> entities, IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Add entities batch
        /// </summary>
        /// <returns>Added entities</returns>
        public Task<List<object>> AddBatch(
            List<object> entities,
            BeforeSaveObjectBatchDelegate beforeSave = null,
            AfterSaveObjectBatchDelegate afterSave = null,
            AfterSaveAllObjectBatchDelegate afterBatchSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default);

        #endregion

        #region READ

        /// <summary>
        /// Get Entity by id and return a DTO
        /// </summary>
        /// <typeparam name="TDtoOut">DTO type</typeparam>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity id</param>
        /// <param name="configureSet">Function for configuring the set before performing the search (Adding relations).</param>
        /// <param name="projectTo">Function for projecting the entity into the DTO</param>
        /// <param name="track">Wether the set will be used with "AsNoTracking" or not</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>DTO</returns>
        Task<TDtoOut> GetById<T, TDtoOut>(int id,
        Func<IQueryable<T>, IQueryable<T>> configureSet = null,
        Func<IQueryable<T>, IQueryable<TDtoOut>> projectTo = null, bool track = true,
        CancellationToken cancellation = default)
        where T : BaseEntity
        where TDtoOut : BaseEntityDto;

        /// <summary>
        /// Get Entity by id.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="id">Entity id</param>
        /// <param name="configureSet">Function for configuring the set before performing the search (Adding relations)</param>
        /// <param name="track">Wether the set will be used with "AsNoTracking" or not</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Entity</returns>
        Task<T> GetById<T>(int id, Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            bool track = true, CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Get paged results of DTOs.
        /// </summary>
        /// <typeparam name="TDtoOut">DTO type</typeparam>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="query">Sieve parameters</param>
        /// <param name="configureSet">Function for configuring the set before performing the search</param>
        /// <param name="projectTo">Function for projecting the entity into the DTO</param>
        /// <param name="entityActiveStatus">Entity active status</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Paged list of Dtos</returns>
        Task<IPagedResults<TDtoOut>> GetAsPagedResults<T, TDtoOut>(SieveModel query,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            Func<IQueryable<T>, IQueryable<TDtoOut>> projectTo = null,
            EntityActiveStatus entityActiveStatus = EntityActiveStatus.Any,
            CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto;

        /// <summary>
        /// Get paged results of entities.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query">Sieve parameters</param>
        /// <param name="configureSet">Function for configuring the set before performing the search</param>
        /// <param name="entityActiveStatus">Entity active status</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Paged list</returns>
        Task<IPagedResults<T>> GetAsPagedResults<T>(SieveModel query,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            EntityActiveStatus entityActiveStatus = EntityActiveStatus.Any,
            CancellationToken cancellation = default) where T : BaseEntity;

        #endregion

        #region UPDATE

        /// <summary>
        /// Updates an entity from an Id.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id"></param>
        /// <param name="validator">Entity validator</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="beforeSave">Function to be invoked before saving the entity to the database</param>
        /// <param name="afterSave">Function to be invoked after saving the entity to the database; Only invoked if changes were detected and saved to the database</param>
        /// <param name="configureSet">Function for configuring the set before performing the database query</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <param name="includeRulesNotInRuleSet"></param>
        /// <returns>Id</returns>
        Task<int> Update<T>(
            int id,
            IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            string rulesSet = null,
            bool includeRulesNotInRuleSet = true,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Updates an entity from a dto.
        /// </summary>
        /// <typeparam name="TDto">Update DTO type</typeparam>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="dto">Dto to update entity from</param>
        /// <param name="mapper">Function for mapping from the DTO to the entity</param>
        /// <param name="validator">Entity validator</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="beforeSave">Function to be invoked before saving the entity to the database</param>
        /// <param name="afterSave">Function to be invoked after saving the entity to the database; Only invoked if changes were detected and saved to the database</param>
        /// <param name="configureSet">Function for configuring the set before performing the database query</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <param name="includeRulesNotInRuleSet"></param>
        /// <returns>Updated entity converted to DTO</returns>
        Task<TDto> Update<TDto, T>(
            TDto dto,
            IValidator<T> validator,
            Func<TDto, T, T> mapper = null,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            string rulesSet = null,
            bool includeRulesNotInRuleSet = true,
            CancellationToken cancellation = default)
            where TDto : BaseUpdateDto
            where T : BaseEntity;

        /// <summary>
        /// Updates an entity.
        /// </summary>
        /// <returns>Updated entity</returns>
        ///<inheritdoc cref="Update{TDto, T}"/>
        Task<T> Update<T>(
            T entity,
            IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            bool includeRulesNotInRuleSet = true,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        /// <summary>
        /// Update entities from dtos.
        /// </summary>
        /// <typeparam name="TDtoIn">Update dto type</typeparam>
        /// <param name="dtoInArr">Entities dtos</param>
        /// <param name="validator"></param>
        /// <param name="rulesSet"></param>
        /// <param name="afterQuery"></param>
        /// <param name="mapper"></param>
        /// <param name="validationContextData"></param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="cancellation"></param>
        /// <typeparam name="T">Entity type</typeparam>
        /// <returns>Updated entities count</returns>
        Task<int> UpdateBatch<TDtoIn, T>(TDtoIn[] dtoInArr,
            IValidator<T> validator,
            string rulesSet = null,
            AfterQueryBatchDelegate<T> afterQuery = null,
            Func<TDtoIn, T, T> mapper = null,
            IDictionary<string, object> validationContextData = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveWithRulesDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            CancellationToken cancellation = default)
            where TDtoIn : BaseUpdateDto
            where T : BaseEntity;

        /// <summary>
        /// Update entities from ids.
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="validator"></param>
        /// <param name="rulesSet"></param>
        /// <param name="afterQuery"></param>
        /// <param name="validationContextData"></param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="cancellation"></param>
        /// <returns>Updated entities count</returns>
        Task<int> UpdateBatch<T>(
            int[] ids,
            IValidator<T> validator,
            string rulesSet,
            AfterQueryBatchDelegate<T> afterQuery = null,
            IDictionary<string, object> validationContextData = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveWithRulesDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Update entities from ids.
        /// </summary>
        /// <returns>Updated entities count</returns>
        Task<int> UpdateBatch<T>(
            IValidator<T> validator,
            string rulesSet,
            Func<IQueryable<T>, IQueryable<T>> configureSet,
            AfterQueryBatchDelegate<T> afterQuery = null,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveWithRulesDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Update entities batch.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Entities to update</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Updated entities</returns>
        Task<List<T>> UpdateBatch<T>(
            IEnumerable<T> entities,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Update entities batch.
        /// </summary>
        /// <param name="entities">Entities to update</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns>Updated entities</returns>
        public Task<List<object>> UpdateBatch(
            IEnumerable<object> entities,
            BeforeSaveObjectBatchDelegate beforeSave = null,
            AfterSaveObjectBatchDelegate afterSave = null,
            AfterSaveAllObjectBatchDelegate afterBatchSave = null,
            CancellationToken cancellation = default);

        #endregion

        #region DELETE

        /// <summary>
        /// Deletes an entity from id and returns a dto.
        /// </summary>
        /// <typeparam name="TDtoOut">Dto type</typeparam>
        /// <typeparam name="T">Dto type</typeparam>
        ///<inheritdoc cref="Delete{T}(int, IValidator{T}, BeforeSaveDelegate{T}, AfterSaveDelegate{T}, IDictionary{string, object}, string, CancellationToken)"/>
        Task<int?> Delete<T, TDtoOut>(
            int id,
            IValidator<T> validator,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto;

        /// <summary>
        /// Deletes an entity from id.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity id</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns>Deleted entity's id</returns>
        Task<int?> Delete<T>(
            int id,
            IValidator<T> validator,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Delete entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="ids">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> DeleteBatch<T>(
            int[] ids,
            IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Delete entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="confirmationNotes">Array of Ids with confirmation notes</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>hk
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> DeleteBatch<T>(
            ConfirmationNoteDto[] confirmationNotes,
            IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        #endregion

        #region RESTORE

        /// <summary>
        /// Restore an entity and return dto.
        /// Set Entity property SoftDeleteLevel = 0
        /// </summary>
        /// <typeparam name="TDtoOut">Dto type</typeparam>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity id</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="rulesSet"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<int?> Restore<T, TDtoOut>(int id,
            IValidator<T> validator,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity
            where TDtoOut : BaseEntityDto;

        /// <summary>
        /// Restore an entity.
        /// Set Entity property SoftDeleteLevel = 0
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="id">Entity id</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="rulesSet"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<int?> Restore<T>(int id,
            IValidator<T> validator,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Restore an entity.
        /// Set Entity property SoftDeleteLevel = 0
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entity">Entity</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="rulesSet"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<int?> Restore<T>(T entity,
            IValidator<T> validator,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Restore entities from a ids array and return a list of restored entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="ids">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="configureSet"></param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> RestoreBatch<T>(int[] ids,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Restore entities from a ids array and return a list of restored entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="confirmationNotes"></param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> RestoreBatch<T>(
            ConfirmationNoteDto[] confirmationNotes,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        #endregion

        #region UTILS

        /// <summary>
        /// Make the mapping Entity to Dto or Dto to Entity
        /// </summary>
        /// <typeparam name="TIn">Entity or Dto type</typeparam>
        /// <typeparam name="TOut">Entity or Dto type</typeparam>
        /// <param name="objInToObjOut">Function for mapping from object In to object Out</param>
        /// <param name="objIn">Input object for mapping</param>
        /// <param name="failedMethod">Specify the method to be executed</param>
        /// <returns></returns>
        TOut Map<TIn, TOut>(Func<TIn, TOut> objInToObjOut, TIn objIn, GlobalActions failedMethod)
            where TIn : class
            where TOut : class;

        /// <summary>
        /// Make the mapping Entity to Dto or Dto to Entity
        /// </summary>
        /// <typeparam name="TIn">Entity or Dto type</typeparam>
        /// <typeparam name="TOut">Entity or Dto type</typeparam>
        /// <param name="objIn">Input object for mapping</param>
        /// <param name="objInToObjOut">Function for mapping from object In to object Out</param>
        /// <param name="objOut">Output object mapped</param>
        /// <param name="failedMethod">Specify the method to be executed</param>
        /// <returns></returns>
        TOut Map<TIn, TOut>(TIn objIn, Func<TIn, TOut, TOut> objInToObjOut, TOut objOut,
            GlobalActions failedMethod)
            where TIn : class
            where TOut : class;

        /// <summary>
        /// Checks wheter the current user can perform the action on the entity.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="rulesSet"></param>
        /// <param name="entity"></param>
        /// <param name="contextData"></param>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        public Task CheckCanAction<T>(string rulesSet, T entity,
            IDictionary<string, object> contextData, CancellationToken cancellation)
            where T : BaseEntity;

        #endregion

        #region Disable

        /// <summary>
        /// Disable entities from a ids array and return a list of disabled entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="confirmationNotes">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <returns></returns>
        Task<int[]> DisableBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        /// <summary>
        /// Disable entities with a reason from an array and return a list of disabled entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="confirmationNotes">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <returns></returns>
        Task<int[]> DisableBatch<T>(ChangeActiveStatusWithReasonDto[] confirmationNotes, IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        #endregion

        #region Enable

        /// <summary>
        /// Enable entities from a ids array and return a list of enabled entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="confirmationNotes">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> EnableBatch<T>(ConfirmationNoteDto[] confirmationNotes, IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        /// <summary>
        /// Enable entities with a reason from an array and return a list of enabled entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="dtos">Entities enabling data array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="configureSet"></param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<int[]> EnableBatch<T>(ChangeActiveStatusWithReasonDto[] dtos, IValidator<T> validator,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default)
            where T : BaseEntity;

        #endregion

        #region Lock

        /// <summary>
        /// PickUp an entity
        /// </summary>
        /// <param name="id"></param>
        /// <param name="validator"></param>
        /// <param name="validationContextData"></param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="cancellation"></param>
        /// <param name="rulesSet"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        Task<int?> PickUp<T>(int id, IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// PickUp entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="baseUpdateDtos">BaseUpdateDto array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<List<int>> PickUpBatch<T>(IEnumerable<BaseUpdateDto> baseUpdateDtos,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// PickUp entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="ids">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<IEnumerable<T>> PickUpBatch<T>(IEnumerable<int> ids,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Release entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<List<T>> PickUpBatch<T>(
            List<T> entities,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        #endregion

        #region Release

        /// <summary>
        /// Release an entity
        /// </summary>
        /// <param name="id"></param>
        /// <param name="validator"></param>
        /// <param name="validationContextData"></param>
        /// <param name="beforeSave"></param>
        /// <param name="afterSave"></param>
        /// <param name="cancellation"></param>
        /// <param name="rulesSet"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        Task<int?> Release<T>(int id, IValidator<T> validator,
            IDictionary<string, object> validationContextData = null,
            BeforeSaveDelegate<T> beforeSave = null,
            AfterSaveDelegate<T> afterSave = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Release entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="baseUpdateDtos">BaseUpdateDto array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<List<int>> ReleaseBatch<T>(IEnumerable<BaseUpdateDto> baseUpdateDtos,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Release entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="ids">Id's array</param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="configureSet"></param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<IEnumerable<T>> ReleaseBatch<T>(IEnumerable<int> ids,
            IValidator<T> validator,
            BeforeSaveBatchDelegate<T> beforeSave = null,
            Func<IQueryable<T>, IQueryable<T>> configureSet = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        /// <summary>
        /// Release entities from a ids array and return a list of deleted entities.
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="entities"></param>
        /// <param name="validator">Entity Validator</param>
        /// <param name="beforeSave">Before save function. Will be call for each entity on the batch</param>
        /// <param name="afterSave"></param>
        /// <param name="afterBatchSave"></param>
        /// <param name="validationContextData">Validation context data</param>
        /// <param name="cancellation">Cancellation token</param>
        /// <param name="rulesSet">Validation rules set</param>
        /// <returns></returns>
        Task<List<T>> ReleaseBatch<T>(
            List<T> entities,
            IValidator<T> validator,
             BeforeSaveBatchDelegate<T> beforeSave = null,
            AfterSaveBatchDelegate<T> afterSave = null,
            AfterSaveAllBatchDelegate<T> afterBatchSave = null,
            IDictionary<string, object> validationContextData = null,
            string rulesSet = null,
            CancellationToken cancellation = default) where T : BaseEntity;

        #endregion
    }

    /// <summary>
    /// Krudder interface
    /// </summary>
    public interface IKrudder<TUser> : IKrudder where TUser : User<TUser>, IUser
    {
        /// <summary>
        /// Gets logged user entity
        /// </summary>
        /// <param name="cancellation">Cancellation token</param>
        /// <returns></returns>
        Task<TUser> GetCurrentUser(CancellationToken cancellation = default);

        /// <summary>
        /// Get current user photo
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        ///
        public Task<(TUser, string)> GetCurrentUserWithPhoto(CancellationToken cancellationToken);

        /// <summary>
        /// Gets active Impersonation
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        public Task<Impersonation<TUser>> GetActiveImpersonation(CancellationToken cancellation = default);

        /// <summary>
        /// Gets whether the current user is impersonating
        /// </summary>
        /// <returns></returns>
        public Task<bool> IsImpersonation(CancellationToken cancellation = default);

        /// <summary>
        /// Gets logged user groups
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        public Task<IEnumerable<string>> GetCurrentUserGroups(CancellationToken cancellation = default);

        /// <summary>
        /// Gets logged user groups names
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        public Task<IEnumerable<string>> GetCurrentUserGroupsNames(CancellationToken cancellation = default);

        /// <summary>
        /// Gets logged user roles
        /// </summary>
        /// <param name="cancellation"></param>
        /// <returns></returns>
        public Task<IEnumerable<string>> GetCurrentUserRoles(CancellationToken cancellation = default);
    }
}
