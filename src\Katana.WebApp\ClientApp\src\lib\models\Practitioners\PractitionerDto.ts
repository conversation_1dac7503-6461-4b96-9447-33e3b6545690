import { ApprovalPeriodOptions } from "lib/models/ApprovalPeriodOptions";
import { SearchableEntity } from "lib/models/Common/SearchableEntity";
import { QuotaUnit } from "lib/models/QuotaUnit";
import { SortCriteriaOptions } from "lib/models/SortCriteriaOptions";
import { PractitionerCommonActionDto } from "./PractitionerCommonActionDto";

export interface PractitionerDto extends SearchableEntity, PractitionerCommonActionDto {
    practId: number | string;
    sp: boolean;
    np: boolean;
    itp: boolean;
    gpsiItp: boolean;
    gpsi: boolean;
    spItp: boolean;
    cps: boolean;
    hpcLead: boolean;
    rp: boolean;
    externalBilling: boolean;
    notesCount: number;
    requestingQuota: number | string | null;
    requestingQuotaUnit: QuotaUnit | null;
    approvingQuota: number | string | null;
    approvingQuotaUnit: QuotaUnit | null;
    approvalPeriod: number | null;
    approvalPeriodOption: ApprovalPeriodOptions | null;
    patientPanelingSortField: string;
    patientPanelingSortCriteria: SortCriteriaOptions | null;
    searchIndex: string;
    dailyRCodeEarningsCap: number | null;
    publicCalendarUrl: string | null;
    maxDailyQuestionsPerPatient: number | string | null;
    assignedPractitionersIds: number[];
}
