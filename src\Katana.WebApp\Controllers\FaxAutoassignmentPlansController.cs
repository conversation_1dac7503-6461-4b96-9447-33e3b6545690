﻿using Katana.Services.FaxAutoassignPlans;
using Katana.Services.FaxAutoassignPlans.Models;
using Microsoft.AspNetCore.Mvc;

namespace Katana.WebApp.Controllers
{
    /// <summary>
    /// Faxes Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class FaxAutoassignmentPlansController : ControllerBase
    {
        private readonly IFaxAutoassingPlansService _faxAutoassingPlansService;

        /// <summary>
        /// Public constructor with all required data
        /// </summary>
        /// <param name="faxAutoassingPlansService">CRUD service</param>
        public FaxAutoassignmentPlansController(
            IFaxAutoassingPlansService faxAutoassingPlansService) =>
            _faxAutoassingPlansService = faxAutoassingPlansService;

        /// <summary>
        /// Creates an Autoassigment Plan
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<FaxAutoassignPlanDto>> CreateAutoAssignPlan(CancellationToken cancellation)
        {
            var plan = await _faxAutoassingPlansService.CreateAutoAssignPlan(cancellation);
            return plan != null ? Ok(plan) : BadRequest();
        }

        /// <summary>
        /// Updates an Autoassigment Plan
        /// </summary>
        /// <param name="id">Plan Id</param>
        /// <param name="dto">New Data</param>
        /// <param name="cancellation"></param>
        /// <returns>FaxAutoassignPlanDto</returns>
        [HttpPut("{id}")]
        public async Task<UpdateFaxAutoassignPlanDto> AdjustAutoAssignGroupDetail(int id,
            UpdateFaxAutoassignPlanDto dto, CancellationToken cancellation) =>
            await _faxAutoassingPlansService.UpdateAutoAssignPlan(id, dto, cancellation);

        /// <summary>
        /// Executes an Autoassigment Plan
        /// </summary>
        /// <returns></returns>
        [HttpPut("{id}/Execute")]
        public async Task<IActionResult> ExecuteAutoAssignPlan(int id,
            CancellationToken cancellation)
        {
            await _faxAutoassingPlansService.ExecuteAutoAssignPlan(id, cancellation);
            return NoContent();
        }

        /// <summary>
        /// Cancels an Autoassigment Plan
        /// </summary>
        /// <returns></returns>
        [HttpPut("{id}/Cancel")]
        public async Task<IActionResult> CancelAutoAssignPlan(int id, CancellationToken cancellation)
        {
            await _faxAutoassingPlansService.CancelAutoAssignPlan(id, cancellation);
            return NoContent();
        }
    }
}
