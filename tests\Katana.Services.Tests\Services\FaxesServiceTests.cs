﻿using Aoshield.Core.DataAccess.Models;
using Katana.Core.Entities;
using Katana.Services.Faxes;
using Katana.Services.Faxes.Models;
using Katana.Services.Tests.Common;
using Katana.Services.Tests.Common.FakeGenerators;
using Katana.Tests.Common.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Katana.Services.Tests.Services
{
    /// <summary>
    /// Faxes Service Tests
    /// </summary>
    public class FaxesServiceTests : BasicCrudServiceTests<IFaxesService, FaxFakeGenerator
        , Fax, FaxListDto, FaxDto, AddFaxDto, UpdateFaxDto>
    {
        private readonly IFaxesService _service;
        private readonly FaxFakeGenerator _entityFakeGenerator;
        private readonly EntityNoteFakeGenerator<FaxNote> _noteFakeGenerator;

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="configurationFixture">Configuration Fixture</param>
        public FaxesServiceTests(ConfigurationFixture configurationFixture) : base(
            configurationFixture)
        {
            _service = ServiceProvider.GetService<IFaxesService>();
            _entityFakeGenerator = ServiceProvider.GetService<FaxFakeGenerator>();
            _noteFakeGenerator = ServiceProvider.GetService<EntityNoteFakeGenerator<FaxNote>>();
        }

        /// <summary>
        /// Test run to Add method
        /// </summary>
        [Fact]
        public override async Task TestAdd() => await Task.Run(() => Task.CompletedTask);

        /// <summary>
        /// Test run to Delete entity method
        /// </summary>
        [Fact]
        public override async Task TestDelete() => await Task.Run(() => Task.CompletedTask);


        #region Notes Tests

        /// <summary>
        /// Test run to AddNote method
        /// </summary>
        [Fact]
        public async Task TestAddNote()
        {
            var parentId = await Krudder.Set<Fax>().Select(c => c.Id).FirstOrDefaultAsync();
            var note = _noteFakeGenerator.Generate(parentId, await Krudder.GetCurrentUserId());

            //Adding note and checking that the note was added
            await GenericTests.TestAddNote(_service, note, await Krudder.GetCurrentUserId(), Krudder, Mapper);
        }

        /// <summary>
        /// Test run to UpdateNote method
        /// </summary>
        [Fact]
        public async Task TestUpdateNote() =>
            await GenericTests.TestUpdateNote<FaxNote, NoteDto, AddNoteDto, UpdateNoteDto>(_service, await Krudder.GetCurrentUserId(), Krudder, Mapper);

        /// <summary>
        /// Test run to DeleteNote method
        /// </summary>
        [Fact]
        public async Task TestDeleteNote() =>
            await GenericTests.TestDeleteNote<FaxNote, NoteDto, AddNoteDto, UpdateNoteDto>(_service, Krudder);

        /// <summary>
        /// Test run to GetNoteById method
        /// </summary>
        [Fact]
        public async Task TestGetNoteById() =>
            await GenericTests.TestGetNoteById<FaxNote, NoteDto, AddNoteDto, UpdateNoteDto>(_service, Krudder);

        /// <summary>
        /// Test run to GetNotes method
        /// </summary>
        [Fact]
        public async Task TestGetNotes() =>
            await GenericTests.TestGetNotes<Fax, NoteDto, AddNoteDto, UpdateNoteDto>(_service, Krudder);

        #endregion

        #region StatusLogs Tests

        /// <summary>
        /// Test run to GetStatusLogsAsPagedResults method
        /// </summary>
        [Fact]
        public async Task TestGetStatusLogs() =>
            await GenericTests.TestGetStatusLogsById<Fax>(_service, Krudder);

        #endregion

        #region WorkflowStatusLogs Tests

        /// <summary>
        /// Test run to GetWorkflowStatusLogsAsPagedResults method
        /// </summary>
        [Fact]
        public async Task TestGetWorkflowStatusLogs()
            => await GenericTests.TestGetWorkflowStatusLogsById<Fax, FaxWorkflowStatus>(_service,
                Krudder);

        #endregion

        #region Operations Tests

        /// <summary>
        /// Test run to AssignToTranscriberGroup method
        /// </summary>
        [Fact]
        public async Task TestAssignToTranscriberGroup()
        {
            var parentId = await Krudder.Set<TranscribersGroup>().Select(g => g.Id)
                .FirstOrDefaultAsync();
            var faxes = Krudder.Set<Fax>()
                .Where(f => f.WorkflowStatus == FaxWorkflowStatus.New).ToList();

            //Ensuring that there is a fax with WorkflowStatus equal to New
            if (faxes.Count == 0)
            {
                var entityDto = await _entityFakeGenerator.GenerateAdd(Krudder);
                await _service.Add(entityDto);
                faxes = [.. Krudder.Set<Fax>().Where(f => f.WorkflowStatus == FaxWorkflowStatus.New)];
            }

            //var addIds = faxes.Select(f => f.Id).ToList();
            //var operationDto = new OperationsDto() { Id = parentId, AddIds = addIds };

            ////Assign faxes to transcribers group
            //await _service.AssignToTranscriberGroup(operationDto);
            //faxes = KatanaDbContext.Set<Fax>().Where(f => f.TranscribersGroupId == parentId)
            //    .ToList();

            ////Checking that the faxes was assigment
            //Assert.NotEmpty(faxes);

            ////Checking that all faxes changed Workflow status to Grouped
            //Assert.DoesNotContain(faxes, f => f.WorkflowStatus != FaxWorkflowStatus.Grouped);

            //operationDto = new OperationsDto() { Id = parentId, DeleteIds = addIds };

            ////Unassign faxes
            //await _service.AssignToTranscriberGroup(operationDto, notify: false);
            //faxes = KatanaDbContext.Set<Fax>().Where(f => f.TranscribersGroupId == parentId)
            //    .ToList();

            ////Checking that the faxes was unassigment
            //Assert.Empty(faxes);

            ////Checking that all faxes changed Workflow status back to previous status
            //Assert.DoesNotContain(faxes, f => f.WorkflowStatus != FaxWorkflowStatus.New);
        }

        /// <summary>
        /// Test run to AssignToTranscriber method
        /// </summary>
        [Fact]
        public async Task TestAssignToTranscriber()
        {
            var parentId = await Krudder.Set<Transcriber>().Select(g => g.Id)
                .FirstOrDefaultAsync();
            var faxes = Krudder.Set<Fax>()
                .Where(f => f.WorkflowStatus == FaxWorkflowStatus.New).ToList();

            //Ensuring that there is a fax with WorkflowStatus equal to New
            if (faxes.Count == 0)
            {
                var entityDto = await _entityFakeGenerator.GenerateAdd(Krudder);
                await _service.Add(entityDto);
                faxes = [.. Krudder.Set<Fax>().Where(f => f.WorkflowStatus == FaxWorkflowStatus.New)];
            }

            //    var addIds = faxes.Select(f => f.Id).ToList();
            //    var operationDto = new OperationsDto() { Id = parentId, AddIds = addIds };

            //    //Assign faxes to transcriber
            //    await _service.AssignToTranscriber(operationDto, notify: false);
            //    faxes = KatanaDbContext.Set<Fax>().Where(f => f.TranscriberId == parentId).ToList();

            //    //Checking that the faxes was assigment
            //    Assert.NotEmpty(faxes);

            //    //Checking that all faxes changed Workflow status to Assigned
            //    Assert.DoesNotContain(faxes, f => f.WorkflowStatus != FaxWorkflowStatus.Assigned);

            //    operationDto = new OperationsDto() { Id = parentId, DeleteIds = addIds };

            //    //Unassign faxes
            //    await _service.AssignToTranscriber(operationDto, notify: false);
            //    faxes = KatanaDbContext.Set<Fax>().Where(f => f.TranscriberId == parentId).ToList();

            //    //Checking that the faxes was unassigment
            //    Assert.Empty(faxes);

            //    //Checking that all faxes changed Workflow status back to previous status
            //    Assert.DoesNotContain(faxes, f => f.WorkflowStatus != FaxWorkflowStatus.New);
        }

        ///// <summary>
        ///// Test run to Process method
        ///// </summary>
        //[Fact]
        //public async Task TestSendBackFax()
        //{
        //    var faxId = await KatanaDbContext.Set<Fax>().Select(f => f.Id).FirstOrDefaultAsync();

        //    var dto = new BaseUpdateDto()
        //    {
        //        Id = faxId,
        //    };
        //    var dtosArr = new BaseUpdateDto[] { dto };

        //    var entities = await _service.SendBackFax(dtosArr);

        //    Assert.Equal(entities, dtosArr.Length);
        //}

        #endregion
    }
}
