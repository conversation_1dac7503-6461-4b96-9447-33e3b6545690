import { Display, MUID<PERSON>TableColumn, MUIDataTableColumnOptions, MUISortOptions } from "mui-datatables";
import { EntityUseQueryAutocompleteProps } from "Components/Common/Autocomplete/types";
import { PagingOptions } from "../katanaApiClient/types";
import { KTableAction } from "./muiDataTableUtils";
import { InputBaseComponentProps } from "@mui/material";

/**
 * Allowed values for Input type of filters
 *
 * Values for "KMUIDataTableInputFilterType", "KMUIDataTableSelectFilterType" (Ex. client side autocomplete) and
 * "KMUIDataTableServerSideSelectFilterType" (Ex. server side autocomplete) are in
 * separated types so we can validate the filter options for each of them (they are different)
 */
export enum KMUIDataTableInputFilterType {
    NumberRange = 1,
    DateRange = 2,
    Contains = 3,
    Equals = 4,
}

/**
 * MUIDataTable Katana Select Filter Type
 *
 * Values for "KMUIDataTableInputFilterType", "KMUIDataTableSelectFilterType" (Ex. client side autocomplete) and
 * "KMUIDataTableServerSideSelectFilterType" (Ex. server side autocomplete) are in
 * separated types so we can validate the filter options for each of them (they are different)
 */
export enum KMUIDataTableSelectFilterType {
    Autocomplete = 5,
    RadioButtons = 8,
    Checkboxes = 9,
}

/**
 * MUIDataTable Katana Select Filter Type
 *
 * Values for "KMUIDataTableInputFilterType", "KMUIDataTableSelectFilterType" (Ex. client side autocomplete) and
 * "KMUIDataTableServerSideSelectFilterType" (Ex. server side autocomplete) are in
 * separated types so we can validate the filter options for each of them (they are different)
 */
export enum KMUIDataTableYesNoFilterType {
    YesNo = 6,
}

/**
 * MUIDataTable Katana Server side autocomplete Filter Type
 *
 * Values for "KMUIDataTableInputFilterType", "KMUIDataTableSelectFilterType" (Ex. client side autocomplete) and
 * "KMUIDataTableServerSideSelectFilterType" (Ex. server side autocomplete) are in
 * separated types so we can validate the filter options for each of them (they are different)
 */
export enum KMUIDataTableServerSideSelectFilterType {
    ServerSideAutocomplete = 7,
}

/**
 * MUIDataTable Katana Columns filter details (type and values)
 */
export interface KMUIDataTableFilterDetail {
    type:
        | KMUIDataTableInputFilterType
        | KMUIDataTableSelectFilterType
        | KMUIDataTableYesNoFilterType
        | KMUIDataTableServerSideSelectFilterType;
    values: string[];
    customFilter?: (name: string, values: string[]) => string[];
}

/**
 * MUIDataTable Katana Columns filters. Used for constructing Sieve filters option
 */
export interface KMUIDataTableFilters {
    [name: string]: KMUIDataTableFilterDetail;
}

/**
 * MUIDataTable Katana State PagingOptions (to be used as parameter for usePagingOptionsState). Does not allows the use of sorts and filters
 * as options since these two are built based on sortOrder and filtersList
 */
export interface KMUIDataTableInitPagingOptions {
    page?: number;
    pageSize?: number;
    expands?: string;
    sortOrder?: MUISortOptions;
    filtersList?: KMUIDataTableFilters;
}

/**
 * MUIDataTable Katana PagingOptions
 */
export interface KMUIDataTablePagingOptions extends PagingOptions {
    sortOrder?: MUISortOptions;
    filtersList?: KMUIDataTableFilters;
    displayColumns?: { [name: string]: Display | undefined };
    location: string;
}

/**
 * Column's export metadata
 */
export interface ExportMetadata {
    dateFormat?: string;
    offset?: number;
    columnName?: string;
}

/**
 * Extending MUIDataTableColumn with kFilterType option
 */
export interface KMUIDataTableColumn extends MUIDataTableColumn {
    filterType?:
        | KMUIDataTableInputFilterType
        | KMUIDataTableSelectFilterType
        | KMUIDataTableYesNoFilterType
        | KMUIDataTableServerSideSelectFilterType;
    customFilter?: (name: string, values: string[]) => string[];
    sticky?: number;
    exportMetadata?: ExportMetadata;
    customSortMethodName?: string;
}

/**
 * Options for Filterable Columns
 */
export interface KMUIDataTableFilterColumnOptions {
    /**
     * pagingOptions state variable
     */
    pagingOptions: KMUIDataTablePagingOptions;
    /**
     * Filter label (wins over column's label). Column's label will be used when not specified
     */
    filterLabel?: string;
    /**
     * When specified will define wether the filter is visible or not. Parameter "display" will be used when not specified
     */
    filterDisplay?: boolean;
    /**
     * Function for customizing filter value. Return value will be sent as is in the Sieve "filters" parameter
     */
    customFilter?: (name: string, values: string[]) => string[];
    /**
     * Will force a filter option to take up the grid's full width.
     */
    fullWidth?: boolean;

    /**
     * List of fields to search from
     */
    searchFields?: string[];
}

/**
 * Options for Filterable Columns type input (contains, number range and date range)
 */
export interface KMUIDataTableInputFilterColumnOptions extends KMUIDataTableFilterColumnOptions {
    filterType: KMUIDataTableInputFilterType;
    /**
     * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the left `input` element.
     *
     * @default {}
     */
    inputLeftProps?: InputBaseComponentProps;
    /**
     * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the right `input` element.
     *
     * @default {}
     */
    inputRightProps?: InputBaseComponentProps;
}

/**
 * Options for Filterable Columns type select (multiselect and client side autocomplete)
 */
export interface KMUIDataTableSelectFilterColumnOptions extends KMUIDataTableFilterColumnOptions {
    /** Filter type. The allowed values for this property are in "KMUIDataTableSelectFilterType" type */
    filterType: KMUIDataTableSelectFilterType;

    /**  Options for the selectable list */
    options?: string[];

    /**  Whether allows multiple selection or not. Default value is "true" */
    multiple?: boolean;
}

/**
 * Options for Filterable Columns type select YES/ NO
 */
export interface KMUIDataTableYesNoFilterColumnOptions extends KMUIDataTableFilterColumnOptions {
    /** Filter type. The allowed values for this property are in "KMUIDataTableSelectFilterType" type */
    filterType: KMUIDataTableYesNoFilterType;

    /**Whether to show values as True/ False instead of Yes/ No */
    trueFalseValues?: boolean;
}

/**
 * Options for Filterable Columns type server side select)
 */
export interface KMUIDataTableServerSideSelectFilterColumnOptions
    extends KMUIDataTableFilterColumnOptions,
        EntityUseQueryAutocompleteProps {
    /** Filter type. The allowed values for this property are in "KMUIDataTableServerSideAutompleteFilterType" type */
    filterType: KMUIDataTableServerSideSelectFilterType;
}

/**
 * Katana MUIDataTableColumnOptions
 */
export interface KMUIDataTableColumnOptions {
    /**
     * Column name
     */
    name: string;
    /**
     * Column label
     */
    label: string;
    /**
     * Options for Filterable Columns
     */
    filterOptions?:
        | KMUIDataTableInputFilterColumnOptions
        | KMUIDataTableSelectFilterColumnOptions
        | KMUIDataTableYesNoFilterColumnOptions
        | KMUIDataTableServerSideSelectFilterColumnOptions;
    /**
     * Sort enabled/ disabled
     */
    sort?: boolean;

    /**
     * Name of the SieveCustomSort method implemented
     */
    customSortMethodName?: string;

    /**
     * Columns options
     */
    options?: MUIDataTableColumnOptions;
    /**
     * Whether
     */
    sticky?: number;
    /**
     * Export Metadata
     */
    exportMetadata?: ExportMetadata;
}

/**
 * MUIDataTableSelectRows type
 *
 * Used as alias type for batch operations on MUIDataTable components
 */
export interface MUIDataTableSelectRows {
    data: Array<{ index: number; dataIndex: number }>;
    lookup: { [key: number]: boolean };
}

/**
 * KFilterWidget Props
 */
export interface KFilterWidgetProps {
    /** Filter list */
    filterList: string[][];

    /** onChange function */
    onChange: any;

    /** Filter index */
    index: number;

    /** Column */
    column: MUIDataTableColumn;

    /** Filter lable */
    filterLabel?: string;

    /** Input Props */
    inputsProps?: {
        inputLeftProps?: InputBaseComponentProps;
        inputRightProps?: InputBaseComponentProps;
    };
}

export interface KContainsFilterWidgetProps extends KFilterWidgetProps {
    /** Search fields */
    searchFields?: string[];
}

/**
 * KAutocompleteFilterWidget Props
 */
export interface KAutocompleteFilterWidgetProps extends KFilterWidgetProps {
    /**
     * Options values
     */
    options: string[];
    /**
     * Whether allows multiple selection or not
     */
    multiple?: boolean;
}

/**
 * KRadioButtonsFilter Widget Props
 */
export interface KRadioButtonsFilterWidgetProps extends KFilterWidgetProps {
    /**
     * Options values
     */
    options: string[];
}

/**
 * KCheckboxesFilterWidget Props
 */
export interface KCheckboxesFilterWidgetProps extends KFilterWidgetProps {
    /**
     * Options values
     */
    options: string[];
}

export interface KServerSideAutocompleteFilterWidgetProps
    extends KFilterWidgetProps,
        EntityUseQueryAutocompleteProps {
    /**
     * Helper text
     */
    helperText?: string;
}

export interface KActionsProps<TDto> {
    /** Get actions by row */
    getActions: (entity: TDto, selectedRows: TDto[]) => KTableAction[];

    /** Column Title */
    columnTitle?: string;

    /** Column Name */
    columnName?: string;
}

export enum stickyColumnsWidth {
    ActionsDefault = 17,
    Id = 17,
    Lock = 17,
    DateToExpire = 28,
    Expired = 16,
    RequestDate = 34,
    WorkflowStatus = 34,
    ValidationStatus = 34,
    ValidationErrorsCount = 26,
    Status = 25,
    ProcessingType = 30,
    CareplanId = 18,
    Name = 40,
    Method = 30,
    FileName = 40,
    ImportDate = 30,
    InvoiceId = 24,
    IgnoreErrors = 26,
    DoNotBill = 24,
    Duplicated = 18,
    Priority = 30,
    RequestedByGP = 15,
    RoleName = 30,
}
